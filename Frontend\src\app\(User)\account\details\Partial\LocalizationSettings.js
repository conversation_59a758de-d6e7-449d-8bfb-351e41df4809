'use client';
import React, { useMemo, useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import { EditIconSvg, SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CustomDropdown from '@/Components/common/CustumDropdown';
import StatusIndicator from '@/Components/UI/StatusIndicator';
import { getTimezonesForDropdown } from '@/utils/getTimezonesForDropdown';
import CommonTooltip from '@/Components/UI/CommonTooltip';
import { get, put } from '@/utils/apiUtils';
import { setUser } from '@/redux/authSlice';

export default function LocalizationSettings() {
    // Use the same timezone options as portfolio manager for consistency
    const timezoneOptions = useMemo(() => {
        return getTimezonesForDropdown();
    }, []);

    // Updated to match backend configuration
    const languages = [
        { label: 'English', value: 'en' },
        { label: 'French', value: 'fr' },
        { label: 'German', value: 'de' },
        { label: 'Spanish', value: 'es' },
        { label: 'Italian', value: 'it' },
        { label: 'Portuguese', value: 'pt' },
        { label: 'Russian', value: 'ru' },
        { label: 'Arabic', value: 'ar' },
        { label: 'Chinese', value: 'zh' },
        { label: 'Japanese', value: 'ja' },
        { label: 'Korean', value: 'ko' },
        { label: 'Urdu', value: 'ur' },
    ];

    // Updated to match backend configuration
    const currencies = [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'GBP', value: 'GBP' },
        { label: 'PKR', value: 'PKR' },
        { label: 'AUD', value: 'AUD' },
        { label: 'CAD', value: 'CAD' },
        { label: 'JPY', value: 'JPY' },
        { label: 'CNY', value: 'CNY' },
        { label: 'INR', value: 'INR' },
        { label: 'CHF', value: 'CHF' },
        { label: 'AED', value: 'AED' },
        { label: 'SAR', value: 'SAR' },
        { label: 'KWD', value: 'KWD' },
        { label: 'BHD', value: 'BHD' },
        { label: 'QAR', value: 'QAR' },
        { label: 'MYR', value: 'MYR' },
        { label: 'SGD', value: 'SGD' },
        { label: 'THB', value: 'THB' },
        { label: 'HKD', value: 'HKD' },
    ];

    // Updated to match backend configuration
    const numberFormatOptions = [
        { label: 'Dot Separator (1,234.56)', value: '1,234.56' },
        { label: 'Comma Separator (1.234,56)', value: '1.234,56' },
        { label: 'No Separator (1234.56)', value: '1234.56' },
    ];

    const [isEditing, setIsEditing] = useState(false);
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);
    const [saveStatus, setSaveStatus] = useState(null);

    // Temporary state for editing
    const [tempLanguage, setTempLanguage] = useState('');
    const [tempTimezone, setTempTimezone] = useState('');
    const [tempCurrency, setTempCurrency] = useState('');
    const [tempNumberFormat, setTempNumberFormat] = useState('');

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const controller = new AbortController();
            const response = await get('/account', {}, { signal: controller.signal });

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    // Load user data on component mount
    useEffect(() => {
        fetchUserData();
    }, []);

    // Update temp values when userData changes
    useEffect(() => {
        if (userData) {
            setTempLanguage(userData.language || '');
            setTempTimezone(userData.timezone || '');
            setTempCurrency(userData.currency || '');
            setTempNumberFormat(userData.number_format || '');
        }
    }, [userData]);

    // Helper functions to get display labels
    const getLanguageLabel = (value) => {
        const lang = languages.find(l => l.value === value);
        return lang ? lang.label : value || 'Not set';
    };

    const getTimezoneLabel = (value) => {
        const tz = timezoneOptions.find(t => t.value === value);
        return tz ? tz.label : value || 'Not set';
    };

    const getCurrencyLabel = (value) => {
        const curr = currencies.find(c => c.value === value);
        return curr ? curr.label : value || 'Not set';
    };

    const getNumberFormatLabel = (value) => {
        const format = numberFormatOptions.find(f => f.value === value);
        return format ? format.label : value || 'Not set';
    };

    const handleEditClick = () => {
        if (userData) {
            setTempLanguage(userData.language || '');
            setTempTimezone(userData.timezone || '');
            setTempCurrency(userData.currency || '');
            setTempNumberFormat(userData.number_format || '');
        }
        setIsEditing(true);
    };

    const handleSave = async () => {
        if (!userData) {
            setSaveStatus('error');
            setError('User data not available');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setSaving(true);
            setSaveStatus('loading');
            setError(null);

            const updateData = {
                language: tempLanguage || null,
                timezone: tempTimezone || null,
                currency: tempCurrency || null,
                number_format: tempNumberFormat || null
            };

            const response = await put(`/account/update/${userData.id}`, updateData);

            if (response.message) {
                // Update local state with the fresh user data from response
                if (response.user) {
                    setUserData(response.user);
                    // Update Redux store
                    dispatch(setUser(response.user));
                    // Update localStorage
                    localStorage.setItem('user', JSON.stringify(response.user));
                }

                setSaveStatus('success');
                setIsEditing(false);
            } else {
                throw new Error('Failed to update localization settings');
            }
        } catch (err) {
            console.error('Error updating localization settings:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to update localization settings';
            setSaveStatus('error');
            setError(errorMessage);
        } finally {
            setSaving(false);
            setTimeout(() => setSaveStatus(null), 3000);
        }
    };

    const handleCancel = () => {
        setIsEditing(false);
        if (userData) {
            setTempLanguage(userData.language || '');
            setTempTimezone(userData.timezone || '');
            setTempCurrency(userData.currency || '');
            setTempNumberFormat(userData.number_format || '');
        }
        setError(null);
    };



    // Show loading state
    if (loading) {
        return (
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Localization Settings</h6>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <p>Loading...</p>
                        </div>
                    </div>
                </div>
            </Col>
        );
    }

    // Show error state if no data and error exists
    if (!userData && error) {
        return (
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Localization Settings</h6>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <p style={{ color: 'red' }}>Error: {error}</p>
                            <button className="btn-style" onClick={fetchUserData}>
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            </Col>
        );
    }

    return (
        <Col lg={12} xs={12} className="mb-3 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <div className="account_header_main">
                            <h6>Localization Settings</h6>
                            <div className="account_status_indicator">
                                <StatusIndicator
                                    saveStatus={saveStatus}
                                    error={error}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="common_blackcard_innerheader_icon">
                        {!isEditing && (
                            <button
                                className="d-flex align-items-center"
                                onClick={handleEditClick}
                                disabled={saving || loading}
                            >
                                <EditIconSvg />
                                <span className="ms-2">Update</span>
                            </button>
                        )}
                    </div>
                </div>
                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <ul>
                            {/* Language */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Language</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        key={`language-${tempLanguage || 'none'}`}
                                                        options={languages}
                                                        defaultValue={getLanguageLabel(tempLanguage)}
                                                        onSelect={(val) => setTempLanguage(val.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{getLanguageLabel(userData?.language)}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Timezone */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Timezone</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        key={`timezone-${tempTimezone || 'none'}`}
                                                        options={timezoneOptions}
                                                        defaultValue={getTimezoneLabel(tempTimezone)}
                                                        onSelect={(val) => setTempTimezone(val.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{getTimezoneLabel(userData?.timezone)}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Currency */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Currency</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        key={`currency-${tempCurrency || 'none'}`}
                                                        options={currencies}
                                                        defaultValue={getCurrencyLabel(tempCurrency)}
                                                        onSelect={(val) => setTempCurrency(val.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{getCurrencyLabel(userData?.currency)}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Number Format Display */}
                            <li>
                                <Col xs={12} md={3}>
                                    <div className='d-flex align-items-center gap-1'>
                                        <span className='pe-0'>Number Format Display</span>
                                        <CommonTooltip
                                            className="CustomTooltip"
                                            content={
                                                <>
                                                    <p className='mb-2'>
                                                        Choose how numbers—such as prices, metrics, and marketplace values—are displayed across your account. This setting controls <span className='fw-800 width-autofit pe-0'>visual formatting only</span>, not how numbers are calculated or stored.
                                                    </p>
                                                    <p className='mb-1'>• <span className='fw-800 width-autofit pe-0'>Dot Separator:</span> uses commas for thousands and a dot for decimals</p>
                                                    <p className='mb-2'>• <span className='fw-800 width-autofit pe-0'>Comma Separator:</span> uses dots for thousands and a comma for decimals</p>
                                                    <p className='mb-2'>
                                                        This setting applies globally to all account-level and marketplace pages. Individual Trade Accounts may define their own display preference, but it only affects how numbers appear <span className='fw-800 width-autofit pe-0'>within the dashboard experience.</span>
                                                    </p>
                                                    <p className='mb-2'>
                                                        When entering numbers, use only digits and the correct decimal separator. Thousands separators are added automatically and cannot be typed manually.
                                                    </p>
                                                </>
                                            }
                                            position="top-right"
                                        >
                                            <SolidInfoIcon />
                                        </CommonTooltip>
                                    </div>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        key={`number-format-${tempNumberFormat || 'none'}`}
                                                        options={numberFormatOptions}
                                                        defaultValue={getNumberFormatLabel(tempNumberFormat)}
                                                        onSelect={(val) => setTempNumberFormat(val.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{getNumberFormatLabel(userData?.number_format)}</span>
                                    )}
                                </Col>
                            </li>
                        </ul>

                        {isEditing && (
                            <div className="account_card_list_btns">
                                <button
                                    className="btn-style"
                                    onClick={handleSave}
                                    disabled={saving}
                                >
                                    {saving ? 'Saving...' : 'Save'}
                                </button>
                                <button
                                    className="btn-style gray-btn"
                                    onClick={handleCancel}
                                    disabled={saving}
                                >
                                    Cancel
                                </button>
                            </div>
                        )}

                        {error && !isEditing && (
                            <div className="mt-3">
                                <p style={{ color: 'red', fontSize: '14px' }}>
                                    {error}
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col >
    );
}

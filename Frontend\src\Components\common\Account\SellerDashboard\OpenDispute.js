"use client";
import { Col, Row } from "react-bootstrap";
import React, { useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { DropArrowIcon, DropArrowUpIcon } from "@/assets/svgIcons/SvgIcon";
import CommonButton from "@/Components/UI/CommonButton";

export default function OpenDispute() {
  const [isDisputeExp, setIsDisputeExp] = useState(null);
  const disputeArray = [
    {
      buyer: "@Wakas",
      product: "Stock Indicators Course 101",
      orderId: "29311",
      dispute: "Not what I expected",
      reason: "Mar 24, 2025",
      openedOn: "Waiting on Seller",
      respondBy: "Mar 24, 2025",
    },
    {
      buyer: "@Wakas",
      product: "Stock Indicators Course 101",
      orderId: "29311",
      dispute: "Not what I expected",
      reason: "Mar 24, 2025",
      openedOn: "Waiting on Seller",
      respondBy: "Mar 24, 2025",
    },
  ];
  return (
    <>
      <CommonWhiteCard title="Open Disputes (3) " className="account_card">
        <div className="account_card_disputes">
          {/* Table View (shown only on md and up) */}
          <div className="d-none d-md-block lg_screen_table">
            <table className="table">
              <thead>
                <tr>
                  <th>
                    <div className="th-inner">Buyer</div>
                  </th>
                  <th>
                    <div className="th-inner">Product Name</div>
                  </th>
                  <th>
                    <div className="th-inner">Order ID</div>
                  </th>
                  <th>
                    <div className="th-inner">Dispute Reason</div>
                  </th>
                  <th>
                    <div className="th-inner">Opened on</div>
                  </th>
                  <th>
                    <div className="th-inner">Status</div>
                  </th>
                  <th>
                    <div className="th-inner">Respond By</div>
                  </th>
                  <th>
                    <div className="th-inner">Actions</div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {disputeArray.map((item, index) => (
                  <tr key={index}>
                    <td>{item.buyer}</td>
                    <td>{item.product}</td>
                    <td>{item.orderId}</td>
                    <td>{item.dispute}</td>
                    <td>{item.reason}</td>
                    <td>{item.openedOn}</td>
                    <td>{item.respondBy}</td>
                    <td className="px-0">
                      <CommonButton
                        title=" View / Respond"
                        className="view_res_btn"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Card View (shown only on small screens) */}
          <div className="d-block d-md-none sm_screen_table">
            {disputeArray.map((item, index) => (
              <div className="wrap-div" key={index}>
                <Row className="mb-2 px-2">
                  <div className="colunm_head">
                    <Col xs>Buyer</Col>
                    <Col xs>Product Name</Col>
                    <Col xs>Status</Col>
                    <Col xs="auto">
                      <div className="arrow-header"></div>
                    </Col>
                  </div>

                  <Col className="colunm_value" xs>
                    {item.buyer}
                  </Col>
                  <Col className="colunm_value" xs>
                    {item.product}
                  </Col>
                  <Col className="colunm_value" xs>
                    {item.openedOn}
                  </Col>
                  <Col className="colunm_value m-auto" xs="auto">
                    <div
                      className="account_card_btnArrow"
                      onClick={() =>
                        setIsDisputeExp((prev) =>
                          prev !== index ? index : null
                        )
                      }
                    >
                      {isDisputeExp == index ? (
                        <DropArrowUpIcon />
                      ) : (
                        <DropArrowIcon />
                      )}
                    </div>
                  </Col>
                </Row>
                {isDisputeExp == index && (
                  <>
                    <Row className="mb-2 px-2">
                      <div className="colunm_head">
                        <Col xs={4}>Order ID</Col>
                        <Col xs={4}>Dispute Reason</Col>
                        <Col xs={4}>Opened on</Col>
                      </div>

                      <Col xs={4} className="colunm_value">
                        {item.orderId}
                      </Col>
                      <Col xs={4} className="colunm_value">
                        {item.dispute}
                      </Col>
                      <Col xs={4} className="colunm_value">
                        {item.reason}
                      </Col>
                    </Row>
                    <Row className="mb-2 px-2">
                      <div className="colunm_head">
                        <Col xs={12}>Respond By</Col>
                      </div>
                      <Col xs={12} className="colunm_value">
                        {item.respondBy}
                      </Col>
                    </Row>
                    <Row className="mb-2 px-2">
                      <div className="colunm_head">
                        <Col xs={12}>Actions</Col>
                      </div>
                      <CommonButton
                        title="View / Respond"
                        className="view_res_btn w-100"
                      />
                    </Row>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      </CommonWhiteCard>
    </>
  );
}

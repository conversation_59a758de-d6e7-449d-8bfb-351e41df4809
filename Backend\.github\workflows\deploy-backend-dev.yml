
name: Deploy backend dev to ec2


on:
 pull_request:
    branches:
      - dev
 push:
   branches:
     - dev


jobs:
  deploy:
     runs-on: ubuntu-latest
     env:
       AWS_REGION: us-east-1
     
      # 1. Checkout the repository
      
     steps:
     - name: Checkout Code
       uses: actions/checkout@v3
      
      #2. Configuring Aws Credentials
      
     - name: Configure Aws Credentials
       uses: aws-actions/configure-aws-credentials@v2
       with:
         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_DEV }}
         aws-secret-access-key: ${{  secrets.AWS_SECRET_ACCESS_KEY_DEV }}
         aws-region: ${{ env.AWS_REGION }}
         
      # 3. Setting up ssh keys
      
     - name: Setup ssh key
       run: |
        echo "${{secrets.EC2_KEY_BACKEND_DEV}}" |base64 -d > ec2_key.pem
        chmod 600 ec2_key.pem

      # 4. Deploy to ec2 using ssh

     - name: Deploy ON ec2 using ssh
       uses: appleboy/ssh-action@master
       with:
        host: ${{secrets.EC2_HOST_BACKEND_DEV}}
        username: ubuntu
        key_path: ec2_key.pem
        script: |
        
          cd /home/<USER>/backend
          git reset --hard
          git checkout  dev 
          git pull origin dev 
          composer install 
          php artisan migrate
          sudo systemctl restart laravel-worker.service
      
       # 5. Clean up the SSH key after deployment
     - name: Clean up the SSH key
       if: always()
       run: |
         shred -u ec2_key.pem

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SecurityVerificationAttempt extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'attempted_code',
        'successful',
        'failure_reason',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'successful' => 'boolean',
    ];

    /**
     * Get the user that made the attempt
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the verification session this attempt belongs to
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(SecurityVerificationSession::class, 'session_id', 'session_id');
    }

    /**
     * Scope to get failed attempts
     */
    public function scopeFailed($query)
    {
        return $query->where('successful', false);
    }

    /**
     * Scope to get successful attempts
     */
    public function scopeSuccessful($query)
    {
        return $query->where('successful', true);
    }

    /**
     * Scope to get recent attempts within specified minutes
     */
    public function scopeRecent($query, int $minutes = 15)
    {
        return $query->where('created_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Check if user has exceeded global rate limit
     */
    public static function hasUserExceededGlobalRateLimit(int $userId): bool
    {
        $maxAttempts = config('security.session.max_attempts', 5);
        $lockoutMinutes = config('security.session.lockout_minutes', 15);

        $recentFailedAttempts = static::where('user_id', $userId)
            ->failed()
            ->recent($lockoutMinutes)
            ->count();

        return $recentFailedAttempts >= $maxAttempts;
    }

    /**
     * Get time until user can attempt again (in minutes)
     */
    public static function getTimeUntilUserCanAttemptAgain(int $userId): ?int
    {
        $lockoutMinutes = config('security.session.lockout_minutes', 15);
        
        $oldestRecentAttempt = static::where('user_id', $userId)
            ->failed()
            ->recent($lockoutMinutes)
            ->orderBy('created_at')
            ->first();

        if (!$oldestRecentAttempt) {
            return null; // No recent attempts, can attempt now
        }

        $unlockTime = $oldestRecentAttempt->created_at->addMinutes($lockoutMinutes);
        
        if ($unlockTime->isPast()) {
            return null; // Lockout period has passed
        }

        return $unlockTime->diffInMinutes(now());
    }

    /**
     * Record a verification attempt
     */
    public static function recordAttempt(
        int $userId,
        string $sessionId,
        ?string $attemptedCode,
        bool $successful,
        ?string $failureReason = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return static::create([
            'user_id' => $userId,
            'session_id' => $sessionId,
            'attempted_code' => $attemptedCode,
            'successful' => $successful,
            'failure_reason' => $failureReason,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);
    }

    /**
     * Clean up old attempts (for scheduled cleanup)
     */
    public static function cleanupOldAttempts(int $daysOld = 30): int
    {
        return static::where('created_at', '<', now()->subDays($daysOld))->delete();
    }
}

# TradeReply Frontend Repository

This repository is for the **Frontend Environment** for TradeReply, an advanced analytics suite for crypto and stock traders.

[![Development https://dev.tradereply.com](https://img.shields.io/badge/Development-https%3A%2F%2Fdev.tradereply.com-blue?style=for-the-badge)](https://dev.tradereply.com)

[![Production https://tradereply.com](https://img.shields.io/badge/Production-https%3A%2F%2Ftradereply.com-blue?style=for-the-badge)](https://tradereply.com)

[![Deprecated https://static.tradereply.com](https://img.shields.io/badge/Deprecated-https%3A%2F%2Fstatic.tradereply.com-blue?style=for-the-badge)](https://static.tradereply.com)

## 🔄 Deployment & Branch Strategy

TradeReply is divided into two repositories:
- **Frontend Repository** (`TradeReply/frontend`) → React-based user interface.
- **Backend Repository** (`TradeReply/backend`) → Laravel API and server-side logic.

Each repository follows a **branch-based deployment strategy**:

- `main` → **Production branch** (deployed to `tradereply.com`)
- `dev` → **Development branch** (deployed to `dev.tradereply.com`)

Each branch deploys automatically to its respective AWS EC2 instance.

### Deployment Process:
1. Push changes to `dev` branch → **Deploys to `dev.tradereply.com`**
2. Merge `dev` into `main` → **Deploys to `tradereply.com`**

---

## 🔗 References

- **Laravel Documentation**: Follow best practices and guidelines provided in the [Laravel Official Documentation](https://laravel.com/docs).
- **Next.js Documentation**: Follow best practices and guidelines provided in the [Next.js Official Documentation](https://nextjs.org/docs).

---

## 📘 Purpose

- Maintain a structured development workflow using separate repositories for frontend and backend.
- Use `dev` for staging and testing before merging into `main`.
- Deploy both frontend and backend to separate AWS EC2 instances per environment.

---

## 🚀 Tech Stack

- **Quick Overview**: Public pages use **Next.js SSR for SEO**, while authenticated pages use **Next.js CSR with API-driven data fetching**. Laravel provides the backend, and MySQL is the database, all hosted on AWS.
- **Frontend**: Next.js (built on React) with SCSS for building interactive user interfaces, styled with TailwindCSS and Sass for maintainable, modular CSS. Next.js optimizes rendering and prefetching for better performance.
- **Rendering Strategy**:
  - **Public Pages**: Server-Side Rendering (**SSR**) with Next.js for **SEO-friendly, pre-rendered content**.
  - **Authenticated Pages**: Client-Side Rendering (**CSR**) with Next.js, fetching data via **optimized Laravel APIs** for a fast, dynamic experience.
- **Metadata Handling**: Next.js **automatically injects SEO metadata** via `next/head` for SSR pages, and updates dynamically for CSR pages.
- **Backend**: Laravel for robust API development and server-side logic, handling **database operations, authentication, and API responses** for Next.js.
- **Database**: MySQL for relational data management.
- **Build Tools**: Next.js provides optimized builds with **automatic static generation (SSG), incremental static regeneration (ISR), and hybrid rendering**.
- **UI Libraries**: Bootstrap and React-Bootstrap for prebuilt components, with additional enhancements from React-Select, React-Datepicker, and React-Slick for advanced UI functionality.
- **Hosting**: AWS (EC2 T3 small (64-bit x86) using Apache Web Server on Ubuntu Server for compute, RDS for database, S3 for storage).
- **Validation**: Yup for schema-based form validation, often paired with Next.js forms for robust input handling.
- **HTTP Requests**: 
  - `fetch` is used inside `getServerSideProps` for **SSR API calls**.
  - Axios is used for **client-side API interactions** in authenticated pages, handling secure requests and dynamic updates efficiently.

---

## 🗂️ Repository Structure Instructions

To align with deployment requirements, the repositories must be structured as follows:

### **Frontend Repository (`frontend`)**
- Contains all Next.js pages (`/pages/`), SCSS files, images, fonts, and frontend-specific assets.
- Uses Next.js **built-in routing** instead of React Router.
- Includes **SSR (`getServerSideProps`) for public pages** and **CSR for authenticated pages**.
- Uses `next/head` for metadata handling and SEO optimizations.
- **Built assets remain within the frontend repo and do not output to the backend.**

### **Backend Repository (`backend`)**
- Includes all Laravel framework files, such as `app/`, `config/`, `routes/`, and `database/` for backend logic.
- Handles **API requests** and **secure data processing** for the Next.js frontend.
- Manages authentication and session handling via **Laravel Sanctum or API tokens**.
- Contains `resources/` for any backend views if needed.
- **Does not serve frontend assets**—Next.js handles all UI rendering independently.

---

## 💂️ Backend Environment Setup

### Clone the Repository
```bash
git clone https://github.com/TradeReply/backend.git
cd backend
```

### Install Dependencies
```bash
composer install
```

### Set Up `.env` File
```bash
cp .env.example .env
```

### Generate the Laravel Application Key
```bash
php artisan key:generate
```

### Set Up the Database
1. Configure the `.env` file with development settings.
2. Ensure MySQL is running, then run database migrations:
   ```bash
   php artisan migrate
   ```

### Start the Backend Development Server
```bash
php artisan serve
```

---

## 📚 Frontend Environment Setup

### Clone the Repository
```bash
git clone https://github.com/TradeReply/frontend.git
cd frontend
```

### Install Dependencies
```bash
npm install
```

### Set Up `.env` File
```bash
cp .env.example .env
```

### Start the Frontend Development Server (Next.js)
```bash
npm run dev
```

---

## 🔧 Essential Development Commands

### Laravel Commands
Here are common Laravel commands developers should know:

```bash
# Clear Cache
php artisan cache:clear

# Clear Config Cache
php artisan config:clear

# Clear Route Cache
php artisan route:clear

# Clear View Cache
php artisan view:clear

# Check All Routes
php artisan route:list

# Run Unit Tests
php artisan test

# Rollback Migrations
php artisan migrate:rollback
```

### Frontend Commands
Common commands for working with Vite and npm:

```bash
# Start Vite Development Server
npm run dev

# Build Assets for Production (if needed for testing builds)
npm run build

# Lint and Fix SCSS or JS (if configured)
npm run lint
```

### Debugging and Logs
- Enable **debugging** in `.env`:
  ```dotenv
  APP_DEBUG=true
  ```
- View logs directly from the terminal:
  ```bash
  tail -f storage/logs/laravel.log
  ```

### Database Seeding
If you need sample data for testing:

```bash
# Run Database Seeders
php artisan db:seed

# Reset and Re-seed the Database
php artisan migrate:fresh --seed
```

---

## 🌐 API and Webhook Testing Tools
To test APIs or webhooks during development, you can use the following tools:

### **API Testing**
1. **Postman** – Comprehensive API testing tool to validate Laravel API endpoints, simulate frontend-to-backend requests, and test authentication.
2. **Insomnia** – A lightweight and flexible alternative to Postman for rapid API testing.
3. **Hoppscotch** – A web-based API testing tool that offers an intuitive interface for testing REST and GraphQL APIs.
4. **VS Code REST Client** – Allows developers to write and test API requests directly in `.http` or `.rest` files within VS Code.

### **Webhook Testing**
1. **Ngrok** – Exposes your local Laravel server to the internet with a public URL for webhook testing and external API integration.
2. **Expose** – A self-hosted alternative to Ngrok for persistent and private tunnels.
3. **RequestBin** – Captures and inspects webhook payloads for debugging and development purposes.
4. **Webhook.site** – Provides real-time monitoring and debugging for incoming webhooks from external services.

---

## ⚙️ Collaboration Guidelines

- Use **GitHub Issues** to track bugs and feature requests.
- Submit **pull requests** for code reviews before merging into the main branch.
- Ensure proper documentation of all new features or changes.
- Always use comments to explain complex logic or calculations.
- Maintain consistent indentation and formatting.
- Follow naming conventions for variables, functions, and classes.
- Write modular, reusable code.

---

## 📄 File Naming Conventions

To ensure consistency and optimize for SEO, all file names must adhere to the following guidelines:

- **Lowercase Only**: All file names should be written in lowercase letters.
- **Prefix with "tradereply"**: Every file name must start with tradereply for branding and organization.
- **Use Dashes**: Use dashes (-) to separate words in file names. Avoid underscores (_) unless explicitly required by a system.
- **Descriptive Naming**: File names should be clear and descriptive to reflect their purpose.
- Example:
- Correct: tradereply-dashboard-icon.png
- Incorrect: TradeReply_dashboard_icon.png
- By following these conventions, we ensure a consistent, professional file structure across the project.

## ☁️ AWS Resource Naming

To ensure clarity and consistency across AWS resources, follow these naming conventions:

- **Name**: Use descriptive resource names (e.g., `dev-laravel-ec2`, `prod-react-s3`).
- **Environment**: Specify `Dev`, `Prod`, or `All` to indicate the target environment.
- **Purpose**: Include the resource's specific purpose (e.g., `Frontend`, `Backend`).
- **Application**: Always set to `TradeReply`.
- **Owner**: Include the responsible developer or team name.

---

## 🔒 Security Guidelines

- Do not commit sensitive information, such as credentials or API keys.
- Use `.env` files for all environment-specific configurations.
- Follow AWS security policies for deploying frontend and backend services.

---

© 2025 TradeReply LLC. All rights reserved.

Unauthorized copying, modification, or distribution of this software and its content is strictly prohibited without prior written permission from TradeReply LLC.

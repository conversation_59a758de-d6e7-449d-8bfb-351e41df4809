"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { Col, Container, ProgressBar, Row } from "react-bootstrap";
import { useRouter } from "next/navigation";
import debounce from "lodash.debounce";
import CommonSearch from "@/Components/UI/CommonSearch";
import CommonButton from "@/Components/UI/CommonButton";
import CustomBreadcrumb from "@/Components/UI/CustomBreadcrumb";
import Link from "next/link";
import RecentPost from "@/Components/common/Home/RecentPost";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import "@/css/Home/EducationDetail.scss";
import { useParams } from "next/navigation";
import { get, post } from "@/utils/apiUtils";
import { CircularProgressbarWithChildren } from "react-circular-progressbar";
import Cookies from "js-cookie";

export default function EducationContent({
  detailSlug,
  articleData,
  nextArticle,
  avgProgress
}) {
  const [searchKeyword, setSearchKeyword] = useState('');
  const params = useParams();
  const scrollyDivRef = useRef(null);
  const router = useRouter();
  const [isActive, setIsActive] = useState(false);
  const sliderRef = useRef(null);
  const [disableLeft, setDisableLeft] = useState(true);
  const [disableRight, setDisableRight] = useState(false);
  const [searchedArticles, setSearchedArticles] = useState([]);
  const [avgPercentage, setAvgPercentage] = useState(avgProgress || 0);
  const [currentArticlePercentage, setCurrentArticlePercentage] = useState(0);
  const [educationArticles, setEducationArticles] = useState(articleData);
  const [sanitizedHtmlBlog, setSanitizedHtmlBlog] = useState('');
  const [educationNextArticles, setEducationNextArticles] = useState(nextArticle);
  const [userId, setUserId] = useState(null);

  const debouncedSearch = useCallback(
    debounce(async (searchTerm) => {
      try {
        const educationArticlesData = await fetchEducationArticles(searchTerm, educationArticles?.id, userId);
        setSearchedArticles(educationArticlesData?.data?.education);
      } catch (error) {
        console.error(error);
      }
    }, 300),
    []
  );

  const handleEducationSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  const toggleClass = () => {
    setIsActive(!isActive);
  };

  async function fetchEducationArticles(searchKeyword = '', id = '', userId = null) {
    const params = new URLSearchParams({
      type: 'education',
      per_page: '8',
      is_next_article_sorting: 'true',
      sidebar: 'true',
    });

    if (searchKeyword) {
      params.append('key', searchKeyword);
    }

    if (id) {
      params.append('current_article_id', id);
    }

    if (userId) {
      params.append("user_id", userId);
    }

    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article?${params.toString()}`,
      { cache: 'no-store' }
    );

    if (!res.ok) throw new Error(`API error: ${res.status}`);

    return res.json();
  }

  async function fetchEducationDetail(detailSlug, userId = null) {
    const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detailSlug}`);

    if (userId) {
      url.searchParams.append("user_id", userId);
    }

    const res = await fetch(url.toString(), {
      cache: "no-store",
    });

    if (!res.ok) throw new Error(`API error: ${res.status}`);
    return res.json();
  }

  const getCookieValue = (name) => {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    return match ? decodeURIComponent(match[2]) : null;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        let userId = null;
        const authToken = Cookies.get("authToken");

        if (authToken) {
          const storedUser = localStorage.getItem("user");
          userId = JSON.parse(storedUser)?.id;
        }

        const detailRes = await fetchEducationDetail(detailSlug, userId);
        const educationArticlesData = await fetchEducationArticles(null, detailRes?.data?.id, userId);
        setSearchedArticles(educationArticlesData?.data?.education);
        setUserId(userId);
        setEducationArticles(detailRes?.data);
        setSanitizedHtmlBlog(detailRes?.data?.content || '<div></div>');
        setEducationNextArticles(detailRes?.next_article || null);
        setAvgPercentage(detailRes?.avgProgress || 0);

        if (authToken) {
          setCurrentArticlePercentage(detailRes?.progress || 0);
        } else {
          console.log('user id not exist');
          const slug = detailSlug;
          const browserId = window.navigator.userAgent;
          const cookieKey = `progress_${slug}_${btoa(browserId)}`;

          const match = document.cookie.match(new RegExp('(^| )' + cookieKey + '=([^;]+)'));
          const savedProgress = match ? decodeURIComponent(match[2]) : null;

          if (savedProgress !== null) {
            const parsed = parseInt(savedProgress, 10);
            if (!isNaN(parsed)) {
              setCurrentArticlePercentage(parsed);
              console.log(`✅ Progress loaded: ${cookieKey} = ${parsed}`);
            } else {
              console.warn(`⚠️ Cookie found but value is not a number: ${savedProgress}`);
            }
          } else {
            console.warn(`❌ Progress cookie not found for key: ${cookieKey}`);
          }
        }
      } catch (error) {
        console.error("Error fetching education detail:", error);
      }
    };

    fetchData();
  }, [detailSlug]);

  useEffect(() => {
    if (document.referrer) {
      sessionStorage.setItem("previousPage", document.referrer);
    }
  }, []);

  const handleBackToEducation = () => {
    const lastVisitedPage = sessionStorage.getItem("previousPage");

    if (lastVisitedPage) {
      router.back();
    } else {
      router.push("/education");
    }
  };

  const handleNextEducationClick = () => {
    const lastArticle = sessionStorage.getItem("lastEducationArticle") || window.location.href;
    sessionStorage.setItem("lastEducationArticle", lastArticle);
    router.push(`/education/${educationNextArticles?.slug}`);
  };

  useEffect(() => {
    if (params?.detail) {
      sessionStorage.setItem(
        "lastEducationArticle",
        `/education/${params.detail}`
      );
    }
  }, [params.detail]);

  const valuetwo = 100;

  const limitText = (text, limit = 80) => {
    if (text?.length <= limit) return text;

    const trimmedText = text?.substring(0, limit);
    const lastSpaceIndex = trimmedText?.lastIndexOf(" ");

    return lastSpaceIndex > 0 ? trimmedText?.substring(0, lastSpaceIndex) + "..." : trimmedText + "...";
  };

  const saveProgress = useCallback(
    debounce(async (percentage) => {
      const slug = params?.detail;
      const authToken = Cookies.get("authToken");

      try {
        if (authToken) {
          post(`/education/${slug}/progress`, { progress: percentage });
        } else {
          const browserId = window.navigator.userAgent;
          const cookieKey = `progress_${slug}_${btoa(browserId)}`;
          const cookieValue = percentage.toString();

          const consent = window.Osano?.cm?.getConsent?.();

          if (consent?.STORAGE === 'ACCEPT') {
            document.cookie = `${cookieKey}=${cookieValue}; path=/; max-age=31536000; Secure`;
          }
        }
      } catch (err) {
        console.error("Error saving progress:", err);
      }
    }, 1000),
    [params?.detail]
  );

  useEffect(() => {
    const scrollContainer = scrollyDivRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const contentTop = scrollContainer.offsetTop;
      const contentHeight = scrollContainer.offsetHeight;
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;

      const maxScrollable = contentHeight - windowHeight;
      if (maxScrollable <= 0) return;

      const scrollWithinContent = scrollY - contentTop;
      const progress = Math.min(Math.max(scrollWithinContent / maxScrollable, 0), 1);
      const roundedProgress = Math.round(progress * 100);

      if (roundedProgress > currentArticlePercentage) {
        setCurrentArticlePercentage(roundedProgress);
        saveProgress(roundedProgress);
      }
    };

    window.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
    handleScroll();

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, [currentArticlePercentage, saveProgress]);

  return (
    <div className="education_detail py-100">
      <Container>
        <div className="education_detail_inner"
        >
          <Row>
            <Col xs={12} lg={4} xl={3} className="order-lg-last">
              <div className="education_detail_sidebar">
                <div onClick={toggleClass} className="collapse_btn">
                  <RightArrowIcon />
                </div>
                <div
                  className={
                    isActive
                      ? "education_detail_sidebar_collapse active"
                      : "education_detail_sidebar_collapse"
                  }
                >
                  <div className="collapse_wrap">
                    <div className="education_detail_sidebar_top">
                      <CommonButton
                        title="Back to Education center"
                        className="w-100"
                        onClick={handleBackToEducation}
                      />
                      <div className="education_search mt-4">
                        <CommonSearch
                          placeholder="Search Key Terms"
                          icon={true}
                          onChange={handleEducationSearch}
                          name={"educationDetail"}
                        />
                      </div>
                    </div>
                    <div className="education_detail_sidebar_article">
                      <Row className="justify-content-center">
                        <Col xs={6} sm={4} md={3} lg={6}>
                          <div className="education_detail_sidebar_article_data">
                            <h6>Current Article</h6>
                            <CircularProgressbarWithChildren value={currentArticlePercentage}>
                              <div className="CircularProgressbar_text">
                                <h6>{currentArticlePercentage}%</h6>
                                <h6 className="text-uppercase">Complete</h6>
                              </div>
                            </CircularProgressbarWithChildren>
                          </div>
                        </Col>
                        <Col xs={6} sm={4} md={3} lg={6}>
                          <div className="education_detail_sidebar_article_data">
                            <h6>All Articles</h6>
                            <CircularProgressbarWithChildren value={avgPercentage}>
                              <div className="CircularProgressbar_text">
                                <h6>{avgPercentage}%</h6>
                                <h6 className="text-uppercase">Complete</h6>
                              </div>
                            </CircularProgressbarWithChildren>
                          </div>
                        </Col>
                      </Row>
                    </div>
                    <div className="position-relative">
                      <button
                        className={`scroll-btn left ${disableLeft ? "disabled" : ""
                          }`}
                        disabled={disableLeft}
                      >
                        <RightArrowIcon />
                      </button>
                      <div
                        ref={sliderRef}
                        className="education_detail_sidebar_profit"
                      >
                        {searchedArticles?.map((item, index) => (
                          <div
                            key={index}
                            className="education_detail_sidebar_profit_inner"
                            onClick={() => {
                              router.push(`/education/${item?.slug}`);
                            }}
                          >
                            <div className="d-flex education_detail_sidebar_profit_inner_detail">
                              <div className="education_detail_sidebar_profit_img">
                                <img
                                  src={item.feature_image_url}
                                  alt={
                                    item?.title
                                      ? `${item.title} - Trading metric visualization`
                                      : "Trading metric chart"
                                  }
                                />
                              </div>
                              <div className="education_detail_sidebar_profit_text">
                                <h6>{item.title}</h6>
                                <p>{limitText(item.summary)}</p>
                              </div>
                            </div>
                            <div className="education_detail_sidebar_profit_progressbar">
                              <ProgressBar now={item?.progress} />
                            </div>
                          </div>
                        ))}
                      </div>
                      <button
                        className={`scroll-btn right ${disableRight ? "disabled" : ""
                          }`}
                        disabled={disableRight}
                      >
                        <RightArrowIcon />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
            <Col xs={12} lg={8} xl={9} className="mt-0 mt-lg-0"
              ref={scrollyDivRef}
            >
              <div className="education_detail_heading">
                <Link
                  href={`/category/${educationArticles?.primary_category?.slug}`}
                  passHref
                >
                  <p className="education_detail_tag">
                    {educationArticles?.primary_category?.title
                      ? educationArticles?.primary_category?.title
                      : "No Category Found"}
                  </p>
                </Link>
                <h1>
                  What is {educationArticles?.title}?
                </h1>
              </div>
              <div className="education_detail_breadcrumb">
                <CustomBreadcrumb
                  href={`/education`}
                  linkname={'Education Center'}
                  pagename={educationArticles?.title}
                // linkname={educationArticles?.slug}
                // pagename={educationArticles?.primary_category?.title}
                />
              </div>
              <div className="education_detail_postimg">
                <img
                  src={educationArticles?.feature_image_url}
                  alt={educationArticles?.title}
                />
              </div>

              <div className="education_detail_text">
                <div dangerouslySetInnerHTML={{ __html: sanitizedHtmlBlog }} />
              </div>

              <div className="education_detail_author">
                {/* <Link href="/Blog">
                        <p className="education_detail_author_btn"> Next Term </p>
                      </Link> */}
                <div>
                  {educationNextArticles ? (
                    <>
                      <button
                        type="button"
                        className="education_detail_author_btn"
                        onClick={handleNextEducationClick}
                      >
                        Next Article
                      </button>

                      <RecentPost
                        img={educationNextArticles?.feature_image_url}
                        title={educationNextArticles?.title}
                        text={educationNextArticles?.summary}
                        coinname={educationNextArticles?.coinname}
                        href={`/education/${educationNextArticles?.slug}`}
                      // time={time}
                      // category={educationNextArticles?.primary_category?.title}
                      />
                    </>
                  ) : (
                    <h4>No next article available.</h4>
                  )}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </Container>
    </div>
  );
}

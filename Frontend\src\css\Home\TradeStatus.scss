@use "../theme/var";

.trade_status {
  &_box {
    padding-bottom: 5rem;

    @media screen and (max-width: 767px) {
      padding-bottom: 3rem;
    }

    &_card {
      background: var.$gradientGreen;
      border-radius: 1.25rem;
      margin-bottom: 1.875rem;
      display: flex;
      align-items: center;
      padding: 1.25rem;

      @media screen and (max-width: 767px) {
        padding: 0.8rem 0.625rem;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &_icon {
        width: 40px;

        @media screen and (max-width: 767px) {
          width: 30px;
        }

        svg {
          width: 40px;

          @media screen and (max-width: 767px) {
            width: 30px;
          }
        }
      }

      &.greenGradient {
        background: var.$gradientGreen;
      }

      &.redGradient {
        background: var.$gradientRed;
      }

      &.yellowGradient {
        background: var.$gradientYellow;
        color: var.$yellow;
      }

      &_content {
        padding-left: 1.25rem;
        width: calc(100% - 40px);
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media screen and (max-width: 767px) {
          padding-left: 1rem;
          width: calc(100% - 30px);
          padding-right: 10px;
        }

        h4 {
          @media screen and (max-width: 767px) {
            font-size: 18px;
          }
        }
      }
    }
  }

  .service_updates {
    padding: 50px 0;

    &_card {
      background-color: var.$white;
      padding: 50px 1.875rem;
      border-radius: 15px;
      display: flex;
      justify-content: center;
      margin-top: 50px;

      @media screen and (max-width: 767px) {
        padding: 20px 15px;
        flex-wrap: wrap;
        justify-content: flex-start;
        margin-top: 30px;
      }

      &_icon {
        width: 48px;

        @media screen and (max-width: 767px) {
          margin-bottom: 20px;
        }
      }

      &_content {
        padding-left: 1.25rem;
        width: calc(100% - 48px);

        @media screen and (max-width: 767px) {
          padding-left: 0px;
          width: 100%;
        }

        h4 {
          margin-bottom: 10px;
          color: var.$black;
        }

        p {
          font-size: 18px;
          font-weight: 600;
          line-height: 27px;
          letter-spacing: -1px;
          text-align: left;
          color: var.$textclr;

          @media screen and (max-width: 767px) {
            font-size: 15px;
            line-height: 23px;
          }
        }
      }
    }
  }
}

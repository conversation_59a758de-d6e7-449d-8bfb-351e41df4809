"use client";

import React, { useState, useRef, useEffect } from "react";
import { BlackDownIcon } from "@/assets/svgIcons/SvgIcon";

export default function CustomDropdown({
  options = [],
  defaultValue = "Select an option",
  optionLabelKey = "label",
  onSelect,
  className,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState("");
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (option) => {
    const label = option[optionLabelKey];
    setSelected(label);
    onSelect && onSelect(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="account-custom-select simple" ref={dropdownRef}>
      <div className="header" onClick={toggleDropdown}>
        <span>{selected || defaultValue}</span>
        <BlackDownIcon />
      </div>

      {isOpen && (
        <div className={`body ${className}`}>
          <ul>
            {options.length > 0 ? (
              options.map((option, optionIndex) => (
                <React.Fragment key={optionIndex}>
                  {option?.heading ? (
                    <>
                      <div className="option-heading">
                        {option.img && <img src={option.img} alt={option.heading} />}
                        <p>{option.heading}</p>
                      </div>
                      {option.data.map((opt, dataIndex) => (
                        <li key={dataIndex} onClick={() => handleSelect(opt)}>
                          {opt[optionLabelKey]}
                        </li>
                      ))}
                    </>
                  ) : (
                      <li key={optionIndex} onClick={() => handleSelect(option)}>
                        {option[optionLabelKey]}
                      </li>
                  )}
                </React.Fragment>
              ))
            ) : (
              <li className="no-results">No results found</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}

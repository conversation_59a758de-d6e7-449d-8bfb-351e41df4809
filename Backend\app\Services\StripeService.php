<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\Charge;
use Stripe\Customer;
use Stripe\PaymentIntent;
use Stripe\Checkout\Session;
use Stripe\Subscription;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Auth;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a new Stripe customer.
     */
    public function createCustomer($email, $name, $paymentMethod = null)
    {
        try {
            return Customer::create([
                'email' => $email,
                'name' => $name,
                'payment_method' => $paymentMethod,
            ]);
        } catch (ApiErrorException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Charge a customer.
     */
    public function createCharge($customerId, $amount, $currency = 'usd', $description = 'Payment')
    {
        try {
            return Charge::create([
                'customer' => $customerId,
                'amount' => $amount * 100, // Convert to cents
                'currency' => $currency,
                'description' => $description,
            ]);
        } catch (ApiErrorException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Create a Payment Intent for SCA (Strong Customer Authentication).
     */
    public function createPaymentIntent($amount, $currency = 'usd', $paymentMethod = null)
    {
        try {
            return PaymentIntent::create([
                'amount' => $amount * 100,
                'currency' => $currency,
                'payment_method' => $paymentMethod,
                'confirm' => true, // Auto-confirm payment
            ]);
        } catch (ApiErrorException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Create a checkout session for Stripe Checkout.
     */
    public function createCheckoutSession($amount, $currency = 'usd', $successUrl, $cancelUrl)
    {
        try {
            return Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => $currency,
                        'product_data' => [
                            'name' => 'Payment',
                        ],
                        'unit_amount' => $amount * 100,
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => $successUrl,
                'cancel_url' => $cancelUrl,
            ]);
        } catch (ApiErrorException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Create a new subscription for a customer.
     */
    public function createSubscription($customerId, $planId, $paymentMethod = null)
    {
        try {
            return Subscription::create([
                'customer' => $customerId,
                'items' => [['price' => $planId]],
                'default_payment_method' => $paymentMethod,
                'payment_behavior' => 'default_incomplete', // Allows confirmation later
                'expand' => ['latest_invoice.payment_intent'],
            ]);
        } catch (ApiErrorException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Create a subscription for the authenticated user.
     */
    public function create($paymentMethodId, $planId)
    {
        $user = Auth::user();

        if (!$user->stripe_id) {
            $customer = $this->createCustomer($user->email, $user->name, $paymentMethodId);

            if (isset($customer['error'])) {
                return $customer; // Return error if customer creation fails
            }

            $user->stripe_id = $customer->id;
            $user->save();
        }

        return $this->createSubscription($user->stripe_id, $planId, $paymentMethodId);
    }

}

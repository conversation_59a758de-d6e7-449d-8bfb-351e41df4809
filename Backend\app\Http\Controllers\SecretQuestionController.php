<?php

namespace App\Http\Controllers;

use App\Http\Requests\SecretQuestionRequest;
use App\Models\UserSecretQuestion;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SecretQuestionController extends Controller
{
    use ApiResponseTrait;

    /**
     * Get user's secret questions
     */
    public function index()
    {
        try {
            $user = auth()->user();

            $questions = UserSecretQuestion::where('user_id', $user->id)
                ->select('id', 'question', 'answer', 'created_at', 'updated_at')
                ->orderBy('created_at')
                ->get();

            // Always return exactly 2 questions for the frontend
            $formattedQuestions = [];
            for ($i = 0; $i < 2; $i++) {
                if (isset($questions[$i])) {
                    $formattedQuestions[] = [
                        'question' => $questions[$i]->question,
                        'answer' => $questions[$i]->answer, // Now send the decrypted answer
                        'error' => ''
                    ];
                } else {
                    $formattedQuestions[] = [
                        'question' => '',
                        'answer' => '',
                        'error' => ''
                    ];
                }
            }

            return $this->successResponse(
                $formattedQuestions,
                'Secret questions retrieved successfully'
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to retrieve secret questions',
                500,
                $e->getMessage()
            );
        }
    }

    /**
     * Store or update user's secret questions
     */
    public function store(SecretQuestionRequest $request)
    {
        try {
            $user = auth()->user();
            $questions = $request->validated()['questions'];

            DB::beginTransaction();

            // Delete existing questions for this user
            UserSecretQuestion::where('user_id', $user->id)->delete();

            // Create new questions
            foreach ($questions as $questionData) {
                UserSecretQuestion::create([
                    'user_id' => $user->id,
                    'question' => $questionData['question'],
                    'answer' => $questionData['answer'] // Will be hashed by the model
                ]);
            }

            DB::commit();

            return $this->successResponse(
                null,
                'Secret questions saved successfully'
            );
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse(
                'Failed to save secret questions',
                500,
                $e->getMessage()
            );
        }
    }

    /**
     * Update user's secret questions (alias for store)
     */
    public function update(SecretQuestionRequest $request)
    {
        return $this->store($request);
    }

    /**
     * Verify a secret question answer (for authentication purposes)
     */
    public function verify(Request $request)
    {
        try {
            $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string'
            ]);

            $user = auth()->user();

            $secretQuestion = UserSecretQuestion::where('user_id', $user->id)
                ->where('question', $request->question)
                ->first();

            if (!$secretQuestion) {
                return $this->errorResponse('Question not found', 404);
            }

            $isValid = $secretQuestion->verifyAnswer($request->answer);

            return $this->successResponse(
                ['valid' => $isValid],
                $isValid ? 'Answer verified successfully' : 'Invalid answer'
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to verify answer',
                500,
                $e->getMessage()
            );
        }
    }
}

<?php

namespace App\Traits;

use Exception;

trait CsvImporterTrait
{
    /**
     * Read CSV and return only matching fillable fields
     *
     * @param string $filePath
     * @param array $fillable
     * @param array $mapping
     * @return array
     * @throws Exception
     */
    public function importCsv(string $file, array $fillable, array $mapping = [])
    {
        try {
            // Open CSV file
            if (!file_exists($file) || !is_readable($file)) {
                throw new Exception("File not found or not readable.");
            }

            $handle = fopen($file, 'r');
            if (!$handle) {
                throw new Exception("Unable to open file.");
            }

            // Read the first line as headers
            $headers = fgetcsv($handle);
            if (!$headers) {
                throw new Exception("Invalid CSV format.");
            }

            // Map headers to database fields
            $validColumns = [];
            foreach ($headers as $header) {
                $field = $mapping[$header] ?? $header;
                if (in_array($field, $fillable)) {
                    $validColumns[$header] = $field;
                }
            }

            // Read CSV rows
            $records = [];
            while (($row = fgetcsv($handle)) !== false) {
                $data = [];
                foreach ($validColumns as $csvHeader => $dbField) {
                    $index = array_search($csvHeader, $headers);
                    $data[$dbField] = $index !== false ? $row[$index] : null;
                }
                $records[] = (object) $data;
            }

            fclose($handle);
            return $records;

        } catch (Exception $e) {
            throw new Exception("Error processing CSV: " . $e->getMessage());
        }
    }
}

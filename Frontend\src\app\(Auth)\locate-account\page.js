'use client';
import { useState, useEffect } from "react";
import { RightArrowIconSvg, RedErrorCircle } from "@/assets/svgIcons/SvgIcon";
import LoginFooter from "@/Components/UI/LoginFooter";
import AuthLayout from "@/Layouts/AuthLayout";
import Link from "next/link";
import Head from "next/head";
import TextInput from "@/Components/UI/TextInput";
import { localAccountSchema } from "@/validations/schema";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import AuthLogo from "@/Components/common/AuthLogo";
import { Formik, Field, Form } from "formik";
import NavLink from "@/Components/UI/NavLink";
import InputError from "@/Components/UI/InputError";
import MetaHead from "@/Seo/Meta/MetaHead";
import { forgotPassword } from "@/utils/auth";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { hashInput } from "@/utils/hashInput";

const initialValues = {
  emailOrUsername: "",
};

export default function ForgotPassword({ status }) {
  const router = useRouter();
  const [isEmail, setIsEmail] = useState(null);
  const [cooldown, setCooldown] = useState(0);
  const [isCooldownActive, setIsCooldownActive] = useState(false);
  const [lockoutMessage, setLockoutMessage] = useState("");


  const startCooldown = (duration = 60) => {
    setIsCooldownActive(true);
    setCooldown(duration);
    localStorage.setItem("localaccount_resend_cooldown", Date.now() + duration * 1000);

    const interval = setInterval(() => {
      setCooldown(prev => {
        if (prev <= 1) {
          clearInterval(interval);
          setIsCooldownActive(false);
          localStorage.removeItem("localaccount_resend_cooldown");
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    sessionStorage.removeItem("masked_email");
    sessionStorage.removeItem("identifier_type");
    sessionStorage.removeItem("signup_data");
    sessionStorage.removeItem("reset_password_data");

    const storedCooldown = localStorage.getItem("localaccount_resend_cooldown");
    if (storedCooldown) {
      const remaining = Math.floor((+storedCooldown - Date.now()) / 1000);
      if (remaining > 0) {
        startCooldown(remaining);
      }
    }
  }, []);

  useEffect(() => {
    const message = localStorage.getItem("lockout_redirect_message");
    if (message) {
      setLockoutMessage(message);
      localStorage.removeItem("lockout_redirect_message");

      const timer = setTimeout(() => {
        setLockoutMessage("");
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, []);


  const handleSubmit = async (values, { setSubmitting, setErrors, setStatus }) => {
    try {
      const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(values.emailOrUsername);
      const type = isEmail ? "email" : "username";
      const uuid = uuidv4();
      const expiresInMinutes = 15;
      const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;

      const response = await forgotPassword(type, values.emailOrUsername, uuid);

      sessionStorage.setItem("masked_email", hashInput(values.emailOrUsername));
      sessionStorage.setItem("identifier_type", type); // 'email' or 'username'

      if (response.success) {
        sessionStorage.setItem(
          "reset_password_data",
          JSON.stringify({ uuid, expiresAt })
        );

        startCooldown();
        setStatus("Password reset link sent! Check your email.");
        router.push(`/security-check?resetPassword=reset_password_data`);
      } else {
        setErrors({ emailOrUsername: response.message });
      }
    } catch (error) {
      setErrors({ emailOrUsername: "Something went wrong. Please try again." });
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (e, setFieldValue) => {
    const value = e.target.value;
    setFieldValue("emailOrUsername", value);

    if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      setIsEmail(true);
    } else {
      setIsEmail(false);
    }
  };

  const metaArray = {
    noindex: true,
    title: "Locate Your TradeReply Account | Account Recovery",
    description: "Recover your TradeReply.com account. Reset your password and regain access to your personalized trading tools, analytics, and strategies in just a few steps.",
    canonical_link: "https://www.tradereply.com/locate-account",
    og_site_name: "TradeReply",
    og_title: "Locate Your TradeReply Account | Account Recovery",
    og_description: "Recover your TradeReply.com account. Reset your password and regain access to your personalized trading tools, analytics, and strategies in just a few steps.",
    twitter_title: "Locate Your TradeReply Account | Account Recovery",
    twitter_description: "Recover your TradeReply.com account. Reset your password and regain access to your personalized trading tools, analytics, and strategies in just a few steps.",
  };

  return (
    <AuthLayout>
      <MetaHead props={metaArray} />
      <Head>
        <title>Forgot Password</title>
      </Head>
      <div className="loginCommon_rightSide">
        <div className="loginCommon_rightSide_inner">
          <div className="backbtn">
            <Link href="/">
              <RightArrowIconSvg color="svg-white_baseblue" /> Return to Home
            </Link>
          </div>
          <div className="loginCommon_rightSide_formBox forgot_form">
            <AuthLogo />
            <div className="loginHeading">
              <h1>Locate Your Account</h1>
            </div>

            <div className="orLine">
              <span>Enter your email or username</span>
            </div>
            <div className="loginTabs">
              <div className="loginForm">
                {lockoutMessage && (
                  <div className="invalid_credential mb-4">
                    <RedErrorCircle />
                    <span>
                      {lockoutMessage}
                    </span>
                  </div>
                )}
                <Formik
                  initialValues={initialValues}
                  validationSchema={localAccountSchema}
                  onSubmit={handleSubmit}
                >
                  {({ values, errors, touched, isSubmitting, submitCount, setFieldValue, dirty }) => (
                    <Form>
                      <div className="authCorrectIcon">
                        <div className="checkIcon">
                          {values.emailOrUsername && !errors.emailOrUsername && dirty && <CheckIcon width="25" height="25" />}
                        </div>
                        <Field name="emailOrUsername">
                          {({ field, meta }) => (
                            <TextInput
                              {...field}
                              placeholder="Email or username"
                              type="text"
                              maxLength={100}
                              onChange={(e) => handleInputChange(e, setFieldValue)}
                              error={submitCount > 0 && meta.error ? <InputError message={meta.error} /> : null}
                              isError={submitCount > 0 && meta.error ? true : false}
                            />
                          )}
                        </Field>
                      </div>
                      <div className="w-100">
                        <button
                          type="submit"
                          className="btn-style fluid w-100"
                          disabled={isSubmitting || isCooldownActive}
                        >
                          {isSubmitting ? "Locating..." : "Locate Account"}
                          <span className="ps-3">{isCooldownActive ? `${cooldown}s` : null}</span>
                        </button>

                      </div>
                      <div className="anAccount mt-3 text-center">
                        <h6>
                          <NavLink href="/login" className="ml-1">Return to Login</NavLink>
                        </h6>
                      </div>
                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </div>
          <div className="mt-4 mt-md-5">
            <LoginFooter />
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

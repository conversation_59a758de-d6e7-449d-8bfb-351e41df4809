"use client";

import { useEffect, useState } from "react";
import { Row, Col, Container } from "react-bootstrap";
import { PlusIcon, SettingIcon, SolidRedArrowIcon } from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import CommonButton from "@/Components/UI/CommonButton";
import DashboardLayout from "@/Layouts/DashboardLayout";
import "@/css/dashboard/TradeManager.scss";
import MetaHead from "@/Seo/Meta/MetaHead";
import { get } from "@/utils/apiUtils";
import Link from "next/link";
import SettingWheel from "@/Components/common/Dashboard/SettingWheel/SettingWheel";


const TradeManager = () => {
  const [draftTrades, setDraftTrades] = useState([]);
  const [tradeAccounts, setTradeAccounts] = useState([]);
  const [selectedTradeAccountId, setSelectedTradeAccountId] = useState(null);
  const [isSettingWheelPopup, setSettingWheelPopup] = useState(false);

  const openSettingWheelPopup = () => setSettingWheelPopup(true);
  const closeSettingWheelPopup = () => setSettingWheelPopup(false);

  useEffect(() => {
    const fetchTradeAccounts = async () => {
      try {
        const response = await get("/trade-accounts");
        if (response.success) {
          setTradeAccounts(response.data || []);
          const storedId = localStorage.getItem("selectedTradeAccountId");
          setSelectedTradeAccountId(storedId || response.data[0]?.id || null);
        }
      } catch (err) {
        console.error("Fetch Trade Accounts Error:", err.response?.data || err.message);
      }
    };
    fetchTradeAccounts();
  }, []);

  useEffect(() => {
    if (!selectedTradeAccountId) return;

    const fetchDraftTrades = async () => {
      try {
        const response = await get(`/drafts?trade_account_id=${selectedTradeAccountId}`);
        if (response.success) {
          setDraftTrades(response.data || []);
        }
      } catch (err) {
        console.error("Fetch Draft Trades Error:", err.response?.data || err.message);
      }
    };
    fetchDraftTrades();
  }, [selectedTradeAccountId]);

  useEffect(() => {
    if (selectedTradeAccountId) {
      localStorage.setItem("selectedTradeAccountId", selectedTradeAccountId);
    }
  }, [selectedTradeAccountId]);

  const metaArray = {
    noindex: true,
    title: "Trade Manager | Manage Your Trades | TradeReply",
    description: "Manage and track your trades with TradeReply's Trade Manager. Stay on top of your trading activities with easy-to-use tools.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-manager",
    og_site_name: "TradeReply",
    og_title: "Trade Manager | Manage Your Trades | TradeReply",
    og_description: "Manage and track your trades with TradeReply's Trade Manager. Stay on top of your trading activities with easy-to-use tools.",
    twitter_title: "Trade Manager | Manage Your Trades | TradeReply",
    twitter_description: "Manage and track your trades with TradeReply's Trade Manager. Stay on top of your trading activities with easy-to-use tools.",
  };

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="trade_manager">
        <CommonHead
          tradeAccounts={tradeAccounts}
          selectedTradeAccountId={selectedTradeAccountId}
          setSelectedTradeAccountId={setSelectedTradeAccountId}
        />
        <Container>
          <div className="trade_head custom_trade_head">
            <AdminHeading heading="Trade Manager" />
            <button className="setting_btn" type="button"
              onClick={openSettingWheelPopup}
              style={isSettingWheelPopup ? { filter: "brightness(0.2) invert(1)" } : {}}
            >
              <SettingIcon color="image_color_to_white" />
            </button>
          </div>
          {isSettingWheelPopup && (
            <SettingWheel
              hideSetting={closeSettingWheelPopup}
            />
          )}
          {!isSettingWheelPopup && (
            <>
              <div className="trade_manager_btns">
                <Row>
                  <Col sm={12} md={6}>
                    <Link
                      href={`/dashboard/trade-builder`}
                      className="w-100"
                    >
                      <CommonButton
                        title="Manual Trade"
                        onlyIcon={<PlusIcon />}
                        className="w-full mb-3 mb-sm-0"
                      />
                    </Link>
                  </Col>
                  <Col sm={12} md={6}>
                    <Link
                      href={`/dashboard/trade-importer?trade_account_id=${selectedTradeAccountId || ""}`}
                      className="w-100"
                    >
                      <CommonButton
                        title="Import Trade"
                        onlyIcon={<PlusIcon />}
                        className="w-full"
                        disabled={!selectedTradeAccountId}
                      />
                    </Link>
                  </Col>
                </Row>
              </div>
              <div className="trade_manager_trade_entry">
                {draftTrades.map((trade, index) => (
                  <Link
                    key={index}
                    href={`/dashboard/trade-builder?tradeId=${trade.id}&trade_account_id=${selectedTradeAccountId}`}
                    className="w-100"
                  >
                    <div
                      className={`trade_manager_trade_entry_box Redgrandient ${index === 1 ? "greengrandient" : ""}`}
                    >
                      <span className="solidArrow red_arrow me-3">
                        <SolidRedArrowIcon />
                      </span>
                      <div className="d-flex trade_manager_trade_entry_box_headtext align-items-center w-100 justify-content-between">
                        <h5>{trade.draft_title}</h5>
                        <h5>{new Date(trade.created_at).toLocaleDateString()}</h5>
                        <h5>-</h5>
                        <h5>-</h5>
                        <h5>-</h5>
                        <h5>-</h5>
                        <h5>-</h5>
                        <h5>-</h5>
                      </div>
                      <span className="solidArrow red_arrow endArrow ms-3">
                        <SolidRedArrowIcon />
                      </span>
                    </div>
                  </Link>
                ))}
              </div>
            </>
          )}
        </Container>
      </div>
    </DashboardLayout>
  );
};

export default TradeManager;
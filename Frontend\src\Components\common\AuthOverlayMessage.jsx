import React, { useEffect, useState } from "react";
import Link from "next/link";
import Cookies from "js-cookie";

export default function AuthOverlayMessage({ isLoggedIn, Overlay }) {

    const [loginToken, SetLoginToken] = useState(isLoggedIn);

    useEffect(() => {
        SetLoginToken(isLoggedIn);
    }, [isLoggedIn]);

    return (
        <>
            {!loginToken && (
                <div className="auth-overlay-container">
                    <div className={`auth-overlay-message px-3 ${Overlay}`}>
                        <p>
                            Unlock powerful trading tools.{" "}
                            <Link href="/login">Log in</Link> or{" "}
                            <Link href="/signup">Sign up</Link> for full access.
                        </p>
                    </div>
                </div>
            )}
        </>
    );
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class GeoIpService
{
    /**
     * Get city from IP address using multiple fallback methods
     *
     * @param string $ip
     * @return string
     */
    public static function getCity(string $ip): string
    {
        // Handle local/private IPs
        if (self::isLocalIp($ip)) {
            return 'Local Network';
        }

        // Try cached result first
        $cacheKey = "geoip_city_{$ip}";
        $cached = Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }

        $city = self::getCityFromSources($ip);

        // Cache result for 24 hours
        Cache::put($cacheKey, $city, 86400);

        return $city;
    }

    /**
     * Get country from IP address using multiple fallback methods
     *
     * @param string $ip
     * @return string
     */
    public static function getCountry(string $ip): string
    {
        // Handle local/private IPs
        if (self::isLocalIp($ip)) {
            return 'Local Network';
        }

        // Try cached result first
        $cacheKey = "geoip_country_{$ip}";
        $cached = Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }

        $country = self::getCountryFromSources($ip);

        // Cache result for 24 hours
        Cache::put($cacheKey, $country, 86400);

        return $country;
    }

    /**
     * Get city from various sources with fallback
     *
     * @param string $ip
     * @return string
     */
    private static function getCityFromSources(string $ip): string
    {
        // Try free IP-API service first
        $city = self::getCityFromIpApi($ip);
        if ($city !== 'Unknown City') {
            return $city;
        }

        // Try ipinfo.io as fallback
        $city = self::getCityFromIpInfo($ip);
        if ($city !== 'Unknown City') {
            return $city;
        }

        // Try ipapi.co as final fallback
        $city = self::getCityFromIpapiCo($ip);
        if ($city !== 'Unknown City') {
            return $city;
        }

        return 'Unknown City';
    }

    /**
     * Get country from various sources with fallback
     *
     * @param string $ip
     * @return string
     */
    private static function getCountryFromSources(string $ip): string
    {
        // Try free IP-API service first
        $country = self::getCountryFromIpApi($ip);
        if ($country !== 'Unknown Country') {
            return $country;
        }

        // Try ipinfo.io as fallback
        $country = self::getCountryFromIpInfo($ip);
        if ($country !== 'Unknown Country') {
            return $country;
        }

        // Try ipapi.co as final fallback
        $country = self::getCountryFromIpapiCo($ip);
        if ($country !== 'Unknown Country') {
            return $country;
        }

        return 'Unknown Country';
    }



    /**
     * Get city from IP-API service (free tier)
     *
     * @param string $ip
     * @return string
     */
    private static function getCityFromIpApi(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ip}?fields=city");

            if ($response->successful()) {
                $data = $response->json();
                return $data['city'] ?? 'Unknown City';
            }
        } catch (\Exception $e) {
            Log::warning("IP-API city lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown City';
    }

    /**
     * Get country from IP-API service (free tier)
     *
     * @param string $ip
     * @return string
     */
    private static function getCountryFromIpApi(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ip}?fields=country");

            if ($response->successful()) {
                $data = $response->json();
                return $data['country'] ?? 'Unknown Country';
            }
        } catch (\Exception $e) {
            Log::warning("IP-API country lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown Country';
    }

    /**
     * Get city from ipinfo.io service
     *
     * @param string $ip
     * @return string
     */
    private static function getCityFromIpInfo(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("https://ipinfo.io/{$ip}/json");

            if ($response->successful()) {
                $data = $response->json();
                return $data['city'] ?? 'Unknown City';
            }
        } catch (\Exception $e) {
            Log::warning("IPInfo city lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown City';
    }

    /**
     * Get country from ipinfo.io service
     *
     * @param string $ip
     * @return string
     */
    private static function getCountryFromIpInfo(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("https://ipinfo.io/{$ip}/json");

            if ($response->successful()) {
                $data = $response->json();
                return $data['country'] ?? 'Unknown Country';
            }
        } catch (\Exception $e) {
            Log::warning("IPInfo country lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown Country';
    }

    /**
     * Get city from ipapi.co service (free tier)
     *
     * @param string $ip
     * @return string
     */
    private static function getCityFromIpapiCo(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("https://ipapi.co/{$ip}/json/");

            if ($response->successful()) {
                $data = $response->json();
                return $data['city'] ?? 'Unknown City';
            }
        } catch (\Exception $e) {
            Log::warning("IPAPI.co city lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown City';
    }

    /**
     * Get country from ipapi.co service (free tier)
     *
     * @param string $ip
     * @return string
     */
    private static function getCountryFromIpapiCo(string $ip): string
    {
        try {
            $response = Http::timeout(5)->get("https://ipapi.co/{$ip}/json/");

            if ($response->successful()) {
                $data = $response->json();
                return $data['country_name'] ?? 'Unknown Country';
            }
        } catch (\Exception $e) {
            Log::warning("IPAPI.co country lookup failed for IP {$ip}: " . $e->getMessage());
        }

        return 'Unknown Country';
    }

    /**
     * Check if IP is local/private
     *
     * @param string $ip
     * @return bool
     */
    private static function isLocalIp(string $ip): bool
    {
        // Check for localhost
        if (in_array($ip, ['127.0.0.1', '::1', 'localhost'])) {
            return true;
        }

        // Check for private IP ranges
        return !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
    }

    /**
     * Get complete location data from IP
     *
     * @param string $ip
     * @return array
     */
    public static function getLocationData(string $ip): array
    {
        return [
            'city' => self::getCity($ip),
            'country' => self::getCountry($ip),
            'ip' => $ip,
            'is_local' => self::isLocalIp($ip)
        ];
    }
}

/* [project]/src/css/Home/Education.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.education {
  padding: 5rem 0;
}

@media (max-width: 991px) {
  .education {
    padding-top: 40px !important;
  }
}

.education .container {
  max-width: 1080px;
}

.education_heading p {
  letter-spacing: -.1px;
  margin: 30px 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 28px;
}

@media (max-width: 991px) {
  .education_heading p {
    margin: 20px 0;
    font-size: 1rem;
    line-height: 22px;
  }
}

.education_heading h1 {
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 1199px) {
  .education_heading h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 767px) {
  .education_heading h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 390px) {
  .education_heading h1 {
    font-size: 1.3rem;
  }
}

.education_search .commonSearch {
  max-width: 400px;
  margin: 0 auto;
}

.education_search .commonSearch .form-control {
  width: 100%;
}

.education_fliters {
  padding: 30px 0 80px;
}

@media (max-width: 991px) {
  .education_fliters {
    padding: 20px 0 30px;
  }
}

@media (max-width: 767px) {
  .education_fliters {
    padding: 20px 0 10px;
  }
}

.education_fliters_inner {
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
  display: flex;
}

@media (max-width: 767px) {
  .education_fliters_inner {
    margin-bottom: 20px;
  }
}

.education_fliters_boxbutton {
  color: #fff;
  letter-spacing: -.1px;
  background-color: rgba(0, 173, 239, .15);
  border: 0;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  margin-bottom: 8px;
  margin-right: 10px;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5rem;
  transition: all .3s ease-in-out;
  display: flex;
}

@media (max-width: 767px) {
  .education_fliters_boxbutton {
    width: 26px;
    height: 26px;
    font-size: .875rem;
  }
}

.education_fliters_boxbutton:last-child {
  margin-right: 0;
}

.education_fliters_boxbutton:hover, .education_fliters_boxbutton.active {
  background-color: #00adef;
}

.education_fliters_boxadd {
  background-color: #00adef;
  border-radius: 15px;
  padding: 5px 15px;
  display: inline-flex;
}

.education_fliters_boxadd h6 {
  color: #fff;
  align-items: center;
  display: flex;
}

.education_fliters_boxadd h6 img {
  cursor: pointer;
}

.education_pagination {
  justify-content: flex-end;
  display: flex;
}

.education .education {
  padding: 2rem 0;
}

.education .education_term_head {
  margin: 0;
  padding: 10px 5px;
}

.education .education_term_list {
  border-top: 1px solid #666;
  margin: 0;
  padding: 15px 5px;
}

.education .education_term_list:last-child {
  border-bottom: 1px solid #666;
}

.education .education_term_list p {
  color: #f5f5f5;
  text-align: left;
  font-size: 15px;
  font-weight: 400;
  line-height: 22.5px;
}

@media (max-width: 767px) {
  .education .education_term_list p {
    margin-top: 1.25rem;
  }
}

.education .education_term_list .read_more_button {
  border-radius: 10px;
  min-width: 120px;
  min-height: 46px;
  padding: 8px 1rem;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

/*# sourceMappingURL=src_css_Home_Education_scss_css_e59ae46c._.single.css.map*/
import React, { useState, useEffect, useRef } from "react";
import "@/css/dashboard/TradeAnalysis.scss";
import AddFiltersModal from "./AddFilterModal";
import ConditionBuilder from "./ConditionalBuilder";
import { AddPlusIcon, DeleteIcon, WhiteDownArrow, } from "@/assets/svgIcons/SvgIcon";

export default function Filters() {
    const [isFilterModal, setIsFilterModal] = useState(false);
    const [editingFilterData, setEditingFilterData] = useState(null);
    const [editingFilterIndex, setEditingFilterIndex] = useState(null);
    const [filterList, setFilterList] = useState([]);

    const filterModal = (item = null, index = null) => {
        if (item !== null && index !== null) {
            setEditingFilterData(item);
            setEditingFilterIndex(index);
        } else {
            setEditingFilterData(null);
            setEditingFilterIndex(null);
        }
        setIsFilterModal((prev) => !prev);
    };
    const handleFieldSelect = (data) => {
        if (editingFilterIndex !== null) {
            const updated = [...filterList];
            updated[editingFilterIndex] = data;
            setFilterList(updated);
        } else {
            setFilterList((prev) => [...prev, data]);
        }
        setEditingFilterData(null);
        setEditingFilterIndex(null);
        setIsFilterModal(false);
    };
    const deleteFilterList = (index) => {
        const updatedList = [...filterList];
        updatedList.splice(index, 1);
        setFilterList(updatedList);
    };
    const handleConditionsUpdate = () => {
        // setAndSections(updated)
    };
    return (
        <div className="strategies">
            <div className="cardHeading">
                <p>Filters</p>
            </div>
            <div className="add_filters">
                <div className="d-flex gap-2 align-items-center">
                    <div className="dashed_design"></div>
                    <h6 className="header_title">
                        Add Filter Condition</h6>
                </div>
                <div
                    className="d-flex gap-2 cursor-pointer"
                    onClick={filterModal}

                >
                    <p className="count_add">({filterList?.length}/5)</p>
                    <AddPlusIcon />
                </div>
            </div>
            <div className="trade_analysis_filter">
                {filterList.map((item, index) => (
                    <div className="filter_section" key={index}>
                        <div className="include_trades">
                            <div className="head">
                                <div
                                    className={`${item.type == "include" ? "include" : "exclude"
                                        }`}
                                ></div>
                                <span>
                                    {item.type == "include" ? "Include" : "Exclude"}{" "}
                                    trades when
                                </span>
                            </div>
                            <button onClick={() => deleteFilterList(index)}>
                                <DeleteIcon />
                            </button>
                        </div>
                        <div className="include_trades relative">
                            <div className="head">
                                <div>
                                    <h6>{item.title}</h6>
                                    <p>Scope: {item.activeScope}</p>
                                </div>
                            </div>
                            <button onClick={() => filterModal(item, index)}>
                                <WhiteDownArrow height={"7px"} width={"14px"} />
                            </button>
                        </div>
                        <ConditionBuilder
                            onConditionsChange={(conds) =>
                                handleConditionsUpdate(conds)
                            }
                        />
                    </div>
                ))}

                {isFilterModal && (
                    <AddFiltersModal
                        title="Filters"
                        toggleModal={filterModal}
                        btnIncExc={true}
                        sendToParentFunction={handleFieldSelect}
                        defaultValues={editingFilterData}
                    />
                )}
            </div>
        </div>
    )
}
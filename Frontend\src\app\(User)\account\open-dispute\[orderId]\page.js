"use client";
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import CustomWhiteDropdown from "@/Components/common/CustomWhiteDropdown";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function OpenDispute() {
  const [fileName, setFileName] = useState("Upload File");
  const [disputeDesc, setDisputeDesc] = useState("");

  const MAX_Descrip_LENGTH = 500;

  const descDesktoRef = useRef(null);
  const descMobileRef = useRef(null);

  const resolOptions = [
    { name: "Full refund" },
    { name: "Partial refund" },
    { name: "Replacement file or fixed version" },
    { name: "Corrected external link" },
    { name: "Communication from seller" },
    { name: "No specific instructions (just want to report this issue)" },
  ];
  const disputeOptions = [
    {
      img: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-wrench.svg",
      heading: "General Issues",
      data: [
        { label: "File not received" },
        { label: "File is corrupt or won’t open" },
        { label: "File is incomplete or missing sections" },
        { label: "External download link is broken or incorrect " },
        { label: "Duplicate purchase made by mistake " },
        { label: "Seller did not respond or follow up " },
        { label: "Other" }
      ],
    },
    {
      img: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-product-box.svg",
      heading: "Product - Specific Issues",
      data: [
        { label: "Issue with trading strategy or playbook" },
        { label: "Indicator or script not working as expected" },
        { label: "Access to live event not provided" },
        { label: "Locked out of digital community/group" },
        { label: "Signals or alerts not being delivered" },
        { label: "Educational content is outdated or inaccurate" },
        { label: "Missing promised templates, spreadsheets, or add-ons" }
      ],
    },
  ];
  const disputeArray = [
    {
      seller: "@username",
      orderId: "29311",
      product: "[Auto-filled]",
      dispute: "3432",
      status: "Open",
      openedOn: "Mar 24, 2025",
    },
  ];

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const getTruncatedFileName = (name, maxLength = 18) => {
    return name.length > maxLength
      ? name.slice(0, maxLength - 3) + "..."
      : name;
  };
  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(descDesktoRef);
    autoResize(descMobileRef);
  }, [disputeDesc]);
  useEffect(() => {
    const handleResize = () => {
      autoResize(descDesktoRef);
      autoResize(descMobileRef);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const selectCountry = (country) => {
    console.log("Selected:", country);
  };
  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Open Dispute" />
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Order Summary" className="account_card">
                <div className="account_card_disputes">
                  <div className="lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Product Title</div>
                          </th>
                          <th>
                            <div className="th-inner">Seller</div>
                          </th>
                          <th>
                            <div className="th-inner">Order ID</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Link href="/marketplace/details">
                                {item.product}
                              </Link>
                            </td>
                            <td>{item.seller}</td>
                            <td>{item.orderId}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Dispute Form" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Dispute Category</div>
                          </th>
                          <th>
                            <div className="th-inner">Description</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Upload Files
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">Preferred Outcome</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <CustomWhiteDropdown
                                options={disputeOptions}
                                defaultValue="Select reason for your dispute "
                                onSelect={selectCountry}
                                className="align-left"
                              />
                            </td>
                            <td>
                              <div className="customInput_inner">
                                <textarea
                                  ref={descDesktoRef}
                                  rows="2"
                                  maxLength={MAX_Descrip_LENGTH}
                                  className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                  value={disputeDesc}
                                  onChange={(e) =>
                                    setDisputeDesc(e.target.value)
                                  }
                                />
                                <p className="character-count">
                                  Characters: {disputeDesc.length}/
                                  {MAX_Descrip_LENGTH}
                                </p>
                              </div>
                            </td>
                            <td>
                              <label className="file-upload-wrapper">
                                {getTruncatedFileName(fileName)}
                                <input
                                  type="file"
                                  name="file"
                                  onChange={handleFileChange}
                                />
                              </label>
                            </td>
                            <td>
                              <CustomWhiteDropdown
                                options={resolOptions.map((c) => ({
                                  label: c.name,
                                  ...c,
                                }))}
                                defaultValue="Select your preferred resolution"
                                onSelect={selectCountry}
                                className="align-right"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Dispute Category</Col>
                          </div>

                          <Col xs={12} className="colunm_value px-0">
                            <CustomWhiteDropdown
                              options={disputeOptions}
                              defaultValue="Select reason for your dispute "
                              onSelect={selectCountry}
                              className="align-left"
                            />
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Description</Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <div className="customInput_inner">
                              <textarea
                                ref={descMobileRef}
                                rows="2"
                                maxLength={MAX_Descrip_LENGTH}
                                className="form-control table_form_textarea w-full resize-none overflow-hidden"
                                value={disputeDesc}
                                onChange={(e) => setDisputeDesc(e.target.value)}
                              />
                              <p className="character-count">
                                Characters: {disputeDesc.length}/
                                {MAX_Descrip_LENGTH}
                              </p>
                            </div>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Upload Files
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <label className="file-upload-wrapper">
                              {getTruncatedFileName(fileName)}
                              <input
                                type="file"
                                name="file"
                                onChange={handleFileChange}
                              />
                            </label>
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Preferred Outcome</Col>
                          </div>
                          <Col xs={12} className="colunm_value px-0">
                            <CustomWhiteDropdown
                              options={resolOptions.map((c) => ({
                                label: c.name,
                                ...c,
                              }))}
                              defaultValue="Select your preferred resolution"
                              onSelect={selectCountry}
                              className="align-right"
                            />
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
              <CommonButton
                title="Submit Dispute"
                className="view_res_btn mt-4 w-100"
              />
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

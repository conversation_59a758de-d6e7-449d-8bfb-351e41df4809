'use client';

import { useRef, useEffect } from "react";

export default function ScrollComponent() {
  const scrollyDivRef = useRef(null);

  useEffect(() => {
    const scrollyDiv = scrollyDivRef.current;
    if (!scrollyDiv) return;

    const handleScroll = () => {
      console.log(scrollyDiv.scrollTop);
    };

    scrollyDiv.addEventListener("scroll", handleScroll);

    return () => {
      scrollyDiv.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div
      ref={scrollyDivRef}
      style={{
        width: "300px",
        height: "200px",
        overflowY: "auto", // Important: Use `auto` to allow scrolling when needed
        border: "1px solid black",
        padding: "10px",
      }}
    >
      <div style={{ height: "500px", background: "#f0f0f0" }}>
        Scroll inside this div to see `scrollTop` in the console.
      </div>
    </div>
  );
}

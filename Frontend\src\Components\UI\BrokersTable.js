'useClient';

import { CheckIcon, GreyCrossIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "./CommonTable";
import "../../css/common/BrokersTable.scss";
import "../../css/brokersTable.css";

import { useEffect, useState } from "react";

const BrokersTable = ({ providers }) => {
  const fields = ["Broker/Integration", "URL", "Stocks", "Crypto", "AutoSync"];
  const [Providers, setProviders] = useState([]);
  useEffect(() => {
    setProviders(providers);
  }, [providers]);


  return (
    <div className="brokers_table">
      <CommonTable fields={fields}>
        {Providers?.map((item, index) => (
          <tr key={index}>
            <td className="text-break ws-normal">{item?.name}</td>
            <td>
              <a className="text-break ws-normal" href={item?.url} target="_blank" rel="noopener" aria-label={`opens ${item?.name} in a new tab`}>
                {item?.url}
              </a>
            </td>
            <td className="text-center m-auto">
              <div className="text-center d-flex justify-content-center align-items-center">
                {item?.stocks ? <CheckIcon width={28} /> : <GreyCrossIcon />}
              </div>
            </td>
            <td className="text-center">
              <div className="text-center d-flex justify-content-center align-items-center">
                {item?.crypto ? <CheckIcon width={28} /> : <GreyCrossIcon />}
              </div>
            </td>
            <td className="text-center">
              <div className="text-center d-flex justify-content-center align-items-center">
                {item?.auto_sync ? <CheckIcon width={28} /> : <GreyCrossIcon />}
              </div>
            </td>
          </tr>
        ))}
      </CommonTable>
    </div>
  );
};

export default BrokersTable;

"use client";
import React, { useState } from "react";
import { Col, Row } from "react-bootstrap";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";

export default function PercentageIncreaseCalculator() {
    const [startValue, setStartValue] = useState("");
    const [finalValue, setFinalValue] = useState("");
    const [percentageChange, setPercentageChange] = useState("0.00%");

    const formatPercentage = (value) => {
        return isNaN(value) || value === "" ? "0.00%" : `${parseFloat(value).toFixed(2)}%`;
    };

    const validateNumericInput = (value) => {
        let sanitizedValue = value.replace(/[^0-9.]/g, "");

        const firstPeriodIndex = sanitizedValue.indexOf(".");
        if (firstPeriodIndex !== -1) {
            sanitizedValue =
                sanitizedValue.slice(0, firstPeriodIndex + 1) +
                sanitizedValue.slice(firstPeriodIndex + 1).replace(/\./g, "");
        }

        const parts = sanitizedValue.split(".");
        parts[0] = parts[0].slice(0, 15);

        if (parts[1]) {
            parts[1] = parts[1].slice(0, 2);
        }

        return parts.join(".");
    };


    const calculatePercentageChange = (start, final) => {
        const startNum = parseFloat(start);
        const finalNum = parseFloat(final);

        if (!isNaN(startNum) && !isNaN(finalNum) && startNum !== 0) {
            const result = ((finalNum - startNum) / Math.abs(startNum)) * 100;
            setPercentageChange(formatPercentage(result));
        } else {
            setPercentageChange("0.00%");
        }
    };

    return (
        <>
            <div className="trade_calculators_card">
                <div className="trade_head mb-0 py-3 pb-4">
                    <h6>Percentage Increase Calculator</h6>
                    <CommonTooltip
                        className="CustomTooltip"
                        content="Use this tool to calculate the percentage increase between two values. Enter the starting and ending value, and the calculator will show the percentage change. Useful for tracking price movements, portfolio growth, or trade performance."
                        position="top-left"
                    >
                        <SolidInfoIcon />
                    </CommonTooltip>
                </div>
                <Row className="gx-2 gx-xl-3">
                    <Col xs={6}>
                        <div className="tradeacct">
                            <h6>Start Value</h6>
                            <input
                                type="text"
                                placeholder="$0.00"
                                className="w-full"
                                value={startValue === "" ? "" : `$${startValue}`}
                                onChange={(e) => {
                                    const numericValue = validateNumericInput(e.target.value);
                                    setStartValue(numericValue);
                                    calculatePercentageChange(numericValue, finalValue);
                                }}
                            />
                        </div>
                    </Col>

                    <Col xs={6}>
                        <div className="tradeacct">
                            <h6>Final Value</h6>
                            <input
                                type="text"
                                placeholder="$0.00"
                                className="w-full"
                                value={finalValue === "" ? "" : `$${finalValue}`}
                                onChange={(e) => {
                                    const numericValue = validateNumericInput(e.target.value);
                                    setFinalValue(numericValue);
                                    calculatePercentageChange(startValue, numericValue);
                                }}
                            />
                        </div>
                    </Col>

                    <Col xs={12} className="mt-10">
                        <div className="tradeacct value_change">
                            <h6>% Change</h6>
                            <p>{percentageChange}</p>
                        </div>
                    </Col>
                </Row>
            </div>
        </>
    );
}

'use client';
import { Col } from "react-bootstrap";
import React, { useState } from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { PaymentIconSvg, } from "@/assets/svgIcons/SvgIcon";
import "@/css/account/PaymentMethods.scss";

export default function Credits() {
    const [showRedeemForm, setShowRedeemForm] = useState(false);
    const [promoCode, setPromoCode] = useState('');
    const [status, setStatus] = useState('idle');
    const [appliedCode, setAppliedCode] = useState('');

    const VALID_CODE = "ABC123";

    const handleRedeem = () => {
        const code = promoCode.trim().toUpperCase();

        if (code === VALID_CODE) {
            setStatus('success');
            setAppliedCode(code);

            setTimeout(() => {
                setStatus('idle');
                setAppliedCode('');
            }, 3000);
        } else {
            setStatus('error');
            setAppliedCode(code);

            setTimeout(() => {
                setStatus('idle');
                setAppliedCode('');
            }, 3000);
        }

        setPromoCode('');
    };
    const handleCancel = () => {
        setShowRedeemForm(false);
        setPromoCode('');
        setAppliedCode('');
        setStatus('');
    };
    return (
        <>
            <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                <CommonBlackCard
                    title="TradeReply.com credits"
                    className="account_card"
                >
                    <div className="account_card_list">
                        <div className="row align-items-center justify-content-between">
                            <div className="col-5">
                                <ul>
                                    <li>
                                        <span className="text-white">Standard Credits</span>{" "}
                                        <span>$0.00</span>
                                    </li>
                                    <li>
                                        <span className="text-white">Refer a friend Credits</span>{" "}
                                        <span>$0.00</span>
                                    </li>
                                    <li>
                                        <span className="text-white">Total Credits</span>{" "}
                                        <span>$0.00</span>
                                    </li>
                                </ul>

                            </div>
                            <div className="col-4 payment_icon mt-4 mt-md-0">
                                {!showRedeemForm ? (
                                    <div className="flex justify-end">
                                        <button
                                            type="button"
                                            className="blue_text_btn flex items-center"
                                            onClick={() => setShowRedeemForm(true)}
                                        >
                                            <PaymentIconSvg /> Redeem a code
                                        </button>
                                    </div>
                                ) : (
                                    <div className="payment_redeem">
                                        <div className="d-flex align-items-center">
                                            <input
                                                type="text"
                                                className="form-control text-white"
                                                placeholder="Enter code"
                                                value={promoCode}
                                                onChange={(e) => setPromoCode(e.target.value)}
                                            />
                                        </div>

                                        <div className="mt-2 error-messages">
                                            {status === 'error' && (
                                                <p className="invalid">
                                                    Promo code <strong>{appliedCode || 'enter'}</strong> is invalid.
                                                </p>
                                            )}
                                            {status === 'success' && (
                                                <p className="success">
                                                    Promo code <strong>{appliedCode}</strong> has been applied to your account!
                                                </p>
                                            )}
                                        </div>

                                        <div className="flex justify-end gap-3 mt-3">
                                            <button className="btn-style white-btn" type="button" onClick={handleCancel}>
                                                Cancel
                                            </button>
                                            <button className="btn-style" type="button" onClick={handleRedeem}>
                                                Redeem Code
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}

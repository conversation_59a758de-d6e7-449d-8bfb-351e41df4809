@use "../theme/var";

.blog {
  position: relative;
  padding: 5rem 0;

  &_cards {
    margin-bottom: 50px !important;

    @media (max-width: 767px) {
      margin-bottom: 20px !important;
      // max-width: 500px;
      // margin: 0 auto;
    }

    @media (max-width: 575px) {
      margin-bottom: 10px !important;
      // max-width: 250px;
      // margin: 0 auto;
    }

    .slider-container {
      position: relative;

      .slick-slider {
        margin: 0 -15px;

        .slick-list {
          .slick-track {
            display: flex;

            .slick-slide {
              padding: 0 15px;
              display: flex;
              height: auto;

              &>div {
                width: 100%;
                display: flex;
              }

              &.slick-active {
                padding-top: 0;
              }
            }
          }
        }
      }
    }
  }

  &_postcard {
    position: relative;
    background-color: var.$clr032251;
    border-radius: 1.25rem;
    border: 1px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    height: 100%;

    @media (max-width: 767px) {
      margin-bottom: 1.25rem;
    }

    &:hover {
      .blog_postcard_img {
        img {
          transform: scale(1.1);
        }
      }
    }

    &_img {
      border-top-left-radius: 1.25rem;
      border-top-right-radius: 1.25rem;
      overflow: hidden;
      position: relative;

      &::before {
        content: "";
        background-color: hsla(0, 0%, 100%, 0.6);
        border-top-left-radius: 1.25rem;
        border-top-right-radius: 1.25rem;
        display: block;
        padding-top: 54.25%;
        width: 100%;
      }

      &_overlay {
        bottom: 0;
        display: block;
        height: 100%;
        left: 0;
        margin-left: auto;
        margin-right: auto;
        overflow: hidden;
        position: absolute;
        right: 0;
        top: 0;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-top-left-radius: 1.25rem;
        border-top-right-radius: 1.25rem;
        transition: all 0.3s ease-in-out;
      }
    }

    &_content {
      padding: 0.65rem 1.25rem 2rem;

      @media (max-width: 1199px) {
        padding: 0.65rem 1rem;
      }

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        line-height: 1.5rem;
        letter-spacing: -0.1px;
        margin-bottom: 0.625rem;
        color: var.$clrc5c5d5;

        @media (max-width: 1199px) {
          font-size: 1rem;
          line-height: 1.25rem;
        }
      }

      p {
        font-size: 1.5rem;
        font-weight: 400;
        line-height: 2rem;
        letter-spacing: -0.1px;
        color: var.$white;

        @media (max-width: 1199px) {
          font-size: 15px;
          line-height: 23px;
        }
      }
    }
  }


  .slider-container .slick-slider .slick-arrow.slick-prev {
    @media (max-width: 767px) {
      left: -45px !important;
      top: 100px !important;
    }

    &.slick-disabled {
      background-color: #414c60;
      opacity: 0.7;
    }
  }

  .slider-container .slick-slider .slick-arrow.slick-next {
    @media (max-width: 767px) {
      right: -30px !important;
      top: 100px !important;
    }

    &.slick-disabled {
      background-color: #414c60;
      opacity: 0.7;
    }
  }
}

.recent_post {
  position: relative;
  background-color: var.$clr032251;
  border-radius: 1.25rem;
  border: 1px solid rgba(8, 5, 5, 0.5);
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  cursor: pointer;
  width: 100%;

  h1 {
    margin-bottom: 50px;
    font-size: 3rem;
    font-weight: 800;

    @media (max-width: 767px) {
      margin-bottom: 30px;
    }
  }

  @media (max-width: 767px) {
    margin-bottom: 1.25rem;
  }

  &_img {
    border-top-left-radius: 1.25rem;
    border-bottom-left-radius: 1.25rem;
    overflow: hidden;
    position: relative;
    width: 366px;

    @media (max-width: 991px) {
      width: 40%;
    }

    @media (max-width: 599px) {
      width: 100%;
      border-top-left-radius: 1.25rem;
      border-top-right-radius: 1.25rem;
      border-bottom-left-radius: 0;

      &::before {
        content: "";
        background-color: hsla(0, 0%, 100%, 0);
        border-top-left-radius: 1.25rem;
        border-top-right-radius: 1.25rem;
        display: block;
        padding-top: 54.25%;
        width: 100%;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;

      @media (max-width: 767px) {
        bottom: 0;
        display: block;
        height: 100%;
        left: 0;
        margin-left: auto;
        margin-right: auto;
        overflow: hidden;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }

  &_content {
    width: calc(100% - 366px);
    padding: 2rem 1.25rem;
    word-wrap: break-word; // Ensure long words break and wrap
    overflow-wrap: break-word; // Ensure content wraps to the next line

    @media (max-width: 991px) {
      width: 60%;
      padding: 1rem 1.25rem;
    }

    @media (max-width: 599px) {
      width: 100%;
      padding: 2rem 1rem;
    }

    small {
      display: block;
    }

    h4 {
      margin-block: 0.625rem;
    }

    p,
    small {
      font-size: 1.125rem;
      font-weight: 400;
      line-height: 27px;
      color: var.$clrc5c5d5;

      @media (max-width: 767px) {
        font-size: 1rem;
      }
    }
  }

  &_time {
    color: var.$baseclr;
    font-size: 1rem;
    font-weight: 600;
    line-height: 26px;
    display: block;
    margin-top: 10px;

    @media (max-width: 767px) {
      font-size: 0.9rem;
    }
  }
}

.recontPostTitle {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;

  @media (max-width: 767px) {
    font-size: 1.5rem;
    margin-bottom: 30px;
  }
}

.slick-slider .slick-prev,
.slick-slider .slick-next {
  position: absolute !important;
  top: 50%;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
  -webkit-transform: translate(0, -50%) !important;
  -ms-transform: translate(0, -50%) !important;
  transform: translateY(-50%) !important;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}
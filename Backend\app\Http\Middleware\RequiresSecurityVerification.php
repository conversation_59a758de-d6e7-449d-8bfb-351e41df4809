<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\SecurityVerificationService;

class RequiresSecurityVerification
{
    protected $securityService;

    public function __construct(SecurityVerificationService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Get secure pages from hardcoded list (frontend handles route configuration)
     */
    protected function getSecurePages(): array
    {
        // Note: Frontend now handles route configuration, this is just for backend middleware
        return [
            '/account/phone/setup',
            // Add other backend-protected routes here if needed
        ];
    }

    /**
     * Validate that a URL path is a valid secure route
     */
    protected function isValidSecureRoute(string $path): bool
    {
        // Decode URL if it's encoded
        $decodedPath = urldecode($path);

        // Extract just the path part (remove query parameters for validation)
        $pathOnly = parse_url($decodedPath, PHP_URL_PATH) ?: $decodedPath;

        // Remove leading slash if present for consistent comparison
        $pathOnly = ltrim($pathOnly, '/');

        $securePages = $this->getSecurePages();

        foreach ($securePages as $securePage) {
            // Remove leading slash from secure page for comparison
            $securePagePath = ltrim($securePage, '/');

            // Check if the path matches exactly or starts with the secure page path
            if ($pathOnly === $securePagePath || str_starts_with($pathOnly, $securePagePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only apply to secure pages
        if (!$this->isSecurePage($request->path())) {
            return $next($request);
        }

        // Check for direct URL access (referrer-based access control)
        if (!$this->hasValidNavigationReferrer($request)) {
            $currentPath = '/' . ltrim($request->path(), '/');
            $fallbackUrl = $this->getDirectAccessFallbackUrl($currentPath);

            \Log::info('Direct URL access detected - redirecting to fallback', [
                'attempted_path' => $currentPath,
                'fallback_url' => $fallbackUrl,
                'user_id' => auth()->id(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('Referer') ?: 'none'
            ]);

            return redirect()->to($fallbackUrl)->with('direct_access_blocked',
                'Please use the navigation menu to access this page.');
        }

        // Get cookie configuration and check for security verification cookie
        $cookieConfig = $this->securityService->getCookieConfig();
        $cookieValue = $request->cookie($cookieConfig['name']);

        // OPTIMIZATION: Check if security verification cookie is valid first
        // If valid, allow direct access to the secure route without redirecting
        if ($cookieValue && $this->isValidSecurityCookie($cookieValue)) {
            \Log::debug('Security verification passed - valid cookie found', [
                'path' => $request->path(),
                'user_id' => auth()->id(),
                'cookie_exists' => true
            ]);

            return $next($request);
        }

        // Security verification required - cookie is missing, invalid, or expired
        $user = auth()->user();
        // Use path + query string instead of full URL to avoid domain issues
        $nextUrl = $request->getRequestUri();

        \Log::info('Security verification required', [
            'path' => $request->path(),
            'request_uri' => $nextUrl,
            'user_id' => $user->id ?? null,
            'cookie_exists' => !!$cookieValue,
            'has_completed_verification_before' => $user && $user->first_security_verification_at ? true : false
        ]);

        // Scenario 1: User has never completed security verification
        if (!$user || !$user->first_security_verification_at) {
            // Validate the next URL before redirecting
            if ($this->isValidSecureRoute($nextUrl)) {
                \Log::info('First-time security verification required - redirecting to security checkup', [
                    'user_id' => $user->id ?? null,
                    'intended_url' => $nextUrl,
                    'validation_status' => 'valid'
                ]);
                // Redirect to security check with next parameter for first-time users
                return redirect()->to('/security-check?next=' . urlencode($nextUrl));
            } else {
                \Log::warning('Invalid next URL detected in security verification redirect', [
                    'user_id' => $user->id ?? null,
                    'invalid_url' => $nextUrl,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]);
                // Redirect to security check without next parameter for invalid URLs
                return redirect()->to('/security-check');
            }
        }

        // Scenario 2: User has completed verification before but cookie expired
        \Log::info('Security cookie expired for returning user - redirecting to previous page', [
            'user_id' => $user->id,
            'first_verification_at' => $user->first_security_verification_at,
            'intended_url' => $nextUrl,
            'referrer' => $request->header('Referer')
        ]);

        // For users with expired cookies, redirect back to a safe page
        $referrer = $request->header('Referer');
        $redirectUrl = '/account/overview'; // Default fallback

        // If there's a valid referrer from the same domain, use it
        if ($referrer && $this->isValidReferrer($referrer)) {
            try {
                $parsedUrl = parse_url($referrer);
                if (isset($parsedUrl['path'])) {
                    $redirectUrl = $parsedUrl['path'];
                    if (isset($parsedUrl['query'])) {
                        $redirectUrl .= '?' . $parsedUrl['query'];
                    }
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to parse referrer URL', [
                    'referrer' => $referrer,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return redirect()->to($redirectUrl)->with('security_expired', 'Your security session has expired. Please try again.');
    }

    /**
     * Check if the current path is a secure page
     */
    protected function isSecurePage(string $path): bool
    {
        $securePages = $this->getSecurePages();

        foreach ($securePages as $securePage) {
            if (str_starts_with('/' . $path, $securePage)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate the security verification cookie using enhanced security service
     */
    protected function isValidSecurityCookie($cookieValue): bool
    {
        if (!$cookieValue) {
            return false;
        }

        // Use the security service for validation with cryptographic verification
        $payload = $this->securityService->validateSecureCookieValue($cookieValue);

        if (!$payload) {
            \Log::warning('Invalid security cookie detected', [
                'user_id' => auth()->id(),
                'cookie_preview' => substr($cookieValue, 0, 20) . '...',
                'ip_address' => request()->ip()
            ]);
            return false;
        }

        // Additional validation: check if user_id matches current user
        if (auth()->check() && isset($payload['user_id'])) {
            if ($payload['user_id'] != auth()->id()) {
                \Log::warning('Security cookie user mismatch', [
                    'cookie_user_id' => $payload['user_id'],
                    'current_user_id' => auth()->id(),
                    'ip_address' => request()->ip()
                ]);
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the user arrived via legitimate navigation (not direct URL access)
     */
    protected function hasValidNavigationReferrer(Request $request): bool
    {
        // Note: Frontend now handles referrer-based access control
        // Backend middleware just checks for basic referrer presence
        $referrer = $request->header('Referer');

        // No referrer indicates direct URL access (typed in browser, bookmark, etc.)
        if (!$referrer) {
            return false;
        }

        // Validate that referrer is from the same domain and is a valid application page
        return $this->isValidApplicationReferrer($referrer);
    }

    /**
     * Check if the referrer is from a valid application page
     */
    protected function isValidApplicationReferrer(string $referrer): bool
    {
        try {
            $parsedUrl = parse_url($referrer);

            // Remove domain checking logic to ensure consistent behavior across environments

            // Get the referrer path
            $referrerPath = $parsedUrl['path'] ?? '/';

            // Don't allow referrers from invalid pages (hardcoded for backend)
            $invalidReferrerPages = [
                '/login', '/signup', '/forget-password', '/verify-email',
                '/security-check', '/logout'
            ];

            foreach ($invalidReferrerPages as $invalidPage) {
                if (str_starts_with($referrerPath, $invalidPage)) {
                    return false;
                }
            }

            // Allow referrers from valid application pages (hardcoded for backend)
            $validReferrerPrefixes = [
                '/account', '/dashboard', '/user', '/marketplace',
                '/pricing', '/help', '/settings', '/'
            ];

            foreach ($validReferrerPrefixes as $validPrefix) {
                if (str_starts_with($referrerPath, $validPrefix)) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            \Log::warning('Failed to parse referrer URL for navigation validation', [
                'referrer' => $referrer,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get the appropriate fallback URL for direct access prevention
     */
    protected function getDirectAccessFallbackUrl(string $securePagePath): string
    {
        // Hardcoded fallback mappings for backend (frontend handles its own)
        $fallbackMap = [
            '/account/phone/setup' => '/account/overview',
            // Add other backend fallback mappings here if needed
        ];

        $defaultFallback = '/account/overview';

        return $fallbackMap[$securePagePath] ?? $defaultFallback;
    }

    /**
     * Check if the referrer URL is valid and safe for redirection
     */
    protected function isValidReferrer(string $referrer): bool
    {
        // Don't redirect back to security-check page
        if (str_contains($referrer, '/security-check')) {
            return false;
        }

        // Don't redirect to auth pages
        $authPages = ['/login', '/signup', '/forget-password', '/verify-email'];
        foreach ($authPages as $authPage) {
            if (str_contains($referrer, $authPage)) {
                return false;
            }
        }

        // For relative URLs, allow them
        if (!str_starts_with($referrer, 'http')) {
            return true;
        }

        // For absolute URLs, check if they're from the same domain
        try {
            $parsedUrl = parse_url($referrer);
            $currentHost = request()->getHost();

            if (isset($parsedUrl['host']) && $parsedUrl['host'] !== $currentHost) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}

'use client';

import CommonSearch from "@/Components/UI/CommonSearch";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import { debounce } from "lodash";
import Link from "next/link"; // Import Next.js Link
import { useCallback, useState } from "react";

const CommonBlackCard = ({
  title,
  text,
  onclick,
  link = "#",
  Linktext,
  Linkicon,
  children,
  className,
  editicon,
  tradeacct,
  searcbar,
  subtext,
  submit,
  onSearchChange,
  searchPlaceholder,
}) => {

  const [searchKeyword, setSearchKeyword] = useState('');
  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchKeyword(searchTerm);
      onSearchChange(searchTerm);
    }, 300),
    []
  );

  const handleHomeSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  return (
    <>
      <div className={`common_blackcard ${className}`}>
        <div className="common_blackcard_innerheader">
          <div className="common_blackcard_innerheader_content">
            <h6>{title}</h6>
            <p>{text}</p>
            {searcbar && (
              <div className="common_blackcard_innerheader_search my-3">
                <CommonSearch
                  icon={true}
                  placeholder={searchPlaceholder}
                  onChange={handleHomeSearch}
                  name={"accountConnection"}
                />
              </div>
            )}
            {subtext && <h6>{subtext}</h6>}
            {submit && (
              <button className="blue_text_btn px-0 mt-3 d-flex align-items-center gap-2">
                Submit a Request <RightArrowIcon />
              </button>
            )}
          </div>
          <div className="common_blackcard_innerheader_icon">
            <Link href={link} prefetch={true}>
              <button className="d-flex align-items-center">
                {editicon && <span className="me-2">{editicon}</span>}
                {Linktext}
                {Linkicon && <span className="ms-2">{Linkicon}</span>}
              </button>
            </Link>
            {tradeacct && (
              <div className="common_blackcard_innerheader_tradeacct">
                <h6>Trade Account</h6>
                <p>acct-123</p>
              </div>
            )}
          </div>
        </div>
        <div className="common_blackcard_innerbody">{children}</div>
      </div>
    </>
  );
};

export default CommonBlackCard;

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ArticleProgressRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'progress' => 'required|integer|min:0|max:100',
        ];
    }

    public function messages()
    {
        return [
            'progress.required' => 'Progress is required.',
            'progress.integer'  => 'Progress must be a valid number.',
            'progress.min'      => 'Progress must be at least 0%.',
            'progress.max'      => 'Progress cannot exceed 100%.',
        ];
    }
}

<?php

namespace App\Console\Commands;

use App\Models\SecurityVerificationAttempt;
use App\Models\SecurityVerificationSession;
use Illuminate\Console\Command;

class CleanupSecurityVerificationSessions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:cleanup-verification-sessions
                            {--days=7 : Number of days to keep verification sessions}
                            {--attempts-days=30 : Number of days to keep verification attempts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired security verification sessions and old attempts';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sessionDays = $this->option('days');
        $attemptsDays = $this->option('attempts-days');

        $this->info("Starting cleanup of security verification data...");

        // Clean up expired sessions
        $expiredSessions = SecurityVerificationSession::cleanupExpired();
        $this->info("Cleaned up {$expiredSessions} expired verification sessions");

        // Clean up old completed/verified sessions (older than specified days)
        $oldSessions = SecurityVerificationSession::where('created_at', '<', now()->subDays($sessionDays))->delete();
        $this->info("Cleaned up {$oldSessions} old verification sessions (older than {$sessionDays} days)");

        // Clean up old verification attempts
        $oldAttempts = SecurityVerificationAttempt::cleanupOldAttempts($attemptsDays);
        $this->info("Cleaned up {$oldAttempts} old verification attempts (older than {$attemptsDays} days)");

        $this->info("Security verification cleanup completed successfully!");

        return Command::SUCCESS;
    }
}

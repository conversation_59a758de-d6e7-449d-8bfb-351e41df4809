<?php

namespace App\Console\Commands;

use Database\Factories\UniversalFactory;
use Illuminate\Console\Command;

class SeedModel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:model {model} {--count=1} {--update}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed or update a given model dynamically using the Universal Factory.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $modelName = $this->argument('model');
        $count = (int) $this->option('count');
        $update = $this->option('update');

        // Convert model name to full namespace
        $modelClass = "App\\Models\\$modelName";

        if (!class_exists($modelClass)) {
            $this->error("Model '$modelClass' not found.");
            return;
        }

        $factory = new UniversalFactory($modelClass);

        if ($update) {
            // Update existing records
            $updatedCount = $factory->updateExisting();
            $this->info("$updatedCount records have been updated for $modelName.");
        } else {
            // Create new records
            $factory->createMany($count);
            $this->info("$count records have been seeded for $modelName.");
        }
    }
}

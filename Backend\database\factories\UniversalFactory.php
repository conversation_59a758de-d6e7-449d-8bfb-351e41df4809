<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class UniversalFactory extends Factory
{
    protected $model;
    protected $table;
    protected $fillable;

    /**
     * Initialize factory with a model instance.
     */
    public function __construct($modelClass)
    {
        parent::__construct();
        $this->model = new $modelClass;
        $this->table = $this->model->getTable();
        $this->fillable = $this->model->getFillable();
    }

    /**
     * Define the model's default state.
     */
    public function definition()
    {
        $fakeData = [];

        $modelInstance = new $this->model;
        $table = $modelInstance->getTable();
        $fillable = $modelInstance->getFillable();

        foreach ($fillable as $column) {
            if (Schema::hasColumn($table, $column)) { // Ensure column exists
                $type = Schema::getColumnType($table, $column);
                $length = $this->getColumnLength($table, $column); // ✅ Fetch length

                $fakeData[$column] = $this->generateFakeValue($column, $type, $length);
            }
        }

        return $fakeData;
    }

    /**
     * Generate fake values based on column datatype.
     */
    private function generateFakeValue($column, $type, $length = null)
    {
        return match (strtolower($type)) {
            // ✅ String-related types (Handles name, email, usernames, passwords, and generic text)
            'string', 'varchar', 'char', 'nvarchar' => match (true) {
                Str::contains($column, ['email']) => $this->faker->unique()->safeEmail,
                Str::contains($column, ['password']) => bcrypt('password'), // Default hashed password
                Str::contains($column, ['username']) => $this->faker->userName,
                Str::contains($column, ['uuid']) => Str::uuid(),
                Str::contains($column, ['token', 'key']) => Str::random(min($length ?? 32, 255)), // Token length check
                Str::contains($column, ['phone', 'mobile', 'contact']) => $this->faker->phoneNumber,
                Str::contains($column, ['address', 'location']) => $this->faker->address,
                default => $this->faker->text(min($length ?? 255, 255)), // Ensuring max length constraints
            },

            // ✅ Text-based types (Handles long-form text like descriptions, comments)
            'text', 'tinytext', 'mediumtext', 'longtext' => $this->faker->paragraphs(rand(1, 3), true),

            // ✅ Integer & Numeric types (Handles IDs properly)
            'integer', 'int', 'bigint', 'tinyint', 'smallint', 'mediumint', 'unsignedBigInteger' => match (true) {
                Str::endsWith($column, '_id') => $this->getForeignKeyValue($column),
                Str::contains($column, ['age']) => $this->faker->numberBetween(18, 60),
                Str::contains($column, ['quantity', 'stock']) => $this->faker->numberBetween(1, 100),
                Str::contains($column, ['rank', 'position']) => $this->faker->numberBetween(1, 10),
                default => $this->faker->randomNumber(min($length ?? 10, 10)), // Ensuring max digits
            },

            // ✅ Decimal & Float types (Handles price, amount, cost, ratings)
            'decimal', 'double', 'float' => match (true) {
                Str::contains($column, ['price', 'amount', 'cost']) => $this->faker->randomFloat(2, 10, 1000),
                Str::contains($column, ['rating', 'score']) => $this->faker->randomFloat(1, 1, 5),
                default => $this->faker->randomFloat(2, 1, 100),
            },

            // ✅ Boolean
            'boolean' => (bool) rand(0, 1),

            // ✅ Date & Time Types
            'datetime', 'timestamp' => now(),
            'date' => $this->faker->date(),
            'time' => $this->faker->time(),

            // ✅ JSON Columns (Assuming array data)
            'json' => json_encode([$this->faker->word => $this->faker->sentence]),

            // ✅ Fallback for unknown types
            default => $this->faker->word,
        };
    }


    /**
     * Create multiple records.
     */
    public function createMany($count = 1, array $overrides = [])
    {
        $data = [];

        for ($i = 0; $i < $count; $i++) {
            $data[] = array_merge($this->definition(), $overrides);
        }

        return $this->model::insert($data);
    }

    /**
     * Update existing records.
     */
    public function updateExisting()
    {
        $records = $this->model::all(); // Get all records
        $updatedCount = 0;

        foreach ($records as $record) {
            $record->update($this->definition());
            $updatedCount++;
        }

        return $updatedCount;
    }

    private function getColumnLength($table, $column)
    {
        $database = config('database.connections.mysql.database');

        $result = DB::selectOne("
        SELECT CHARACTER_MAXIMUM_LENGTH AS length
        FROM information_schema.columns
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?
    ", [$database, $table, $column]);

        return $result->length ?? null; // Return length or null if not found
    }

    private function getForeignKeyValue($column)
    {
        $relatedModel = Str::studly(Str::before($column, '_id')); // Convert to Model Name

        $modelClass = "App\\Models\\{$relatedModel}"; // Assuming all models are in `App\Models\`

        if (class_exists($modelClass)) {
            return $modelClass::inRandomOrder()->value('id') ?? 1; // Get a random ID or default to 1
        }

        return 1; // Fallback if model doesn't exist
    }
}

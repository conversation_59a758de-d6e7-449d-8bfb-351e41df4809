@use "../theme/var" as *;

.featured_resources {
  background-color: $clr031940;
  padding: 3rem;
  padding-bottom: 100px;

  @media screen and (max-width: 767px) {
    padding: 3rem 0;
    font-size: 1rem;
  }

  &_heading {
    margin-bottom: 70px;

    @media screen and (max-width: 767px) {
      margin-bottom: 30px;
      text-align: center;
    }
  }

  &_content {
    @media screen and (max-width: 767px) {
      text-align: center;
    }

    p {
      font-size: 21px;
      font-weight: 600;
      line-height: 34px;
      margin: 1.5rem 0 2rem;
      color: $white;

      @media screen and (max-width: 767px) {
        font-size: 16px;
        line-height: 24px;
      }
    }

    h3 {

      font-weight: 800;
      width: 100%;
      font-size: 3rem;

      @media (max-width: 991px) {
        font-size: 35px;
      }

      @media (max-width: 767px) {
        font-size: 1.5rem;
      }
    }
  }
}
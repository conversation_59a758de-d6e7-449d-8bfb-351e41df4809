import React, { useEffect, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CommonTooltip from '@/Components/UI/CommonTooltip';

const RiskInputRow = ({
  label,
  summary,
  value,
  placeholder,
  options = [],
  onChange,
  data_type,
  hasFormula,
  databaseField,
}) => {
  const [inputValue, setInputValue] = useState(value ?? '');
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    setInputValue(value ?? '');
  }, [value]);

  const formatDisplay = (val) => {
    if (val === '' || val === null || val === undefined) return val;

    const strVal = val.toString();

    if (data_type === 'Currency' && !strVal.trim().startsWith('$')) {
      return `$${strVal}`;
    }

    if (data_type?.toLowerCase().includes('percentage') && !strVal.includes('%')) {
      return `${strVal}%`;
    }

    if (data_type?.toLowerCase().includes('ratio') && !strVal.toLowerCase().endsWith('x')) {
      return `${strVal}X`;
    }

    return strVal;
  };

  const parseValue = (val) => {
    if (data_type === 'Currency') return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('percentage')) return val.replace(/[^0-9.]/g, '');
    if (data_type?.toLowerCase().includes('ratio')) return val.replace(/[^0-9.]/g, '');
    return val;
  };

  const handleChange = (val) => {
    const raw = parseValue(val);
    setInputValue(raw);
    if (!hasFormula) {
      onChange(raw);
    }
    let cleaned = val.replace(/[^0-9.]/g, '');
    setInputValue(cleaned);
  };

  const isForcedInput = databaseField === 'PORTFOLIO_MAX_RISK_TOLERANCE';
  const showAsInput = hasFormula || isForcedInput || placeholder !== 'Select';

  return (
    <Row className="mt-2 gx-2">
      <Col md={9} xs={7}>
        <div className="blueCard">
          <p>{label}</p>
          <CommonTooltip
            className="subTooltip"
            content={
              <>
                <p>{summary}</p>
              </>
            }
            position="top-right"
          >
            <SolidInfoIcon />
          </CommonTooltip>
        </div>
      </Col>
      <Col md={3} xs={5}>
        <div className={hasFormula ? "DisabledCard" : "whiteCard"} >
          {showAsInput ? (
            <input
              type="text"
              title={formatDisplay(inputValue)}
              value={
                (data_type?.toLowerCase().includes('percentage') || data_type?.toLowerCase().includes('ratio'))
                  ? (isFocused ? inputValue : formatDisplay(inputValue))
                  : formatDisplay(inputValue)
              }
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              onChange={(e) => handleChange(e.target.value)}
              placeholder={
                (hasFormula && (!inputValue || inputValue === '0')) ? 'No Data' :
                  placeholder || 'Enter value'
              }
              disabled={hasFormula}
            />
          ) : (
            <select
              title={inputValue}

              value={inputValue}
              onChange={(e) => handleChange(e.target.value)}
              disabled={hasFormula}
            >
              <option value="" disabled>Select</option>
              {options.map((option, index) => (
                <option key={index} value={option}>{option}</option>
              ))}
            </select>
          )}
        </div>
      </Col>
    </Row>
  );
};

export default function RiskManagement({ data = [], onChangeField }) {
  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Risk Management</p>
      </div>

      {data.map((field, index) => (
        <RiskInputRow
          key={index}
          label={field.label ?? field.database_field}
          summary={field?.summary}
          value={field.value ?? ''}
          placeholder={field.account_field_placeholder}
          options={field.options ?? [field.account_field_value]}
          data_type={field.data_type}
          hasFormula={field.hasFormula}
          databaseField={field.database_field}
          onChange={(newValue) => onChangeField(field.id, newValue)}
        />
      ))}
    </div>
  );
}

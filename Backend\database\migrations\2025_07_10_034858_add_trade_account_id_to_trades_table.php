<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trades', function (Blueprint $table) {
            $table->unsignedInteger('account_trade_id')->after('is_published')->nullable();
            $table->unsignedBigInteger('trade_account_id')->after('account_trade_id')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trades', function (Blueprint $table) {
            $table->dropColumn('account_trade_id');
            $table->dropColumn('trade_account_id');
            $table->dropSoftDeletes();
        });
    }
};

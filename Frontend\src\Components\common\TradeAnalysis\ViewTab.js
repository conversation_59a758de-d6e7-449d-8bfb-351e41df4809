import React, { useState } from "react";
import {
  WhiteDoubleStack,
  WhiteTripleStack,
  MinusIcon,
  PlusIcon,
} from "@/assets/svgIcons/SvgIcon";
import Table from "react-bootstrap/Table";
import useMediaQuery from "@/Hooks/useMediaQuery";

export default function ViewTab() {
  const [isComparison, setComparison] = useState(false);
  const [isCollapseIndex, setCollapseIndex] = useState(null);

  const viewTblData = [
    {
      id: 1,
      mix: "Totals",
      comparison: "% Change",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
    {
      id: 2,
      mix: "",
      comparison: "Mar 10 - Mar 16, 2025",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
    {
      id: 3,
      mix: "",
      comparison: "Mar 10 - Mar 16, 2025",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
    {
      id: 4,
      mix: "2.5 %",
      comparison: "% Change",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
    {
      id: 2,
      mix: "",
      comparison: "Mar 10 - Mar 16, 2025",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
    {
      id: 3,
      mix: "",
      comparison: "Mar 10 - Mar 16, 2025",
      first: "+ 40.69%",
      second: "+ 40.69%",
      third: "+ 40.69%",
    },
  ];
  const viewSmTblData = [
    {
      id: 4,
      title: "Dimension 1",
      firstArray: [
        {
          mix: "Totals",
          secondArray: [
            {
              scope: "Profit Scope: Transaction 1",
              value: "+ 40.69%",
            },
            {
              scope: "Profit Scope: Transaction 2",
              value: "+ 40.69%",
            },
          ],
        },
        {
          mix: "2.5%",
          secondArray: [
            {
              scope: "Profit Scope: Transaction 1",
              value: "+ 40.69%",
            },
            {
              scope: "Profit Scope: Transaction 2",
              value: "+ 40.69%",
            },
          ],
        },
      ],
    },
  ];

  const isMobile = useMediaQuery("(max-width: 767px)");

  const handleToggleCollapse = (index) => {
    setCollapseIndex((prev) => (prev === index ? null : index));
  };

  return (
    <div className="trade_analysis_view">
      {!isMobile ? (
        <Table className="large_tabel_view" responsive="lg">
          <thead>
            <tr>
              <th>
                Max Risk Percentage <p>Scope: Trade</p>
              </th>
              <th>Data Comparison</th>
              <th>
                Profit <p>Scope: Transaction</p>
              </th>
              <th>
                Profit <p>Scope: Transaction</p>
              </th>
              <th>
                Profit <p>Scope: Transaction</p>
              </th>
            </tr>
          </thead>
          <tbody>
            {viewTblData.length > 0 ? (
              viewTblData.map((data, index) => (
                <tr key={index}>
                  <td>{data.mix}</td>
                  <td>{data.comparison}</td>
                  <td>{data.first}</td>
                  <td>{data.second}</td>
                  <td>{data.third}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="text-center">
                  No Data
                </td>
              </tr>
            )}
          </tbody>
        </Table>
      ) : (
        <div className="mini_screen">
          {viewSmTblData.map((data, index) => (
            <React.Fragment key={index}>
              <div className="firstHeader">{data.title}</div>
              <div className="collapse_header">
                {data.firstArray.map((dati, subIndex) => {
                  const isActive = isCollapseIndex === subIndex;

                  return (
                    <React.Fragment key={subIndex}>
                      <div
                        className="secondHeader"
                        onClick={() => handleToggleCollapse(subIndex)}
                      >
                        <WhiteDoubleStack />
                        {dati.mix}
                        {isActive ? (
                          <MinusIcon height={"3px"} width={"19px"} />
                        ) : (
                          <PlusIcon height={"21px"} width={"21px"} />
                        )}
                      </div>

                      {isActive && (
                        <>
                          {isComparison ? (
                            <Table>
                              <thead>
                                <tr>
                                  <th>Data Comparison</th>
                                  <th>
                                    <WhiteTripleStack />
                                    Profit Scope: Transaction 1
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                {dati.secondArray.length > 0 ? (
                                  dati.secondArray.map((datii, index) => (
                                    <tr key={index}>
                                      <td>{datii.scope}</td>
                                      <td>{datii.value}</td>
                                    </tr>
                                  ))
                                ) : (
                                  <tr>
                                    <td colSpan={2} className="text-center">
                                      No Data
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </Table>
                          ) : (
                            dati.secondArray.map((datii, innerIndex) => (
                              <React.Fragment key={innerIndex}>
                                <div className="thirdHeader">{datii.scope}</div>
                                <div className="firstValue">{datii.value}</div>
                              </React.Fragment>
                            ))
                          )}
                        </>
                      )}
                    </React.Fragment>
                  );
                })}
              </div>
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  );
}

import React from 'react'
import {
    White<PERSON>rossIcon,
    ThumbDownIcon,
    ThumbUpIcon,
    ProfileUserDarkIcon,
    RatingStarIcon,
    RightArrowIconSvg
} from "@/assets/svgIcons/SvgIcon";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";


export default function MarketFeedListPopup({ title, closeModal, feedbackList }) {
    const renderStars = (rating) => {
        const maxStars = 5;
        const filledStars = Math.round(rating);

        return Array.from({ length: maxStars }, (_, index) =>
            index < filledStars ? (
                <RatingStarIcon key={index} />
            ) : (
                <span key={index} style={{ opacity: 0.2 }}>
                    <RatingStarIcon />
                </span>
            )
        );
    };
    return (
        <div className="modal_overlay">
            <div className="modal-body">
                <div className='d-flex justify-content-between align-items-center mb-4'>
                    <h4 className='mb-0'>{title}</h4>
                    <button onClick={closeModal}>
                        <WhiteCrossIcon />
                    </button>
                </div>
                <CommonWhiteCard
                    title="Marketplace Seller Feedback"
                    className="account_card"
                >
                    <div className="account_card_marketplace">
                        <div className="main_inform justify-between">
                            <div className="d-flex gap-1 align-items-center">
                                {renderStars(feedbackList.length)}
                                <span>{feedbackList.length}</span>
                            </div>
                            <span className="most_recent text-end">Most recent reviews</span>
                        </div>

                        {feedbackList.map((feedback, index) => (
                            <div className="mini_card" key={index}>
                                <div className="main_inform">
                                    <div className="profile_photo">
                                        <ProfileUserDarkIcon />
                                    </div>
                                    <div className="main_inform justify-between w-100">
                                        <div>
                                            <p className="small_tag">{feedback.reviewer}</p>
                                            <h6>Purchased courses & webinars totalling {feedback.amount}</h6>
                                        </div>
                                        <div>
                                            <div className="d-flex gap-1">
                                                {renderStars(feedback.stars)}
                                            </div>
                                            <p className="time">{feedback.timeAgo}</p>
                                        </div>
                                    </div>
                                </div>
                                <h6 className="mini_sc_title">
                                    Purchased courses & webinars totalling {feedback.amount}
                                </h6>

                                <div className="main_inform mt-3">
                                    {feedback.thumbs === "up" ? <ThumbUpIcon /> : <ThumbDownIcon />}
                                    <p className="thumbs_text">{feedback.message}</p>
                                </div>
                            </div>
                        ))}
                    </div>
                </CommonWhiteCard>
            </div>
        </div >
    )
}

"use client";

import React, { useRef, useEffect, useState } from "react";
import dynamic from "next/dynamic";
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });

const JoditEditorComponent = ({ value, onChange }) => {
  const editor = useRef(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true); 
  }, []);

  const config = {
    readonly: false,
    height: 400,
    toolbarSticky: true,
    toolbarAdaptive: false,
  };

  return isClient ? (
    <JoditEditor
      ref={editor}
      value={value} 
      config={config}
      onBlur={(newContent) => onChange(newContent)} 
    />
  ) : (
    <p>Loading Editor...</p> 
  );
};

export default JoditEditorComponent;

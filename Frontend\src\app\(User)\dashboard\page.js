
'use client';
import React, { useState } from "react";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { Container } from "react-bootstrap";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import { SettingIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import DashboardCards from "./Partials/DashboardCards";
import SettingWheel from "@/Components/common/Dashboard/SettingWheel/SettingWheel";

const Dashboard = () => {
  const [isSettingWheelPopup, setSettingWheelPopup] = useState(false);

  const openSettingWheelPopup = () => setSettingWheelPopup(true);
  const closeSettingWheelPopup = () => setSettingWheelPopup(false);

  const metaArray = {
    noindex: true,
    title: "Dashboard | Analyze & Optimize Your Trades | TradeReply",
    description: "Access your TradeReply Dashboard to analyze and optimize your trades. View analytics, apply filters, and manage multiple dashboards tailored to your trading needs.",
    canonical_link: "https://www.tradereply.com/dashboard",
    og_site_name: "TradeReply",
    og_title: "Dashboard | Analyze & Optimize Your Trades | TradeReply",
    og_description: "Access your TradeReply Dashboard to analyze and optimize your trades. View analytics, apply filters, and manage multiple dashboards tailored to your trading needs.",
    twitter_title: "Dashboard | Analyze & Optimize Your Trades | TradeReply",
    twitter_description: "Access your TradeReply Dashboard to analyze and optimize your trades. View analytics, apply filters, and manage multiple dashboards tailored to your trading needs."
  };

  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <div className="dashboard">
        <CommonHead />
        <Container>
          <div className="trade_head custom_trade_head mb-0 py-10">
            <AdminHeading heading="Account Overview" centered />
            <button
              className="setting_btn"
              type="button"
              onClick={openSettingWheelPopup}
              style={isSettingWheelPopup ? { filter: "brightness(0.2) invert(1)" } : {}}
            >
              <SettingIcon />
            </button>
          </div>
          {isSettingWheelPopup && (
            <SettingWheel
              hideSetting={closeSettingWheelPopup}
            />
          )}
          {!isSettingWheelPopup && (
            <DashboardCards />
          )}
        </Container>
      </div>
    </DashboardLayout >
  );
};

export default Dashboard;
"use client";
import React, { useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { RightArrowIconSvg, ProfileUserDarkIcon } from "@/assets/svgIcons/SvgIcon";
import { usePathname } from "next/navigation";
import Link from "next/link";

export default function FollowingList() {
  const pathname = usePathname();
  const linkProps =
    pathname !== "/account/following"
      ? {
        Linktext: "View all",
        Linkicon: <RightArrowIconSvg />,
        link: "/account/following",
      }
      : null;

  const [followingList, setFollowingList] = useState([
    { name: "<PERSON>" },
    { name: "<PERSON>" },
    { name: "<PERSON>" },
  ]);

  return (
    <CommonWhiteCard
      title="Following (3)"
      {...linkProps}
      className="account_card"
    >
      <div className="account_card_following">
        {followingList.map((data, index) => (
          <div className="main_inform" key={index}>
            {pathname == "/account/following" ? (
              <div
                className="unFollow_status bg-danger text-white"
                onClick={() =>
                  setFollowingList((prev) => prev.filter((_, i) => i !== index))
                }
              >
                Unfollow
              </div>
            ) : null}
            <div className="profile_photo">
              <ProfileUserDarkIcon />
            </div>
            <Link href={`/@${data.name}`} legacyBehavior passHref>
              <a
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: "#00adef",
                }}
              >
                <h6>{data.name}</h6>
              </a>
            </Link>
          </div>
        ))}
      </div>
    </CommonWhiteCard>
  );
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PortfolioFieldDefinition extends Model
{
    use HasFactory;

    protected $table = 'portfolio_field_definitions';
    protected $fillable = [
        'field_definition_id',
        'database_field',
        'summary',
        'account_field',
        'account_field_value',
        'account_field_placeholder',
        'has_formula'
    ];

    public function fieldDefinition()
    {
        return $this->belongsTo(FieldDefinition::class);
    }

    public function userFieldValue()
    {
        return $this->hasOne(UserPortfolioField::class)
            ->where('user_id', auth()->id());
    }
}

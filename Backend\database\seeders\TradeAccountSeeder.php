<?php

namespace Database\Seeders;

use App\Models\TradeAccount;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TradeAccountSeeder extends Seeder
{
    public function run()
    {
        $users = User::all();

        foreach ($users as $user) {
            for ($i = 0; $i < 5; $i++) {
                $number = 100 + ($user->id * 5) + $i;
                $name = "act {$number}";

                $attempt = 0;
                $baseName = $name;
                while (TradeAccount::where('name', $name)->exists()) {
                    $attempt++;
                    $name = "{$baseName}-" . Str::random(4);
                }

                TradeAccount::firstOrCreate(
                    [
                        'user_id' => $user->id,
                        'name' => $name,
                    ]
                );
            }
        }
    }
}
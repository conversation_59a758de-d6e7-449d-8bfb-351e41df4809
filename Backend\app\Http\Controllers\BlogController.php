<?php

namespace App\Http\Controllers;

use App\Http\Resources\BlogResource;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\SitemapCategoryResource;
use App\Models\Blog;
use App\Http\Requests\BlogRequest;
use App\Models\Education;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BlogController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, $page = null)
    {

        $currentPage = $page ?? $request->query('page', 1);

        $blogs = Blog::with('primaryCategory:id,title')->orderBy('created_at', 'desc')->paginate(10, ['*'], 'page', $currentPage);

        $totalRecords = Blog::count();

        $topBlogs = Blog::with('primaryCategory:id,title')->orderBy('clicks', 'desc')->take(3)->get();

        return response()->json([
            'success' => true,
            'message' => 'Blogs data retrieved successfully',
            'data' => [
                'latest_blogs' => BlogResource::collection($blogs),
                'meta' => [
                    'total' => ceil($totalRecords / $blogs->perPage()),
                    'current_page' => $blogs->currentPage(),
                    'next_page' => $blogs->nextPageUrl(),
                    'prev_page' => $blogs->previousPageUrl(),
                    'canonical_url' => url('/blog/page/' . $blogs->currentPage()),
                    'top_blogs' => BlogResource::collection($topBlogs),
                ]
            ]
        ]);
    }



    public function blogsList()
    {
        $blogs = Blog::with(['primaryCategory:id,title'])
            ->orderBy('created_at','desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Blogs List',
            'data' => BlogResource::collection($blogs),
            'pagination' => [
                'total' => $blogs->lastPage(),
                'count' => $blogs->count(),
                'per_page' => $blogs->perPage(),
                'current_page' => $blogs->currentPage(),
                'next_page_url' => $blogs->nextPageUrl(),
                'prev_page_url' => $blogs->previousPageUrl(),
            ],
        ]);
    }




    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::select('id', 'title','slug','database_field')->get();

        return response()->json([
            'success' => true,
            'message' => 'Categories data retrieved successfully',
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $data = $request->only([
            'title',
            'content',
            'summary',
            'primary_category_id',
        ]);

        $data['secondary_categories'] = is_array($request->secondary_categories)
            ? json_encode($request->secondary_categories)
            : $request->secondary_categories;

        $data['is_featured'] = $request->has('is_featured') ? 1 : 0;

        $blog = Blog::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Blog Added Successfully',
            'data' => new BlogResource($blog),
        ]);

    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        $blog->load('primaryCategory');

        $blog->increment('clicks');
        $blog->refresh();

//        $nextBlog = Blog::with('primaryCategory')
//            ->where('created_at', '<', $blog->created_at)
//            ->orderBy('created_at', 'desc')
//            ->first();

        $previousBlog = Blog::with('primaryCategory')
            ->where('id', '!=', $blog->id)
            ->orderByRaw("CASE WHEN created_at < ? THEN 0 ELSE 1 END", [$blog->created_at])
            ->orderBy('created_at', 'desc')
            ->first();

        return response()->json([
            'success' => true,
            'message' => 'Blog data retrieved successfully',
            'data' => new BlogResource($blog),
            'next_blog' => $previousBlog ? new BlogResource($previousBlog) : null,
        ]);
    }



    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $categories = Category::all();
        $data = [
            'categories'=>CategoryResource::collection($categories),
            'blog'=>new BlogResource($blog),
        ];
        return response()->json([
            'success' => true,
            'message' => 'Blog data',
            'data' => $data,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Blog $blog)
    {
        $data = $request->only([
//            'tags',
            'title',
            'content',
            'summary',
            'feature_image',
            'primary_category_id',
        ]);

        $data['is_featured'] = $request->has('is_featured') ? 1 : 0;
        $data['secondary_categories'] = $request->secondary_categories;

        $blog->fill($data)->save();

        $blog->refresh();
        return response()->json([
            'success' => true,
            'message' => 'Blog data updated successfully',
            'data' => new BlogResource($blog),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        $blog->delete();

        return response()->json([
            'success' => true,
            'message' => 'Education data deleted successfully',
        ]);
    }

    public function blogSitemap()
    {
        $categories = Category::whereHas('primaryBlogs')
        ->orderBy('title', 'asc')
            ->take(10)
            ->get();

        $categories->load(['primaryBlogs' => function ($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        return response()->json([
            'success' => true,
            'message' => 'Sitemap Blogs of Categories',
            'data' => SitemapCategoryResource::collection($categories),
        ]);
    }

}

"use client";

import { Container } from "react-bootstrap";
import { PlusIcon } from "@/assets/svgIcons/SvgIcon";
import CommonButton from "@/Components/UI/CommonButton";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import "@/css/dashboard/StrategyManager.scss";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import DashboardLayout from "@/Layouts/DashboardLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import React from "react";
import Link from "next/link";

const StrategyManager = () => {
  const entrylist = [
    { title: "Strategy 2", text: ">50% D/P" },
    { title: "Strategy 1", text: "Deviation Strict" }
  ];

  const metaArray = {
    noindex: true,
    title: "Strategy Manager | Manage Your Trading Strategies | TradeReply",
    description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective.",
    canonical_link: "https://www.tradereply.com/dashboard/strategy-manager",
    og_site_name: "TradeReply",
    og_title: "Strategy Manager | Manage Your Trading Strategies | TradeReply",
    og_description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective.",
    twitter_title: "Strategy Manager | Manage Your Trading Strategies | TradeReply",
    twitter_description: "Manage and refine your trading strategies with TradeReply's Strategy Manager. Keep your strategies organized and effective."
  };
  return (
    <>
      <DashboardLayout>
        <MetaHead props={metaArray} />
        <div className="trade_manager">
          <CommonHead />
          <Container>
            <div className="trade_head">
              <AdminHeading
                heading="Strategy Manager"
                centered
              />
            </div>
            <div className="trade_manager_btns">
              <Link href={"/dashboard/strategy-builder"} className="w-100">
                <CommonButton
                  onlyIcon={<PlusIcon />}
                  title="Add Strategy"
                  className="w-100 me-2"
                />
              </Link>
            </div>
            <div className="trade_manager_entrylist">
              <Link href={"/dashboard/strategy-builder"} className="w-100">
                {entrylist.map((item, index) => (
                  <div
                    key={index}
                    className="trade_manager_entrylist_box"
                  >
                    <h5>{item?.title}</h5>
                    <h5>{item?.text}</h5>
                  </div>
                ))}
              </Link>
            </div>
          </Container>
        </div>
      </DashboardLayout>
    </>
  );
};

export default StrategyManager;

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class UserSecretQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'question',
        'answer'
    ];

    protected $hidden = [

    ];

    /**
     * Relationship: A secret question belongs to a user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Store answer as plain text
     */
    public function setAnswerAttribute($value)
    {
        $this->attributes['answer'] = strtolower(trim($value));
    }

    /**
     * Verify if the provided answer matches the stored answer
     */
    public function verifyAnswer($answer)
    {
        $normalizedAnswer = strtolower(trim($answer));
        return $this->answer === $normalizedAnswer;
    }

    /**
     * Get the question and answer for display
     */
    public function toArray()
    {
        return parent::toArray();
    }
}

export function hashInput(input) {
    const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
  
    if (isEmail) {
      return input.replace(/^(.)(.*)(@.*)$/, (_, first, middle, domain) => `**${middle.slice(-1)}${domain}`);
    } else {
      return input.length > 3
        ? `**${input.slice(-2)}` // Show last 2 characters for usernames
        : `**`; // Mask completely if too short
    }
  }
  
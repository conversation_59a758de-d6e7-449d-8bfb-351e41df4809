'use client';
import React, { useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import {PlusIconSvg, RightArrowIconSvg} from "@/assets/svgIcons/SvgIcon";
import { get } from "@/utils/apiUtils";
import { setUser } from "@/redux/authSlice";
import { maskEmail } from "@/utils/emailMask";
import Link from 'next/link';

export default function YourInformation() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Only first letter showing, the rest is hashed
    // e.g. A**** M******
    const maskName = (name) => {
        if (!name || name.length === 0) return '';
        if (name.length === 1) return name;
        return name[0] + '*'.repeat(name.length - 1);
    };


    // Use standardized email masking consistent across the application
    // Format: first letter + asterisks + last letter + domain (a***********<EMAIL>)
    const maskEmailAddress = (email) => {
        return maskEmail(email);
    };


    // Only show last 2 digits, the rest is hashed
    // e.g. ********01
    const formatPhoneNumber = (phone) => {
        if (!phone) return null;
        // Remove all non-digits
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.length < 2) return phone;

        const lastTwoDigits = cleaned.slice(-2);
        const starsCount = Math.max(cleaned.length - 2, 8);
        return '*'.repeat(starsCount) + lastTwoDigits;
    };

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Always fetch fresh data first to ensure we have the latest from DB
        fetchUserData();

        // Also check for cached data as fallback
        const storedUser = localStorage.getItem('user');

        if (reduxUser) {
            // Use Redux data if available, but still fetch fresh data
            setUserData(reduxUser);
            setLoading(false);
        } else if (storedUser) {
            // Use localStorage data as immediate fallback while API loads
            try {
                const parsedUser = JSON.parse(storedUser);
                setUserData(parsedUser);
                // Update Redux store
                dispatch(setUser(parsedUser));
                setLoading(false);
            } catch (err) {
                console.error('Error parsing stored user data:', err);
            }
        }
    }, []); // Empty dependency array to run only on mount

    // Get display name
    const getDisplayName = () => {
        if (!userData) return 'Loading...';

        const firstName = userData.first_name || '';
        const lastName = userData.last_name || '';

        if (!firstName && !lastName) {
            // Fallback to full name if first/last names are not available
            const fullName = userData.name || '';
            if (fullName) {
                const nameParts = fullName.split(' ');
                return nameParts.map(part => maskName(part)).join(' ');
            }
            // If no name is set, show prompt to add name
            return (
                <Link
                    href="/account/details" legacyBehavior>
                    <a className="add_number d-flex align-items-center vertical-align-middle"><PlusIconSvg /> Add Name</a>
                </Link>
            );
        }

        return `${maskName(firstName)} ${maskName(lastName)}`.trim();
    };

    const getDisplayEmail = () => {
        if (!userData) return 'Loading...';
        return userData.email ? maskEmailAddress(userData.email) : 'Not set';
    };

    const getDisplayUsername = () => {
        if (!userData) return 'Loading...';
        if (!userData.username) return 'Not set';

        // Username should be a link to public profile according to HIVE specs
        // e.g. tradereply.com/@AaronMcCloud123
        return (
            <a
                target="_blank"
                rel="noopener noreferrer"
                href={`/@${userData.username}`}
                style={{ color: 'rgb(0, 173, 239)' }}
            >
                <h6 style={{ margin: 0, display: 'inline' }}>{userData.username}</h6>
            </a>
        );
    };

    const getDisplayPhoneNumber = () => {
        if (!userData) return null;
        // if phone number isn't added, show "Add Phone Number"
        // If phone number exists, show it hashed with only last 2 digits visible
        return userData.phone_number ? formatPhoneNumber(userData.phone_number) : null;
    };

    const hasPhoneNumber = () => {
        return userData && userData.phone_number;
    };



    return (
        <>
            <Col md={6} xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/details"
                    title="Your Information"
                    Linktext="Account Details"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_list">
                        {loading ? (
                            <div className="text-center py-3">
                                <span>Loading user information...</span>
                            </div>
                        ) : error ? (
                            <div className="text-center py-3">
                                <span className="text-danger">Failed to load user information</span>
                                <br />
                                <button
                                    className="btn btn-sm btn-link"
                                    onClick={fetchUserData}
                                    style={{ color: '#007bff', textDecoration: 'underline' }}
                                >
                                    Retry
                                </button>
                            </div>
                        ) : (
                            <>
                                <ul>
                                    <li>
                                        <span>Name </span> {getDisplayName()}
                                    </li>
                                    <li>
                                        <span>Email </span> {getDisplayEmail()}
                                    </li>
                                    <li>
                                        <span>Username </span> {getDisplayUsername()}
                                    </li>
                                    <li>
                                        <span>Phone Number </span>{" "}
                                        {hasPhoneNumber() ? (
                                            <span>{getDisplayPhoneNumber()}</span>
                                        ) : (
                                            <Link href="/account/phone/setup?from=/account/overview" prefetch={true}>
                                                <button className="add_phone_number add_number d-flex align-items-center" type="button">
                                                    <PlusIconSvg /> Add Phone Number
                                                </button>
                                            </Link>
                                        )}
                                    </li>
                                </ul>

                            </>
                        )}
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    );
}

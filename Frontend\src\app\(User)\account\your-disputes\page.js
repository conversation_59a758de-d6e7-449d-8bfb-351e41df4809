"use client";
import { Col, Row } from "react-bootstrap";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { DropArrowUpIcon, DropArrowIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";
import React, { useState } from "react";

export default function YourDisputes() {
  const [isSellerOpen, setIsSellerOpen] = useState(null);
  const [isSellerClose, setIsSellerClose] = useState(null);
  const [isBuyerOpen, setIsBuyerOpen] = useState(null);
  const [isBuyerClose, setIsBuyerClose] = useState(null);
  const disputeArray = [
    {
      buyer: "@Wakas",
      product: "Stock Indicators Course 101",
      orderId: "29311",
      dispute: "Not what I expected",
      reason: "Mar 24, 2025",
      openedOn: "Waiting on Seller",
      respondBy: "Mar 24, 2025",
    },
    {
      buyer: "@Wakas",
      product: "Stock Indicators Course 101",
      orderId: "29311",
      dispute: "Not what I expected",
      reason: "Mar 24, 2025",
      openedOn: "Waiting on Seller",
      respondBy: "Mar 24, 2025",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Your Disputes (As a Seller)" />
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard
                title="Open Disputes (3) "
                className="account_card"
              >
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Buyer</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Title</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute Reason</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Opened</div>
                          </th>
                          <th>
                            <div className="th-inner">Status</div>
                          </th>
                          <th>
                            <div className="th-inner">Respond By</div>
                          </th>
                          <th>
                            <div className="th-inner">Actions</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>{item.orderId}</td>
                            <td>{item.dispute}</td>
                            <td>{item.reason}</td>
                            <td>{item.openedOn}</td>
                            <td>{item.respondBy}</td>
                            <td>
                              <Link href="/account/resolve-dispute/1">
                                <CommonButton
                                  title=" View / Respond"
                                  className="view_res_btn"
                                />
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs>Buyer</Col>
                            <Col xs>Product Title</Col>
                            <Col xs>Status</Col>
                            <Col xs="auto">
                              <div className="arrow-header"></div>
                            </Col>
                          </div>

                          <Col xs className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs className="colunm_value">
                            {item.product}
                          </Col>
                          <Col xs className="colunm_value">
                            {item.openedOn}
                          </Col>
                          <Col className="colunm_value m-auto" xs="auto">
                            <div
                              className="account_card_btnArrow"
                              onClick={() =>
                                setIsSellerOpen((prev) =>
                                  prev !== index ? index : null
                                )
                              }
                            >
                              {isSellerOpen == index ? (
                                <DropArrowUpIcon />
                              ) : (
                                <DropArrowIcon />
                              )}
                            </div>
                          </Col>
                        </Row>
                        {isSellerOpen == index && (
                          <>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Dispute ID</Col>
                                <Col xs={6}>Dispute Reason</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.orderId}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.dispute}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Date Opened</Col>
                                <Col xs={6}>Respond By</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={12}>Actions</Col>
                              </div>

                              <Link
                                className="w-100"
                                href="/account/resolve-dispute/1"
                              >
                                <CommonButton
                                  title=" View / Respond"
                                  className="view_res_btn w-100"
                                />
                              </Link>
                            </Row>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard
                title="Closed Disputes (15) "
                className="account_card"
              >
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Buyer</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Name</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute Reason</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Opened</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Resolved</div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Outcome</div>
                          </th>
                          <th>
                            <div className="th-inner">Actions</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>{item.orderId}</td>
                            <td>{item.dispute}</td>
                            <td>{item.reason}</td>
                            <td>{item.reason}</td>
                            <td>{item.openedOn}</td>
                            <td>
                              <CommonButton
                                title=" View"
                                className="view_res_btn"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs>Buyer</Col>
                            <Col xs>Product Name</Col>
                            <Col xs>Dispute ID</Col>
                            <Col xs="auto">
                              <div className="arrow-header"></div>
                            </Col>
                          </div>

                          <Col xs className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs className="colunm_value">
                            {item.product}
                          </Col>
                          <Col xs className="colunm_value">
                            {item.orderId}
                          </Col>
                          <Col className="colunm_value m-auto" xs="auto">
                            <div
                              className="account_card_btnArrow"
                              onClick={() =>
                                setIsSellerClose((prev) =>
                                  prev !== index ? index : null
                                )
                              }
                            >
                              {isSellerClose == index ? (
                                <DropArrowUpIcon />
                              ) : (
                                <DropArrowIcon />
                              )}
                            </div>
                          </Col>
                        </Row>
                        {isSellerClose == index && (
                          <>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Dispute Reason</Col>
                                <Col xs={6}>Resolution Outcome</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.dispute}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.openedOn}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Date Opened</Col>
                                <Col xs={6}>Date Resolved</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <Col
                                xs={12}
                                className="colunm_head single_colunm"
                              >
                                Actions
                              </Col>
                              <CommonButton
                                title=" View"
                                className="view_res_btn w-100"
                              />
                            </Row>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <SidebarHeading title="Your Disputes (As a Buyer)" />
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Open Disputes" className="account_card">
                <div className="account_card_disputes">
                  <p className="no_disputes">You have no open disputes.</p>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            {/* Followers */}
            <Col>
              <CommonWhiteCard
                title="Closed Disputes (15) "
                className="account_card"
              >
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Seller</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Name</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute Reason</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Opened</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Resolved</div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Outcome</div>
                          </th>
                          <th>
                            <div className="th-inner">Actions</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>{item.orderId}</td>
                            <td>{item.dispute}</td>
                            <td>{item.reason}</td>
                            <td>{item.respondBy}</td>
                            <td>{item.openedOn}</td>
                            <td>
                              <Link className="w-100" href="/account/dispute/1">
                                <CommonButton
                                  title=" View"
                                  className="view_res_btn"
                                />
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs>Seller</Col>
                            <Col xs>Product Name</Col>
                            <Col xs>Dispute ID</Col>
                            <Col xs="auto">
                              <div className="arrow-header"></div>
                            </Col>
                          </div>

                          <Col xs className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs className="colunm_value">
                            {item.product}
                          </Col>
                          <Col xs className="colunm_value">
                             {item.orderId}
                          </Col>
                          <Col className="colunm_value m-auto" xs="auto">
                            <div
                              className="account_card_btnArrow"
                              onClick={() =>
                                setIsBuyerClose((prev) =>
                                  prev !== index ? index : null
                                )
                              }
                            >
                              {isBuyerClose == index ? (
                                <DropArrowUpIcon />
                              ) : (
                                <DropArrowIcon />
                              )}
                            </div>
                          </Col>
                        </Row>
                        {isBuyerClose == index && (
                          <>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Dispute Reason</Col>
                                <Col xs={6}>Resolution Outcome</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.dispute}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.openedOn}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={6}>Date Opened</Col>
                                <Col xs={6}>Date Resolved</Col>
                              </div>

                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                              <Col xs={6} className="colunm_value">
                                {item.reason}
                              </Col>
                            </Row>
                            <Row className="mb-2 px-2">
                              <div className="colunm_head">
                                <Col xs={12}>Actions</Col>
                              </div>
                              <Link className="w-100" href="/account/dispute/1">
                                <CommonButton
                                  title=" View"
                                  className="view_res_btn w-100"
                                />
                              </Link>
                            </Row>
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

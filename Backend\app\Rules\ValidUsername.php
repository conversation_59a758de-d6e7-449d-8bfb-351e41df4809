<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidUsername implements Rule
{
    protected array $reserved;
    protected string $failReason = '';

    public function __construct()
    {
        $this->reserved = $this->getReservedUsernames();
    }

    public function passes($attribute, $value): bool
    {
        $value = strtolower($value);

        // Check for reserved usernames
        if (in_array($value, $this->reserved)) {
            $this->failReason = 'This username is reserved and cannot be used.';
            return false;
        }

        // Comprehensive validation matching frontend logic
        if (
            !preg_match('/^[a-z0-9_]+$/', $value) ||           // Only letters, numbers, underscores
            strlen($value) < 3 || strlen($value) > 20 ||       // 3-20 characters
            substr_count($value, '_') > 2 ||                   // Max 2 underscores
            str_contains($value, '__') ||                      // No consecutive underscores
            str_starts_with($value, '_') ||                    // Cannot start with underscore
            preg_match('/^\d+$/', $value) ||                   // Cannot be all numbers
            preg_match('/(\d)\1{2,}/', $value) ||              // No 3+ consecutive same digits
            preg_match('/123456|234567|345678|456789|987654|876543/', $value) // No sequential numbers
        ) {
            return false;
        }

        // Check for spammy usernames (single character repeated too many times)
        if (preg_match('/^(.)\1{4,}$/', $value)) {
            $this->failReason = 'Username cannot consist of a single character repeated too many times.';
            return false;
        }

        return true;
    }

    public function message(): string
    {
        return $this->failReason ?: 'Usernames must be 3–20 characters, include at least one letter, and may only contain letters, numbers, and underscores (no consecutive underscores).';
    }

    /**
     * Get reserved usernames dynamically
     */
    private function getReservedUsernames(): array
    {
        try {
            // Get all routes from the application
            $routes = [];

            $routeCollection = \Route::getRoutes();
            foreach ($routeCollection as $route) {
                $uri = $route->uri();
                if (!empty($uri) && $uri !== '/' && !str_starts_with($uri, '_') && !str_starts_with($uri, 'api/')) {
                    // Remove parameters from route
                    $cleanRoute = preg_replace('/{.*?}/', '', $uri);
                    $cleanRoute = trim($cleanRoute, '/');
                    if (!empty($cleanRoute)) {
                        $routes[] = $cleanRoute;
                        // Add variant with underscores
                        $routes[] = str_replace('-', '_', $cleanRoute);
                    }
                }
            }

            // Add common reserved names
            $reservedNames = [
                'admin', 'support', 'help', 'info', 'root', 'system', 'test',
                'username', 'null', 'user', 'guest', 'tradereply', 'trade_reply',
                'dashboard', 'account', 'marketplace', 'checkout', 'login', 'signup',
                'trading_calculator', 'trading-calculator', 'privacy', 'terms', 'contact',
                'blog', 'education', 'features', 'pricing', 'status', 'search'
            ];

            return array_unique(array_merge($routes, $reservedNames));

        } catch (\Exception $e) {
            // Fallback to basic reserved usernames if route fetching fails
            return [
                'admin', 'support', 'help', 'info', 'root', 'system', 'test',
                'username', 'null', 'user', 'guest', 'dashboard', 'account',
                'marketplace', 'checkout', 'login', 'signup', 'tradereply', 'trade_reply',
                'trading_calculator', 'trading-calculator', 'privacy', 'terms', 'contact'
            ];
        }
    }
}

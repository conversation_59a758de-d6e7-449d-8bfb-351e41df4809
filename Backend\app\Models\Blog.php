<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Blog extends Model
{
    use HasFactory;

    protected $table = 'blogs';
    protected $fillable = [
        'id',
        'title',
        'content',
        'slug',
        'feature_image',
        'tags',
        'primary_category_id',
        'summary',
        'secondary_categories',
        'is_featured',
        'clicks'
    ];

    public const BLOG_PATH = 'blog/featured/';
    protected static string $ENV;

    protected $attributes = [
        'is_featured' => 0,
    ];

    protected $casts = [
        'secondary_categories' => 'array', // Automatically convert JSON to array
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }


    protected static function boot()
    {
        parent::boot();

        self::$ENV = env('APP_ENV') === 'production' ? '/main' : '/dev';

        static::deleting(function ($blog) {
//             if ($blog->feature_image) {
//                 if (Storage::disk('s3')->exists(self::$ENV . self::BLOG_PATH . $blog->feature_image)) {
//                     Storage::disk('s3')->delete(self::$ENV . self::BLOG_PATH . $blog->feature_image);
//                 }
//             }
        });

        static::creating(function ($blog) {
            if ($blog->title) {
                $blog->slug = self::generateUniqueSlug($blog->title);
            }
            if (request()->hasFile('feature_image')) {
                $blog->feature_image = self::uploadFeatureImage(request()->file('feature_image'));
            }
        });

        static::updating(function ($blog) {
            $request = request(); // Get request instance safely

            // Check if the title has changed, then update the slug
            if ($blog->isDirty('title')) {
                $blog->slug = self::generateUniqueSlug($blog->title, $blog->id);
            }

//            if ($request->hasFile('feature_image')) {
//                $oldImage = $blog->getOriginal('feature_image');
//
//                // Delete the old image if it exists in S3
//                if ($oldImage && Storage::disk('s3')->exists(self::$ENV . self::BLOG_PATH . $oldImage)) {
//                    Storage::disk('s3')->delete(self::$ENV . self::BLOG_PATH . $oldImage);
//                }
//
//                $blog->feature_image = self::uploadFeatureImage($request->file('feature_image'));
//            }
        });
    }

    public static function uploadFeatureImage($file)
    {
        $originalName = Str::random(40) . '-' . $file->getClientOriginalName();
        $path = self::$ENV . self::BLOG_PATH;

        try {
            $check = Storage::disk('s3')->putFileAs($path, $file, $originalName);
            Log::info('File uploaded successfully', ['status' => $check]);
            return $originalName;
        } catch (\Exception $e) {
            Log::error('S3 Upload Failed: ' . $e->getMessage());
        }
    }

    public function getFeatureImageUrlAttribute()
    {
        return $this->feature_image
            ? config('filesystems.disks.s3.url') . self::$ENV . self::BLOG_PATH . $this->feature_image
            : null;
    }

    /**
     * Generate a unique slug based on the title.
     *
     * @param string $title
     * @return string
     */
    public static function generateUniqueSlug($title, $id = null)
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        while (self::where('slug', $slug)
            ->when($id, fn($query) => $query->where('id', '!=', $id)) // Exclude current record
            ->exists()) {
            $slug = "{$originalSlug}-{$count}";
            $count++;
        }

        return $slug;
    }

    public function getFeatureImageAttribute($value)
    {
        if (!$value) {
            return null;
        }

        return config('filesystems.disks.s3.url').$this->BLOG_PATH. $value;
    }

//    public function uploadFeatureImage($file)
//    {
//        $originalName = Str::random(40).'-'.$file->getClientOriginalName();
//        $path = 'dev/'.$this->BLOG_PATH . $originalName;
//        Storage::disk('s3')->put($path, file_get_contents($file));
//        return $originalName;
//    }
    public function setTagsAttribute($value)
    {
        $this->attributes['tags'] = is_array($value) ? implode(',', $value) : $value;
    }

    public function getTagsAttribute($value)
    {
        return explode(',', $value);
    }



    public function primaryCategory(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'primary_category_id');
    }

//    public function secondaryCategory(): BelongsTo
//    {
//        return $this->belongsTo(Category::class, 'secondary_category_id');
//    }

    /**
     * Categories relationship (many-to-many).
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'blog_category');
    }


    public function userProgress()
    {
        return $this->belongsToMany(User::class, 'article_progress','education_id','article_id')
            ->withPivot('progress')
            ->withTimestamps();
    }
}

'use client';

import { Row } from "react-bootstrap";
import React from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/Connections.scss";
import TradeAccounts from "./Partial/TradeAccounts";
import ConnectedAccounts from "./Partial/ConnectedAccounts";
import BrokersIntegrations from "./Partial/BrokersIntegrations";

export default function Connection() {
    const metaArray = {
        title: "Account Connections | Integrations | TradeReply",
        description:
            "Manage your broker connections on TradeReply.com. Integrate your trading accounts to access advanced tools and real-time analytics.",
        canonical_link: "https://www.tradereply.com/account/connections",
        og_site_name: "TradeReply",
        og_title: "Account Connections | Integrations | TradeReply",
        og_description:
            "Manage your broker connections and integrations on TradeReply. Sync your trading accounts for seamless data and analytics.",
        twitter_title: "Account Connections | Integrations | TradeReply",
        twitter_description:
            "Manage your broker connections and integrations on TradeReply. Sync your trading accounts for seamless data and analytics.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="connections_sec">
                    <SidebarHeading title="Connections" />
                    <Row>
                        <ConnectedAccounts />
                        <TradeAccounts />
                        <BrokersIntegrations />
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}

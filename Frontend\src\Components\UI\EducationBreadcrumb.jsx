
'use client'

import { Breadcrumb } from "react-bootstrap";
import Link from "next/link"; // Import Next.js Link
import "../../css/common/CustomBreadcrumb.scss";

const EducationBreadcrumb = ({ titlehref, categoryname,linkname, educationhref, pagename ,articlename}) => {
  
  return (
    <>
      <div className="custom_breadcrumb">
        <Breadcrumb>
          <li className="breadcrumb-item home-item">
            <Link href="/">Home</Link>
          </li>
          <li className="breadcrumb-item secondary_link">
            {/* <Link href={href}>{linkname}</Link> */}
            {educationhref ? <Link href={educationhref}>{linkname}</Link> : <span>{linkname}</span>}
          </li>
          {/* <li className="breadcrumb-item secondary_link"> 
            {titlehref ? <Link href={titlehref}>{categoryname}</Link> : <span>{categoryname}</span>}
          </li> */}


          <Breadcrumb.Item active>{categoryname}</Breadcrumb.Item>

          <div>{articlename}</div>

        </Breadcrumb>
      </div>
    </>
  );
};

export default EducationBreadcrumb;

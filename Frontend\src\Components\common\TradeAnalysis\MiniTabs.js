import React, { useState } from "react";

export default function MiniTabs( props ) {
  const tabsTAnalysis = ["Setup", "Configure", "View"];

  return (
    <>
      <div className="d-flex gap-1">
        {tabsTAnalysis.map((tab, index) => (
            <div
                onClick={() => props?.onClick?.(tab)}
                className={`trade_analysis_heading ${props.activeTab === tab ? 'active' : ''}`}
                key={index}
            >
            <p>{tab}</p>
          </div>
        ))}
      </div>
    </>
  );
}

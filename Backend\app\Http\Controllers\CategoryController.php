<?php

namespace App\Http\Controllers;

use App\Http\Resources\ArticleResource;
use App\Http\Resources\CategoryResource;
use App\Models\Category;
use App\Models\Article;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CategoryController extends Controller

{
    /**
     * Display a listing of the resource.
     */

    public function index(Request $request, $page = null)
    {
        try {
            $slug = $request->slug ?? null;
            $category = $slug ? Category::where('slug', $slug)->first() : null;

            $allcategories = Category::select('id', 'title', 'slug')->orderBy('title', 'asc')->get();

            // Check if "All Categories" mode is enabled
            $isAllCategories = !$category;

            // Fetch articles with both primary and secondary categories
            $key = $request->query('key', null);
            $articlesQuery = Article::with(['primaryCategory:id,title', 'secondaryCategories:id,title']);

            if (!$isAllCategories) {
                // If a category is selected, filter by both primary and secondary categories
                $articlesQuery->where(function ($query) use ($category) {
                    $query->where('primary_category_id', $category->id)
                        ->orWhereHas('secondaryCategories', function ($q) use ($category) {
                            $q->where('category_id', $category->id);
                        });
                });
            }

            if (!empty($key)) {
                $articlesQuery->where(function ($query) use ($key) {
                    $query->where('summary', 'LIKE', "%{$key}%")
                        ->orWhereHas('primaryCategory', function ($q) use ($key) {
                            $q->where('title', 'LIKE', "%{$key}%");
                        })
                        ->orWhereHas('secondaryCategories', function ($q) use ($key) {
                            $q->where('title', 'LIKE', "%{$key}%");
                        })
                        ->orWhere('tags', 'LIKE', "%{$key}%");
                });


                    $articlesQuery->orderByRaw("
                        CASE
                        WHEN summary LIKE ? THEN 1
                        WHEN summary LIKE ? THEN 2
                        WHEN tags LIKE ? THEN 3
                        ELSE 4
                        END
                    ", ["{$key}%", "%{$key}%", "%{$key}%"]);
            }

            $articlesQuery->orderBy('created_at', 'desc');
            $perPage = 10;
            $currentPage = $request->query('page', 1);
            $paginatedResults = $articlesQuery->paginate($perPage, ['*'], 'page', $currentPage);
            $formattedArticles = ArticleResource::collection($paginatedResults);
//dd($formattedArticles)  ;
            // Prepare selected category details
            $selectedCategory = $category ? [
                'id' => $category->id,
                'slug' => $category->slug,
                'title' => $category->title,
                'content' => $category->content,
            ] : null;

            return response()->json([
                'success' => true,
                'message' => 'Articles retrieved successfully',
                'data' => [
                    'allcategories' => CategoryResource::collection($allcategories),
                    'selected_category' => $selectedCategory,
                    'articles' => $formattedArticles,
                    'meta' => [
                        'total' => ceil($paginatedResults->total() / $perPage),
                        'current_page' => $paginatedResults->currentPage(),
                        'next_page' => $paginatedResults->nextPageUrl(),
                        'prev_page' => $paginatedResults->previousPageUrl(),
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching category data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Server Error: ' . $e->getMessage(),
            ], 500);
        }
    }





    /**
     * Show the form for creating a new resource.
     */
    public function categoriesList()
    {
        $categories = Category::getCategoryList(10); // Fetch paginated data from the model

        return response()->json([
            'success' => true,
            'message' => 'Categories List',
            'data' => CategoryResource::collection($categories),
            'pagination' => [
                'total' => $categories->lastPage(),
                'count' => $categories->count(),
                'per_page' => $categories->perPage(),
                'current_page' => $categories->currentPage(),
                'next_page_url' => $categories->nextPageUrl(),
                'prev_page_url' => $categories->previousPageUrl(),
            ],
        ]);
    }



    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
//        $request->validate([
//            'title' => 'required|string|max:255',
//            'content' => 'nullable|string',
//        ]);

        $categories = Category::createCategory($request->only(['title', 'content']));

        return response()->json([
            'success' => true,
            'message' => 'Categories list',
            'data' => CategoryResource::collection($categories),
            'pagination' => [
                'total' => $categories->total(),
                'count' => $categories->count(),
                'per_page' => $categories->perPage(),
                'current_page' => $categories->currentPage(),
                'next_page_url' => $categories->nextPageUrl(),
                'prev_page_url' => $categories->previousPageUrl(),
            ],
        ]);
    }


    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        return response()->json([
            'success' => true,
            'message' => 'categories list',
            'data' => new CategoryResource($category),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        return response()->json([
            'success' => true,
            'message' => 'categories list',
            'data' => new CategoryResource($category),
        ]);
    }

    /**
    * Update the specified resource in storage.
    */
    public function update(Request $request, Category $category)
    {
//        $request->validate([
//            'title' => 'required|string|max:255',
//            'content' => 'nullable|string',
//        ]);

        $category = $category->updateCategory($request->only(['title', 'content']));

        return response()->json([
            'success' => true,
            'message' => 'Category updated successfully.',
            'data' => new CategoryResource($category),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        $category->deleteCategory();

        return response()->json([
            'success' => true,
            'message' => 'Category deleted, and references updated in articles.',
        ]);
    }









}

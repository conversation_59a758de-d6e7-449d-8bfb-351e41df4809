// // import { <PERSON> } from "@inertiajs/react";
import Link from "next/link"; // Import Next.js Link


const LoginFooter = () => {
  return (
    <>
      <div className="login_footer text-center">
        <div className="login_footer_links d-flex flex-wrap">
          <Link href="/privacy" target="_blank" rel="noopener noreferrer">Privacy</Link>
          <Link href="/terms" target="_blank" rel="noopener noreferrer">Terms</Link>
          <Link href="/disclaimer" target="_blank" rel="noopener noreferrer">Disclaimer</Link>
          <Link href="/cookies" target="_blank" rel="noopener noreferrer">Cookies</Link>
          <a href="#" onClick={(e) => {
            e.preventDefault(); // stop jumping
            if (typeof Osano !== "undefined" && Osano.cm) {
          	Osano.cm.showDrawer("osano-cm-dom-info-dialog-open");
            }
          }}
          >
          <PERSON><PERSON>
          </a>
        </div>
        <p>Copyright © 2025 TradeReply. All Rights Reserved.</p>
      </div>
    </>
  );
};

export default LoginFooter;

import React from 'react'
import { WhiteCrossIcon, ProfileUserDarkIcon } from "@/assets/svgIcons/SvgIcon";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";


export default function FollowingListPopup({ title, closeModal, following }) {
    return (
        <div className="modal_overlay">
            <div className="modal-body">
                <div className='d-flex justify-content-between align-items-center mb-4'>
                    <h4 className='mb-0'>{title}</h4>
                    <button onClick={closeModal}>
                        <WhiteCrossIcon />
                    </button>
                </div>
                <CommonWhiteCard
                    title={`Following  (${following.length})`}
                    className="account_card"
                >
                    <div className="account_card_followers">
                        {following.map((user, index) => (
                            <div className="main_inform" key={index}>
                                <div className="profile_photo">
                                    <ProfileUserDarkIcon />
                                </div>
                                <a
                                    href={`/@${user.username || user.name}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    style={{
                                        color: "#00adef",
                                    }}
                                >
                                    <h6>{user.name}</h6>
                                </a>
                            </div>
                        ))}
                    </div>
                </CommonWhiteCard>
            </div>
        </div>
    )
}

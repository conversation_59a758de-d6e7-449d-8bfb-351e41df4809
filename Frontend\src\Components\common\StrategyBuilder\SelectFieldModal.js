import React, { useEffect, useRef, useState } from "react";
import { SearchIcons, WhiteCrossIcon, RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import ScopeTabContent from '@/Components/common/StrategyBuilder/ScopeTabContent';

export default function SelectFieldModal({ onClose, onFieldSelect }) {
    const [activeScope, setActiveScope] = useState('Transaction');
    const [transactionActiveTab, setTransactionActiveTab] = useState('dimension');
    const [tradeActiveTab, setTradeActiveTab] = useState('dimension');
    const [portfolioActiveTab, setPortfolioActiveTab] = useState('dimension');
    const scrollRef = useRef(null);
    const [showPrevArrow, setShowPrevArrow] = useState(false);
    const [showNextArrow, setShowNextArrow] = useState(false);

    const checkScroll = () => {
        const el = scrollRef.current;
        if (!el) return;
        const { scrollLeft, scrollWidth, clientWidth } = el;
        setShowPrevArrow(scrollLeft > 5);
        setShowNextArrow(scrollLeft + clientWidth < scrollWidth - 5);
    };

    useEffect(() => {
        const el = scrollRef.current;
        if (!el) return;

        checkScroll();
        el.addEventListener("scroll", checkScroll);
        window.addEventListener("resize", checkScroll);

        return () => {
            el.removeEventListener("scroll", checkScroll);
            window.removeEventListener("resize", checkScroll);
        };
    }, []);

    const scrollLeft = () => {
        scrollRef.current?.scrollBy({ left: -100, behavior: "smooth" });
    };

    const scrollRight = () => {
        scrollRef.current?.scrollBy({ left: 100, behavior: "smooth" });
    };

    const scopes = [
        { name: 'Transaction', count: 367 },
        { name: 'Trade', count: 365 },
        { name: 'Portfolio', count: 350 },
    ];

    const transactionDimension = [
        { name: 'ACCOUNT INITIAL BALANCE' },
        { name: 'MAX RISK PERCENTAGE' },
        { name: 'PROFIT ALLOCATION TO CAPITAL RESERCE' },
        { name: 'ACCOUNT GROWTH GOAL' },
        { name: 'ACCOUNT STOP RISK VALUE' },
        { name: 'STOCK UNIT OF MEASUREMENT' },
    ];
    const transactionMetrices = [
        { name: 'ACCOUNT INITIAL BALANCE' },
        { name: 'MAX RISK PERCENTAGE' },
        { name: 'PROFIT ALLOCATION TO CAPITAL RESERCE' },
        { name: 'ACCOUNT GROWTH GOAL' },
        { name: 'ACCOUNT STOP RISK VALUE' },
        { name: 'STOCK UNIT OF MEASUREMENT' },
    ];
    return (
        <div className="modal_overlay">
            <div className='search_section'>
                <div className="d-flex align-items-center gap-2 mb-3">
                    <div className="closeModal">
                        <button onClick={onClose} >
                            <WhiteCrossIcon />
                        </button>
                    </div>
                    <div className='search'>
                        <SearchIcons />
                        <input type="text" placeholder="Search fields" />
                    </div>
                </div>
                <div className="scope_section_wrapper">
                    {showPrevArrow && (
                        <div className="move-pre-arrow" onClick={scrollLeft}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                    <div className="scope_section" ref={scrollRef}>
                        {scopes.map((scope) => (
                            <div
                                key={scope.name}
                                className={`scopeName ${activeScope === scope.name ? 'active' : ''}`}
                                onClick={() => setActiveScope(scope.name)}
                            >
                                <p>{scope.name} (Scope)</p>
                                <span className='scopeCount'>{scope.count}</span>
                            </div>
                        ))}
                    </div>
                    {showNextArrow && (
                        <div className="move-next-arrow" onClick={scrollRight}>
                            <div className="icon">
                                <RightArrowIcon />
                            </div>
                        </div>
                    )}
                </div>
                <div className='scope_content'>
                    {activeScope === 'Transaction' && (
                        <ScopeTabContent
                            scope="Transaction"
                            activeTab={transactionActiveTab}
                            setActiveTab={setTransactionActiveTab}
                            dimensionData={transactionDimension}
                            metricsData={transactionMetrices}
                            onSelectField={(fieldName) =>
                                onFieldSelect(fieldName, activeScope)
                            }

                        />
                    )}

                    {activeScope === 'Trade' && (
                        <ScopeTabContent
                            scope="Trade"
                            activeTab={tradeActiveTab}
                            setActiveTab={setTradeActiveTab}
                            dimensionData={transactionDimension}
                            metricsData={transactionMetrices}
                            onSelectField={(fieldName) =>
                                onFieldSelect(fieldName, activeScope)
                            }

                        />
                    )}

                    {activeScope === 'Portfolio' && (
                        <ScopeTabContent
                            scope="Portfolio"
                            activeTab={portfolioActiveTab}
                            setActiveTab={setPortfolioActiveTab}
                            dimensionData={transactionDimension}
                            metricsData={transactionMetrices}
                            onSelectField={(fieldName) =>
                                onFieldSelect(fieldName, activeScope)
                            }

                        />
                    )}
                </div>
            </div>
        </div>
    )
}

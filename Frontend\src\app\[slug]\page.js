"use client";
import 'bootstrap/dist/css/bootstrap.min.css';
import { Container, Col, Row } from "react-bootstrap";
import React from 'react'
import { notFound } from 'next/navigation';
import MetaHead from "@/Seo/Meta/MetaHead";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import "@/css/UserProfile.scss"
import "@/css/account/AccountLayout.scss";
import UserInformation from './Partials/UserInformation';
import UserAbout from './Partials/UserAbout';
import FollowersList from './Partials/FollowersList';
import FollowingList from './Partials/FollowingList';
import MarketFeedBack from './Partials/MarketFeedBack';
import ActiveListing from './Partials/ActiveListings'

export default function UsernamePage({ params }) {
    const slug = params.slug;

    if (!slug) {
        notFound();
    }

    const slugArray = Array.isArray(slug) ? slug : [slug];
    const fullSlug = decodeURIComponent('/' + slugArray.join('/'));

    if (slugArray.length !== 1 || !fullSlug.startsWith('/@')) {
        notFound();
    }
    const username = fullSlug.slice(2);

    const user = {
        username: username,
        fullName: "Aaron McCloud",
        profileImage: "https://cdn.tradereply.com/dev/site-assets/icons/tradereply-profile-user-dark.svg",
        location: "United States",
        transactionSummary: "6 transactions totalling $9,405",
        lastActive: "13 hours ago",
        joinedDate: "January 2018",
        about: "Crypto Market Strategist & Blockchain Advocate | Featured in Forbes 8+ Years in Crypto Trading & DeFi | Speaker at Global Crypto Summits | Advisor to Web3 Startups | Passionate about leveraging blockchain technology to drivefinancial empowerment and innovation.",
        website: "youtube.com/@aaron",
        followers: [
            { name: "George Mans", username: "georgemans" },
            { name: "John Doe", username: "johndoe" },
            { name: "Jane Smith", username: "janesmith" },
            { name: "Emily Clark", username: "emilyclark" },
            { name: "Michael Lee", username: "michaellee" },
            { name: "Olivia Brown", username: "oliviabrown" },
            { name: "William Harris", username: "willharris" },
            { name: "Sophia Turner", username: "sophiaturner" }
        ],
        following: [
            { name: "George Mans", username: "georgemans" },
            { name: "John Doe", username: "johndoe" },
            { name: "Jane Smith", username: "janesmith" },
            { name: "Liam Walker", username: "liamwalker" },
            { name: "Chloe Davis", username: "chloedavis" },
            { name: "Noah Miller", username: "noahmiller" },
            { name: "Isabella Wilson", username: "isabellawilson" },
            { name: "James Hall", username: "jameshall" }
        ],
        marketFeedback: [
            {
                reviewer: "Sarah",
                amount: "$1,100",
                stars: 3,
                timeAgo: "6 hours ago",
                message: "Aaron's stock courses were amazing. I feel like a crypto expert!",
                thumbs: "up",
            },
            {
                reviewer: "David",
                amount: "$800",
                stars: 4,
                timeAgo: "1 day ago",
                message: "Very insightful trading sessions!",
                thumbs: "down",
            },
        ],
        listings: [
            {
                title: "Mastering the stock market",
                price: "11.95",
                listedDate: "1/19/25",
                clicks: 65,
            },
            {
                title: "Crypto Basics for Beginners",
                price: "9.99",
                listedDate: "3/22/25",
                clicks: 102,
            },
        ],
    };

    const metaArray = {
        noindex: false,
        title: "Trader Profile | Public View | TradeReply",
        description:
            "Explore this user's public trading profile on TradeReply. View shared marketplace listings, performance metrics, and account bio.",
        canonical_link: `https://www.tradereply.com/@${username}`,
        og_site_name: "TradeReply",
        og_title: "Trader Profile | Public View | TradeReply",
        og_description:
            "Explore this user's public trading profile on TradeReply. View shared marketplace listings, performance metrics, and account bio.",
        og_url: `https://www.tradereply.com/@${username}`,
        og_type: "website",
        og_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        og_image_width: "1200",
        og_image_height: "630",
        og_locale: "en_US",
        twitter_card: "summary_large_image",
        twitter_title: "Trader Profile | Public View | TradeReply",
        twitter_description:
            "Explore this user's public trading profile on TradeReply. View shared marketplace listings, performance metrics, and account bio.",
        twitter_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        twitter_site: "@JoinTradeReply",
    };

    return (
        <div>
            <MetaHead props={metaArray} />
            <Header />
            <Container>
                <div className='public_user_profile'>
                    <div className="sidebar_heading">
                        <div className="sidebar_heading_top">
                            <h2>Trader Profile</h2>
                        </div>
                    </div>
                    <Row >
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <UserInformation user={user} />
                        </Col>
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <UserAbout user={user} />
                        </Col>
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <FollowersList
                                followers={user.followers}
                                user={user} />
                        </Col>
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <FollowingList
                                following={user.following}
                                user={user} />
                        </Col>
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <MarketFeedBack feedbackList={user.marketFeedback} />
                        </Col>
                        <Col md={6} xs={12} className="mb-3 mb-lg-4">
                            <ActiveListing listings={user.listings} />
                        </Col>
                    </Row>
                </div>
            </Container>
            <Footer />

        </div>
    )
}

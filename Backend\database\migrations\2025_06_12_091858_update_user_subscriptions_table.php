<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->renameColumn('subscription_id', 'stripe_subscription_id');

            $table->dropColumn('trial_expires_at');
            $table->dropColumn('expires_at');

            $table->unsignedBigInteger('plan_id')->after('user_id')->nullable();

            $table->string('status')->default('active');
            $table->timestamp('starts_at')->nullable();
            $table->timestamp('ends_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_subscriptions', function (Blueprint $table) {
            $table->renameColumn('stripe_subscription_id', 'subscription_id');
            $table->timestamp('trial_expires_at')->nullable();
            $table->dropColumn(['status', 'starts_at', 'ends_at']);
        });
    }
};

import React from 'react'
import { useCallback, useEffect, useState } from "react";
import CommonSearch from '@/Components/UI/CommonSearch'
import { debounce } from "lodash";

export default function CommonHeadSearch() {
    const [searchKeyword, setSearchKeyword] = useState('');
    const [searchedArticles, setSearchedArticles] = useState([]);
    useEffect(() => {

        if (!searchKeyword) return;

        const fetchHome = async () => {
            try {
                const response = await get("/search/home", { key: searchKeyword });
                setSearchedArticles(response.data);
            } catch (error) {
                console.error("Failed to fetch Education Article:", error);
            }
        };
        fetchHome();
    }, [searchKeyword]);

    const debouncedSearch = useCallback(
        debounce((searchTerm) => {
            setSearchKeyword(searchTerm);
        }, 300),
        []
    );

    const handleHomeSearch = (event) => {
        setSearchedArticles("");
        debouncedSearch(event.target.value);
    };
    return (
        <div className="common_head_search">
            <CommonSearch
                icon={true}
                placeholder="Search & Explore"
                onChange={handleHomeSearch}
                name="commonSearch"
            />
            {searchedArticles.length > 0 && searchKeyword && (
                <div className="position-relative">
                    <div id="comboList" className="list-group position-absolute w-100">
                        {searchedArticles.map((item, index) => (
                            <Link key={index}
                                href={item.type === "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                                className="list-group-item list-group-item-action text-start d-flex justify-content-between align-items-center">
                                <span>{item.title}</span>
                                <span className="search-highlight text-uppercase">{item.type}</span>
                            </Link>
                        ))}
                        <Link href={`/search?search=${searchKeyword}`} className="list-group-item list-group-item-action text-primary text-center">
                            View All
                        </Link>
                    </div>
                </div>
            )}
        </div>
    )
}

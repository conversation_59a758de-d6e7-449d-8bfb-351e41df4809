"use client";
import React from 'react'
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from '@/Seo/Meta/MetaHead';
import { Container } from "react-bootstrap";
import { Row, Col } from "react-bootstrap";
import "@/css/Home/Checkout.scss";
import { DeviceMobileSpeaker, BlackErrorCircle, ViewCartDarkBlue, CheckoutCardBaseBlue, AccessGray } from "@/assets/svgIcons/SvgIcon";
import CommonButton from '@/Components/UI/CommonButton';
import NavLink from '@/Components/UI/NavLink';
import { checkoutSchema } from "@/validations/schema";
import { Formik, Field, Form } from "formik";
import InputError from '@/Components/UI/InputError';
import TextInput from '@/Components/UI/TextInput';
import InputLabel from '@/Components/UI/InputLabel';
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { CardElement, Elements, useStripe, useElements, CardNumberElement, CardExpiryElement, CardCvcElement } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);

export default function CheckoutPageWrapper() {
    return (
        <Elements stripe={stripePromise}>
            <CheckoutPage />
        </Elements>
    );
}

function CheckoutPage() {
    const [loginToken, setLoginToken] = useState(null);
    const [orders, setOrders] = useState([]);
    const [billingInfo, setBillingInfo] = useState(null);
    const [initialValues, setInitialValues] = useState({
        firstName: "",
        lastName: "",
        country: "",
        address: "",
    });

    const stripe = useStripe();

    useEffect(() => {
        const tokens = Cookies.get("authToken");
        setLoginToken(tokens || null);
    }, []);

    useEffect(() => {
        const fetchOrders = async () => {
            const userData = localStorage.getItem("user");
            const userId = userData ? JSON.parse(userData)?.id : null;

            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/orders?user_id=${userId}`, {
                    method: 'GET'
                });

                const resJson = await response.json();
                setOrders(resJson?.data || []);
                setBillingInfo(resJson?.billing);

                if (resJson?.billing) {
                    setInitialValues(prev => ({
                        ...prev,
                        firstName: resJson.billing.first_name || "",
                        lastName: resJson.billing.last_name || "",
                        country: resJson.billing.country || "",
                        address: resJson.billing.address || "",
                    }));

                    if (resJson.billing.user?.stripe_customer_id && resJson.clientSecret) {
                        const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);
                        await stripe.confirmCardSetup(resJson.clientSecret, {
                            payment_method: resJson.billing.payment_method_id,
                        });
                    }
                }

            } catch (error) {
                console.error("Failed to fetch orders", error);
            }
        };

        fetchOrders();
    }, [loginToken]);

    const countries = [
        { code: "select", name: "Select your country" },
        { code: "AF", name: "Afghanistan" },
        { code: "AL", name: "Albania" },
        { code: "DZ", name: "Algeria" },
        { code: "US", name: "United States" },
        { code: "CA", name: "Canada" },
        { code: "GB", name: "United Kingdom" },
        { code: "AU", name: "Australia" },
        { code: "IN", name: "India" },
        { code: "PK", name: "Pakistan" },
        { code: "FR", name: "France" },
        { code: "DE", name: "Germany" },
        { code: "IT", name: "Italy" },
        { code: "JP", name: "Japan" },
        { code: "CN", name: "China" },
        { code: "RU", name: "Russia" },
        { code: "ZA", name: "South Africa" },
        { code: "BR", name: "Brazil" },
        { code: "MX", name: "Mexico" },
        { code: "NG", name: "Nigeria" },
        { code: "KR", name: "South Korea" },
    ];

    const router = useRouter();
    const elements = useElements();
    const cartItems = orders.filter(order => order.status === 'pending');
    const subtotal = cartItems.reduce((total, item) => {
        if (item) {
            return 0;
        }
        const numericPrice = parseFloat(item.price?.replace('$', '') || 0);
        return total + numericPrice;
    }, 0);

    const formattedSubtotal = subtotal.toFixed(2);

    const nextBillingDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
    });

    const handleRemove = async (orderId) => {
        if (!loginToken) return;

        try {
            await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/orders/${orderId}`, {
                method: "DELETE",
                headers: {
                    Authorization: `Bearer ${loginToken}`,
                    Accept: "application/json",
                },
            });

            setOrders(prev => prev.filter(order => order.id !== orderId));
        } catch (error) {
            console.error("Failed to delete order", error);
        }
    };

    const metaArray = {
        noindex: true,
        title: "Checkout | Secure Your Purchase | TradeReply",
        description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
        canonical_link: "https://www.tradereply.com/checkout",
        og_site_name: "TradeReply",
        og_title: "Checkout | Secure Your Purchase | TradeReply Marketplace",
        og_description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
        twitter_title: "Checkout | Secure Your Purchase | TradeReply Marketplace",
        twitter_description: "Complete your purchase securely at TradeReply Marketplace. Enter your details and confirm your order to access top trading tools and services.",
    };

    return (
        <>
            <HomeLayout>
                <MetaHead props={metaArray} />
                <div className="checkout">
                    <Container>
                        <div className='checkoutContainer'>
                            <div >
                                <Row>
                                    <Col sm={12} lg={6}>
                                        <p className='checkoutContainer_title'>Checkout</p>
                                        <p className='checkoutContainer_subtitle'>Once your order is complete, your digital resource will be instantly accessible.</p>
                                    </Col>
                                    <Col sm={12} lg={6}>
                                        <div className='checkoutContainer_top'>
                                            <div className='checkoutContainer_top_right'>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkblue_border'>
                                                        <ViewCartDarkBlue />
                                                    </div>
                                                    <p className='darkblue_text'>View Cart</p>
                                                </div>
                                                <div className='border-blue col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps baseblue_border'>
                                                        <CheckoutCardBaseBlue />
                                                    </div>
                                                    <p className='blue_text'>Checkout </p>
                                                </div>
                                                <div className='border-gray col'></div>
                                                <div className='d-flex flex-column align-items-center'>
                                                    <div className='cart_steps darkgray_border'>
                                                        <AccessGray />
                                                    </div>
                                                    <p className='darkgrey_text'>Access</p>
                                                </div>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                                <Row className='mt-2'>
                                    <Col sm={12} lg={6} className='order-lg-1 order-2 mt-3 mt-lg-0'>
                                        <div className='checkoutContainer_billing'>
                                            <Formik
                                                initialValues={initialValues}
                                                validationSchema={checkoutSchema}
                                                enableReinitialize={true}
                                                onSubmit={async (values, { setSubmitting }) => {
                                                    try {
                                                        const token = Cookies.get("authToken");

                                                        if (!stripe || !elements) {
                                                            console.error("Stripe.js has not yet loaded.");
                                                            return;
                                                        }

                                                        const cardElement = elements.getElement(CardNumberElement);

                                                        const { error, paymentMethod } = await stripe.createPaymentMethod({
                                                            type: "card",
                                                            card: cardElement,
                                                            billing_details: {
                                                                name: `${values.firstName} ${values.lastName}`,
                                                                address: {
                                                                    line1: values.address,
                                                                    country: values.country,
                                                                },
                                                            },
                                                        });

                                                        if (error) {
                                                            console.error("Stripe error:", error.message);
                                                            setSubmitting(false);
                                                            return;
                                                        }

                                                        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/checkout/save`, {
                                                            method: "POST",
                                                            headers: {
                                                                "Content-Type": "application/json",
                                                                Authorization: `Bearer ${token}`,
                                                            },
                                                            body: JSON.stringify({
                                                                ...values,
                                                                payment_method_id: paymentMethod.id,
                                                            }),
                                                        });

                                                        const data = await res.json();

                                                        if (data?.type === 'error') {
                                                            alert(data.message);
                                                        } else {
                                                            router.push('checkout/thank-you');
                                                        }
                                                    } catch (err) {
                                                        alert("Checkout error: " + err.message);
                                                    } finally {
                                                        setSubmitting(false);
                                                    }
                                                }}
                                            >
                                                {({ touched, errors, isSubmitting, values, setFieldValue }) => (
                                                    <Form>
                                                        <p className='checkoutContainer_billing_title'>Billing Address</p>
                                                        <Field name="firstName">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="First Name" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Enter your first name"
                                                                        type="text"
                                                                        error={touched.firstName && errors.firstName ? <InputError message={errors.firstName} /> : null}
                                                                        isError={touched.firstName && errors.firstName}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <Field name="lastName">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="Last Name" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Enter your last name"
                                                                        type="text"
                                                                        error={touched.lastName && errors.lastName ? <InputError message={errors.lastName} /> : null}
                                                                        isError={touched.lastName && errors.lastName}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <div className="customInput">
                                                            <Field name="country">
                                                                {({ field }) => (
                                                                    <>
                                                                        <InputLabel value="Country Or Region" />
                                                                        <select
                                                                            {...field}
                                                                            className={`customSelect form-select ${errors.country ? "error-field" : ""}`}
                                                                        >
                                                                            {countries.map((country) => (
                                                                                <option key={country.code} value={country.code}>
                                                                                    {country.name}
                                                                                </option>
                                                                            ))}
                                                                        </select>

                                                                        {touched.country && errors.country && <InputError message={errors.country} />}
                                                                    </>
                                                                )}
                                                            </Field>
                                                        </div>
                                                        <Field name="address">
                                                            {({ field }) => (
                                                                <>
                                                                    <InputLabel value="Address" />
                                                                    <TextInput
                                                                        {...field}
                                                                        placeholder="Street, Apartment, City, State, ZIP"
                                                                        type="text"
                                                                        error={touched.address && errors.address ? <InputError message={errors.address} /> : null}
                                                                        isError={touched.address && errors.address}
                                                                    />
                                                                </>
                                                            )}
                                                        </Field>
                                                        <p className='checkoutContainer_billing_title'>Payment Information</p>

                                                        <div className="customInput">
                                                            <InputLabel value="Card Number" />
                                                            <CardNumberElement
                                                                options={{ style: { base: { fontSize: '16px', lineHeight: '40px' } } }}
                                                                className="form-control"
                                                            />
                                                        </div>

                                                        <Row>
                                                            <Col xs={6}>
                                                                <div className="customInput">
                                                                    <InputLabel value="Expiry Date" />
                                                                    <CardExpiryElement
                                                                        options={{ style: { base: { fontSize: '16px', lineHeight: '40px' } } }}
                                                                        className="form-control"
                                                                    />
                                                                </div>
                                                            </Col>

                                                            <Col xs={6}>
                                                                <div className="customInput">
                                                                    <InputLabel value="CVC" />
                                                                    <CardCvcElement
                                                                        options={{ style: { base: { fontSize: '16px', lineHeight: '40px' } } }}
                                                                        className="form-control"
                                                                    />
                                                                </div>
                                                            </Col>
                                                        </Row>

                                                        <div className="custom_checkbox">
                                                            <input className="custom_checkbox_input form-check-input" type="checkbox" value="" id="rememberPaymentInformation" />
                                                            <label className="custom_checkbox_label mb-0" htmlFor="rememberPaymentInformation" >
                                                                Remember payment information
                                                            </label>
                                                        </div>
                                                        <p className='black_text font-semibold'>By placing this order I understand that I am purchasing a license to access this product under the terms provided and not ownership of the product.
                                                            <NavLink href="/terms" className='text-underline' target="_blank" rel="noreferrer noopener">Details</NavLink>
                                                        </p>
                                                        <CommonButton className="mt-3"
                                                            type="submit"
                                                            title={isSubmitting ? 'Loading' : 'Place Order'}
                                                            fluid
                                                            disabled={isSubmitting}
                                                        />
                                                    </Form>
                                                )}
                                            </Formik>
                                        </div>
                                    </Col>
                                    <Col sm={12} lg={6} className='order-lg-2 order-1 mt-3 mt-lg-0'>
                                        <div className='checkoutContainer_orderSection'>
                                            <div className='orderSummary'>
                                                <p>Order Summary</p>
                                                <p>USD</p>
                                            </div>
                                            <div className='checkoutContainer_orderSection_items'>
                                                {cartItems.map((item) => {
                                                    const title = item?.plan?.title || item?.product?.title || "Untitled";
                                                    const billingType = item?.billing_type;
                                                    const isSubscription = item?.order_type === "subscription";

                                                    const formattedTitle = isSubscription
                                                        ? `TradeReply - ${title} Plan (${billingType === 'monthly' ? 'Monthly' : 'Annual'})`
                                                        : title;

                                                    const accessDuration = isSubscription
                                                        ? billingType === 'monthly'
                                                            ? 'Renews every 30 days'
                                                            : 'Renews every 12 months'
                                                        : item?.duration || 'One-time access';

                                                    return (
                                                        <div key={item.id} className="checkoutContainer_orderSection_items_box">
                                                            <div className="d-flex gap-3">
                                                                <img
                                                                    className="itemImg"
                                                                    src={
                                                                        isSubscription
                                                                            ? "https://cdn.tradereply.com/dev/site-assets/tradereply-square-logo-black.svg"
                                                                            : item?.product?.image || "/default.png"
                                                                    }
                                                                    alt="Cart Image"
                                                                />
                                                                <div>
                                                                    <p className="itemName">{formattedTitle}</p>
                                                                    <p className="itemFormat">
                                                                        Format: <span>{isSubscription ? "Digital Subscription" : "Digital Product"}</span>
                                                                    </p>
                                                                    <p className="itemDuration">
                                                                        Access Duration: <span>{accessDuration}</span>
                                                                    </p>
                                                                    {isSubscription && (
                                                                        <div className="itemLicense">
                                                                            <DeviceMobileSpeaker />
                                                                            <p>{item?.is_free_subscription ? 'This is a 30-day free trial' : 'Paid plan subscription'}</p>
                                                                            <BlackErrorCircle />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                            <div className="checkoutContainer_orderSection_items_box_right">
                                                                <p className="itemName text-end">Total charge:</p>
                                                                {isSubscription && item?.is_free_subscription ? (
                                                                    <p className="mb-0 order-md-1 order-2 text-end small">
                                                                        <strong>Today's Charge:</strong> $0.00<br />
                                                                        30-day trial starts now.<br />
                                                                        Billed <strong>${item.price}</strong> on <strong>{nextBillingDate}</strong>.
                                                                    </p>
                                                                ) : (
                                                                    <p className="mb-0 order-md-1 order-2">${item?.price}</p>
                                                                )}
                                                                <button className="mb-0 order-md-2 order-1"
                                                                    onClick={() => handleRemove(item.id)}
                                                                >Remove</button>
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                            <div className='subtotal'>
                                                <p>Subtotal:</p>
                                                <p>${formattedSubtotal}</p>
                                            </div>
                                            <div className='tax'>
                                                <p>Tax:</p>
                                                <span>calculated in checkout</span>
                                            </div>
                                            <div className='orderTotal'>
                                                <p>Order Total:</p>
                                                <p>${formattedSubtotal}</p>
                                            </div>
                                            <div className='orderTotal'>
                                                <p>Billed:</p>
                                                <p>${cartItems[0]?.price} on {nextBillingDate}</p>
                                            </div>
                                            {cartItems[0]?.is_free_subscription && (
                                                <p className='text-end'>30-day trial starts now.</p>
                                            )}
                                            <p className='text-end'>Cancel Anytime.</p>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </Container>
                </div>
            </HomeLayout>
        </>
    )
}

import React from "react";
import { FaEdit, FaTrash } from "react-icons/fa";

const ListingTable = ({ 
  array, 
  onUpdate, 
  onDelete, 
  categoryFlag = false,
  onSort,
  sortField,
  sortOrder 
}) => {
  const getSortIcon = (field) => {
    if (sortField !== field) return "⬍";
    return sortOrder === "asc" ? "↑" : "↓";
  };

  const limitText = (text, limit = 80) => {
    if (!text) return "";
    if (text.length <= limit) return text;

    const trimmedText = text.substring(0, limit);
    const lastSpaceIndex = trimmedText.lastIndexOf(" ");

    return lastSpaceIndex > 0
      ? trimmedText.substring(0, lastSpaceIndex) + "..."
      : trimmedText + "...";
  };

  return (
    <table className="table">
      <thead>
        <tr>
          <th>
            <input type="checkbox" />
          </th>
          {!categoryFlag && array[0]?.primary_category && (
            <th onClick={() => onSort("primary_category.title")}>
              Category {getSortIcon("primary_category.title")}
            </th>
          )}
          <th onClick={() => onSort("title")}>
            Name {getSortIcon("title")}
          </th>
          <th>Description</th>
          {array[0]?.count != null && (
            <th onClick={() => onSort("count")}>
              Count {getSortIcon("count")}
            </th>
          )}
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        {array.map((item, index) => (
          <tr key={index}>
            <td>
              <input type="checkbox" />
            </td>
            {!categoryFlag && (
              <td>
                {item?.primary_category ? item.primary_category.title : "N/A"}
              </td>
            )}
            <td>{item?.title}</td>
            <td>
              <span>
                {item?.count != null
                  ? limitText(item?.content)
                  : limitText(item?.summary)}
              </span>
            </td>
            {item?.count != null && <td>{item.count}</td>}
            <td>
              <button
                onClick={() => onUpdate(item)}
                style={{ marginRight: "10px", cursor: "pointer" }}
              >
                <FaEdit color="blue" size={16} />
              </button>
              <button
                onClick={() => onDelete(item?.id, item?.slug)}
                style={{ cursor: "pointer" }}
              >
                <FaTrash color="red" size={16} />
              </button>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default ListingTable;

@use "../theme/var";

.admin_layout {
  display: flex;
  flex-wrap: wrap;

  &_sidebar {
    background-color: var.$clr031940;

    @media (width <=767px) {
      width: 100%;
    }

    .admin_sidebar {
      width: 280px;
      box-shadow: 5px 0px 10px 0px #00000026;
      position: sticky;
      top: 89px;
      height: calc(100vh - 89px);
      padding: 1.25rem 1rem;
      overflow-y: auto;

      @media (max-width: 767px) {
        padding-bottom: 0;
        overflow-x: hidden;
        width: 100%;
      }

      @media (max-width: 1279px) {
        width: 250px;
        padding: 10px 10px;
      }

      @media (max-width: 991px) {
        width: 80px;
      }

      @media (max-width: 767px) {
        width: 100%;
        position: relative;
        top: auto;
        height: auto;
        padding: 0 50px;
        overflow-y: hidden;
        overflow-x: hidden;
        display: flex;
        justify-content: center;

        &_wrapper {
          display: flex;
          overflow-x: scroll;
          overflow-y: hidden;
          padding: 10px 0;
          gap: 10px;
          // max-width: 260px;
          width: 100%;

          &::-webkit-scrollbar {
            display: none;
          }

          .linkList {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  &_content {
    position: relative;
    padding: 0 30px 5rem;
    width: calc(100% - 280px);
    background: linear-gradient(180deg, #073992 -15.02%, #0557a3 54.89%);

    @media screen and (max-width: 1279px) {
      padding: 0 0 1.25rem;
      width: calc(100% - 250px);

      .container {
        padding-left: 15px;
        padding-right: 15px;
      }
    }

    @media screen and (max-width: 991px) {
      width: calc(100% - 80px);
    }

    @media screen and (max-width: 767px) {
      width: 100%;
    }
  }

  @media screen and (max-width: 1199px) {
    .py-40 {
      padding-top: 24px;
      padding-bottom: 24px;
    }

    .pb-60 {
      padding-bottom: 24px;
    }

    .pt-40 {
      padding-top: 24px;
    }
  }
}

.trade_manager {
  &_btns {
    .btn-style {
      min-height: 70px;
      text-transform: uppercase;
      font-size: 28px;
      font-weight: bold;
      line-height: 34.66px;
      letter-spacing: -1px;

      @media (max-width: 1199px) {
        min-height: 60px;
        font-size: 20px;
      }

      @media (max-width: 767px) {
        min-height: 50px;
      }

      @media (max-width: 575px) {
        width: 100% !important;
      }

      svg {
        transition: all ease-in-out 0.3s;
        width: 28px;

        @media screen and (max-width: 1199px) {
          width: 21px;
        }
      }
    }
  }

  &_entrylist {
    &_box {
      border: 2px solid var.$baseclr;
      border-radius: 0.625rem;
      padding: 1.25rem;
      margin-top: 1.25rem;
      display: flex;
      background: radial-gradient(50% 50% at 50% 50%,
          rgba(0, 185, 255, 0.2) 21.5%,
          rgba(0, 83, 153, 0.2) 100%),
        linear-gradient(135deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.05) 47.5%,
          rgba(255, 255, 255, 0) 100%);
      text-transform: uppercase;
      cursor: pointer;
      transition: background 0.3s ease;

      &:hover {
        background: radial-gradient(50% 50% at 50% 50%,
            #00b9ff66 21.5%,
            #00539966),
          linear-gradient(135deg, #fff0, #ffffff20 47.5%, #fff0);
      }

      h5 {
        font-size: 1.5rem;
        font-weight: 800;
        min-width: 300px;

        @media (max-width: 1199px) {
          font-size: 1.25rem;
          min-width: 200px;
        }

        @media (max-width: 767px) {
          font-size: 1rem;
          min-width: auto;
        }

        &:nth-child(2) {
          margin-left: 20px;
        }
      }
    }
  }

  &_trade_entry {
    &_box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 1.25rem;
      padding: 1rem 1.25rem;
      border-radius: 1.25rem !important;
      cursor: pointer;

      @media (max-width: 1199px) {
        padding: 10px 10px;
      }

      &_headtext {
        @media (max-width: 767px) {
          flex-wrap: wrap;
        }
      }

      h5 {
        font-size: 1.5rem;
        font-weight: 800;
        text-transform: uppercase;

        @media (max-width: 1279px) {
          font-size: 1rem;
        }

        @media (max-width: 1199px) {
          font-size: 0.875rem;
          margin-left: 10px;
        }

        @media (max-width: 767px) {
          padding: 4px 0;
        }

        &:last-child {
          margin-left: 0;
        }
      }

      .solidArrow {
        transform: rotate(0deg);

        @media screen and (max-width: 1199px) {
          svg {
            width: 20px;
          }
        }

        &.infoIcon {
          svg {
            width: 37px;
            height: 37px;

            circle {
              fill: var.$redlightclr;
            }
          }
        }
      }

      .endArrow {
        transform: rotate(180deg);
        margin-left: 1.5rem;

        @media screen and (max-width: 1199px) {
          margin-left: 10px;
        }
      }
    }
  }
}

.trade_builder_card_body_wrapper {
  padding: 1rem 1.25rem;
}

.head_btns {
  display: flex;
}

.blur-effect {
  filter: blur(10px);
  pointer-events: none;
}

.overlay-message {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.auth-blur-effect {
  filter: blur(10px);
  pointer-events: none;
}

.auth-overlay-message {
  cursor: pointer;
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.459);
  transition: opacity 0.3s ease-in-out;
}

.AuthOverlayRound {
  border-radius: 1.25rem;
}

.auth-overlay-message p {
  text-align: center;
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.auth-overlay-message a {
  color: #00adef;
}

@media screen and (width <=991px) {
  .auth-hide .admin_layout_sidebar {
    display: none;
  }

  .auth-hide .admin_layout_content {
    width: 100%;
  }
}

.trade_head {
  display: flex;
  align-items: center;
  position: relative;
  padding: 1.5rem 0px;

  @media screen and (max-width: 991px) {
    justify-content: unset;
  }

  .common_heading {
    h2 {

      @media screen and (max-width: 1599px) {
        font-size: 35px;
      }

      @media screen and (max-width: 1279px) {
        font-size: 28px;
      }
    }
  }

  &_title {
    display: flex;
    flex-wrap: wrap;

    @media screen and (max-width: 767px) {
      justify-content: center;
    }

    h4 {
      margin-right: 10px;
      margin-bottom: 10px;

      @media screen and (max-width: 1599px) {
        font-size: 18px;
        line-height: normal;
      }
    }
  }

}

.setting-wheel-popup {
  .heading-container {
    position: relative;

    .scrollable_tabs {
      display: flex;
      gap: 0.5rem;
      overflow-x: auto;
      scroll-behavior: smooth;
      flex: 1;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .heading {
      background-color: #0b2c62;
      padding: 10px 30px;
      width: fit-content;
      border-radius: 15px 15px 0 0;
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;

      &.active {
        background-color: #031940;
      }

      p {
        font-size: 20px;
        font-weight: 700;
        white-space: nowrap !important;

        @media (width<=550px) {
          font-size: 16px;
        }
      }
    }

    .move-pre-arrow {
      position: absolute;
      left: 0px !important;
      height: 44px !important;
    }

    .move-next-arrow {
      position: absolute;
      top: 0px !important;
      right: 0px !important;
      height: 44px !important;
    }
  }

  .popup_card {
    background-color: #031940;
    padding: 20px 15px;
    border-radius: 0 0 25px 25px;

    .confirmation {
      margin-bottom: 1rem;

      .head {
        display: flex;
        gap: 10px;
        justify-content: space-between;
        align-items: start;
      }

      .title {
        font-size: 20px;
        font-weight: 600;

        @media (width<=550px) {
          font-size: 16px;
        }

        @media (width<=350px) {
          font-size: 14px;
        }
      }

      button {
        min-height: 40px
      }

      .small-btn {
        min-height: 30px !important;
        font-size: 14px;
        padding: .5rem 1rem !important;
      }
    }

    .innerCard {
      background-color: #283f68;
      border-radius: 0 0 20px 20px;
      border-top: 1px solid #ffffff80;
      margin-bottom: 10px;

      .portfolio {
        padding: 10px;
        border-bottom: 1px solid #ffffff80;

        .greenCircle {
          width: 16px;
          height: 16px;
          background-color: #32cd33;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .redCircle {
          width: 16px;
          height: 16px;
          background-color: #ff696a;
          border-radius: 50%;
          flex-shrink: 0;
        }

        span {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .add_strategies,
      .add_filters {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 7px 9px;
        border-bottom: 1px solid #ffffff80;

        .dashed_design {
          background-color: #ffffff1a;
          border-radius: 10px;
          min-height: 20px;
          min-width: 20px;
          border: 1px dashed #fff !important;
        }

        .header_title {
          font-size: 14px;
          font-weight: 600;
        }

        .count_add {
          font-size: 14px;
          font-weight: 600;
        }
      }

      .show_strategies {
        .include_trades {
          background-color: #031940;
          padding: 6px 10px;
          border-radius: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          gap: 5px;

          .head {
            display: flex;
            gap: 0.45rem;
            align-items: center;
          }

          &_circle {
            width: 14px;
            height: 14px;
            background-color: white;
            border-radius: 50%;
            flex-shrink: 0;
          }

          span {
            font-size: 14px;
            font-weight: 600;
          }

          img {
            height: 14px;
          }
        }
      }

      .strategies {
        padding: 10px;

        .cardHeading {
          display: flex;
          gap: 10px;
          align-items: center;
          padding-bottom: 10px;
          border-bottom: 1px solid #ffffff80;

          p {
            font-size: 16px;
            font-weight: 700;
          }
        }

        .add-strategies-dropdown {
          background-color: #fff;
          width: 100%;
          border-bottom-left-radius: 20px;
          border-bottom-right-radius: 20px;
          border-bottom: 1px solid #031940;
          position: absolute;
          top: 44px;
          left: 0px;
          z-index: 100;

          .header {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            border-bottom: 1px solid #0000000d !important;

            &:hover {
              background-color: #f9f9f9;
            }

            p {
              color: #000000;
              font-size: 16px;
              font-weight: 700;
              padding-left: 5px;

              @media (width<=550px) {
                font-size: 14px;
              }
            }
          }

          .show-dropdown-strategies {
            position: relative;
            display: flex;
            gap: 8px;
            justify-content: space-between;
            align-items: center;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            border-bottom: 1px solid #0000000d !important;
            cursor: pointer;

            &:last-child {
              border-radius: 0 0 20px 20px;
            }

            &:hover {
              background-color: #f9f9f9;
            }

            label {
              padding: 12px 8px 12px 12px;
              cursor: pointer;

              @media (width<=350px) {
                padding: 5px 10px;
              }
            }

            .name {
              text-transform: uppercase;
              color: #000000;
              font-size: 14px;
              font-weight: 600;
            }

            .TripleDotBtn {
              height: 26px;
              width: 25px;
              transition: all 0.2s ease-in-out;
              display: grid;
              place-items: center;
              border-radius: 50%;

              &:hover {
                background-color: rgba(208, 207, 207, 0.426);
              }

              &:active {
                background-color: rgba(208, 207, 207, 0.771);
              }
            }
          }

          .btns {
            background-color: #283f68;
            padding: 15px;
            border-top: 1px solid #ffffff33;
            display: flex;
            justify-content: end;
            gap: 10px;
            font-size: 16px;
            font-weight: 600;
            border-radius: 0 0 20px 20px;

            .confirm {
              color: #00adef;
            }

            .cancel {
              color: #ffffff;
            }
          }

          .TripleDotBtn {
            height: 26px;
            width: 26px;
            transition: all 0.2s ease-in-out;
            display: grid;
            place-items: center;
            border-radius: 50%;
            flex-shrink: 0;

            &:hover {
              background-color: rgba(208, 207, 207, 0.426);
            }

            &:active {
              background-color: rgba(208, 207, 207, 0.771);
            }
          }

          .dropdownlist {
            display: flex;
            align-items: center;
            gap: 14px;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 14px;
            font-weight: 600;

            img {
              width: 15px !important;
              min-width: 15px !important;
            }

            span {
              text-align: left;
              flex: 1;
            }
          }
        }
      }
    }
  }
}

.modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9998;

  .search_section,
  .modal-body {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 1000px;
    background-color: #031940;
    padding: 20px;
    border-radius: 15px;
    max-height: 90vh;
    border: 1px solid #00adef;

    @media (max-width: 1023px) {
      width: 90%;
    }

    h4 {
      font-size: 28px;
      font-weight: 400;
      margin-bottom: 20px;
    }

    .search_header {
      margin-bottom: 20px;

      .closing_Section {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-shrink: 0;
        height: 100%;

        @media (max-width: 1023px) {
          padding-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }

      .search {
        height: 100%;
      }

      p {
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
      }

      span {
        white-space: nowrap;
        color: #ffffff99;
      }

      .btn-style {
        min-width: 100%;
      }

      button {
        span {
          color: #fff !important;
        }
      }
    }

    .btn_include {
      background-color: #fff3;
      border-radius: 10px;
      padding: 5px;
      width: 100%;
      font-weight: 600;
      transition: all 0.4s ease-in-out;
      margin-bottom: 20px;

      &:hover {
        background-color: #32cd33;
      }

      &.active {
        background-color: #32cd33;
      }
    }

    .btn_exclude {
      background-color: #fff3;
      border-radius: 10px;
      padding: 5px;
      width: 100%;
      font-weight: 600;
      transition: all 0.4s ease-in-out;
      margin-bottom: 20px;

      &:hover {
        background-color: #ff696a;
      }

      &.active {
        background-color: #ff696a;
      }
    }

    .search {
      width: 100%;
      background-color: #ffffff33;
      padding: 10px 20px;
      border-radius: 15px;
      display: flex;
      gap: 10px;

      input {
        background-color: transparent;
        width: 100%;
        height: 100%;
        color: #ffffff99;

        &:focus {
          box-shadow: none;
          outline: 0;
        }
      }
    }

    .scope_section_wrapper {
      position: relative;
      display: flex;
      align-items: center;
      border-bottom: 2px solid white;
    }

    .scope_section {
      display: flex;
      overflow-x: auto;
      border-bottom: 2px solid white;

      &::-webkit-scrollbar {
        display: none;
      }

      .active {
        background-color: #00adef33;
        border-bottom: 2px solid #00adef;
      }

      .scopeName {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 10px;
        cursor: pointer;
        transition: 0.3s all ease-in-out;

        p {
          font-size: 18px;
          font-weight: 400;
          white-space: nowrap !important;

          @media (width <=550px) {
            font-size: 14px;
          }

          @media (551px <=width <=900px) {
            font-size: 16px;
          }
        }

        .scopeCount {
          font-weight: 600;
          background-color: #fff;
          width: fit-content;
          color: #000;
          border-radius: 50px;
          padding: 5px 10px;
        }

        &:hover {
          background-color: #00adef33;
        }
      }
    }

    .scope_content {
      .left_side {
        display: flex;
        padding-right: 20px;

        .active {
          background-color: #00adef33;
          border-bottom: 2px solid #00adef;
        }

        .scopeCount {
          font-weight: 600;
          background-color: #fff;
          width: fit-content;
          color: #000;
          border-radius: 50px;
          padding: 5px 10px;

          @media (max-width: 390px) {
            padding: 4px 7px;
            font-size: 13px;
          }
        }

        .scope_dimension,
        .scope_metrices {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 20px;
          cursor: pointer;
          transition: 0.3s all ease-in-out;
          gap: 10px;

          @media (max-width: 550px) {
            padding: 15px 10px;
          }

          @media (width <=550px) {
            font-size: 14px;
          }

          @media (551px <=width <=900px) {
            font-size: 16px;
          }

          &:hover {
            background-color: #00adef33;
          }

          svg {
            transform: rotate(-90deg);
          }
        }
      }

      .right_side {
        background-color: #fff;
        width: 100%;
        overflow-y: auto;
        height: 300px;
        min-height: calc(90vh - 360px) !important;
        max-height: calc(90vh - 360px) !important;

        .default {
          font-size: 20px;
          color: #000;
          font-weight: 600;
          text-align: center;
        }

        .scope_dimension_show,
        .scope_metrices_show {
          display: flex;
          gap: 20px;
          align-items: center;
          padding: 10px 20px;
          color: #000000;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          border-bottom: 1px solid #0000000d !important;

          @media (width<=400px) {
            padding: 5px 20px !important;
          }

          &:hover {
            background-color: #f9f9f9;
          }

          .custom_checkbox_input {
            height: 20px !important;
            width: 20px !important;
            border: 1px solid #00adef !important;
            border-radius: 5px !important;
            cursor: pointer;
          }

          .name {
            text-transform: uppercase;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
          }
        }

        .active_row {
          background-color: rgb(224 224 224);

          &:hover {
            background-color: rgb(224 224 224);
          }
        }
      }
    }

    @keyframes fadeOut {
      0% {
        opacity: 0.9;
      }

      50% {
        opacity: 1;
      }

      90% {
        opacity: 0.9;
      }

      100% {
        opacity: 0;
      }
    }

    .modal-footer-btn {
      min-height: 40px !important;
      font-size: 18px;
    }
  }
}
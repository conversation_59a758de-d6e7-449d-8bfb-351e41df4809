"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function NavLink({ className = "", children, href, ...props }) {
  const pathname = usePathname();

  const isActive = pathname === href;
  return (
    <Link
      href={href}
      {...props}
      className={`${className} ${isActive ? "active" : ""}`}
    >
      {children}
    </Link>
  );
}

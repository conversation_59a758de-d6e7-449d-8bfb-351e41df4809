@import "../../../assets/theme/_var.scss";


.education {

    @media (max-width: 991px) {
        padding-top: 40px !important;
    }

    .container {
        max-width: 1080px;
    }

    &_heading {
        p {
            font-size: 1.25rem;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: -0.1px;
            margin: 30px 0;

            @media (max-width: 991px) {
                font-size: 1rem;
                line-height: 22px;
                margin: 20px 0;
            }
        }
    }

    &_search {
        .commonSearch {
            margin: 0 auto;
            max-width: 400px;

            .form-control {
                width: 100%;
                // border-radius: 10rem;
                // padding-left: 1.25rem;
                // padding-right: 5rem;
            }

            // .onlyIcon {
            //     left: auto;
            //     right: 10px;
            //     width: 50px;
            //     height: 50px;
            //     border-radius: 50%;
            //     display: flex;
            //     align-items: center;
            //     justify-content: center;
            //     background-color: #4B8EF9;

            //     @media screen and (max-width: 991px) {
            //         right: 10px;
            //         width: 36px;
            //         height: 36px;

            //         svg {
            //             width: 16px;
            //         }
            //     }
            // }
        }
    }

    &_fliters {
        padding: 30px 0 80px;

        @media (max-width: 991px) {
            padding: 20px 0 30px;
        }

        @media (max-width: 767px) {
            padding: 20px 0 10px;
        }

        &_inner {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;

            @media (max-width: 767px) {
                margin-bottom: 20px;
            }
        }

        &_boxbutton {
            width: 30px;
            height: 30px;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            margin-bottom: 8px;
            color: $white;
            font-size: 1.125rem;
            font-weight: 600;
            line-height: 1.5rem;
            letter-spacing: -0.1px;
            background-color: $bluelightclr;
            border: 0;
            transition: all ease-in-out .3s;

            @media (max-width: 767px) {
                width: 26px;
                height: 26px;
                font-size: 0.875rem;
            }

            &:last-child {
                margin-right: 0;
            }

            &:hover,
            &.active {
                background-color: $baseclr;
            }
        }

        &_boxadd {
            padding: 5px 15px;
            background-color: $baseclr;
            border-radius: 15px;
            display: inline-flex;
            align-items: center;

            h6 {
                color: $white;
            }
        }
    }

    &_pagination {
        display: flex;
        justify-content: flex-end;
    }

    .education {
        &_term {
            &_head {
                padding: 10px 5px;
                margin: 0;
            }

            &_list {
                border-top: 1px solid $borderclr;
                padding: 15px 5px;
                margin: 0;

                &:last-child {
                    border-bottom: 1px solid $borderclr;
                }

                p {
                    color: #f5f5f5;
                    font-size: 15px;
                    font-weight: 400;
                    line-height: 22.5px;
                    text-align: left;

                    @media (max-width: 767px) {
                        margin-top: 1.25rem;
                    }
                }

                .read_more_button {
                    min-height: 46px;
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 26px;
                    min-width: 120px;
                    border-radius: 10px;
                    padding: 8px 1rem;
                }
            }
        }
    }
}
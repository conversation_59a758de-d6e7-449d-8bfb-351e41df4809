<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('portfolio_field_definitions', function (Blueprint $table) {
            $table->string('account_field_value')->nullable()->after('account_field');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('portfolio_field_definitions', function (Blueprint $table) {
            $table->dropColumn(['account_field_value']);
        });
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionFeature extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    // Relationship with SubscriptionFeaturePlan (One-to-Many)
    public function featurePlans()
    {
        return $this->hasMany(SubscriptionFeaturePlan::class);
    }

    // Get Subscriptions for a Feature (Many-to-Many via Pivot Table)
    public function subscriptions()
    {
        return $this->belongsToMany(Subscription::class, 'subscription_feature_plans')
            ->withPivot('feature_limit'); // Include feature limits in the pivot relationship
    }
}

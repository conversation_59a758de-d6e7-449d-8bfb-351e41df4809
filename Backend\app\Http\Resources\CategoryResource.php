<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id ?? null,
            'title' => $this->title ?? null,
            'content' => $this->content ?? null,
            'slug' => $this->slug ?? null,
            'database_field' => $this->database_field ?? null,
            'created_at' => $this->created_at ?  $this->created_at->toDateTimeString() : null,
            'updated_at' => $this->updated_at ?  $this->updated_at->toDateTimeString() : null,
            'primary_count' => $this->primary_count ?? 0,
            'secondary_count' => $this->secondary_count ?? 0,
            'count' => ($this->primary_count ?? 0) + ($this->secondary_count ?? 0),
            ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\ThirdPartyProviders;
use Illuminate\Http\Request;

class ThirdPartyProvidersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //
        $key = $request->key;
        
        $providers = ThirdPartyProviders::where('name', 'LIKE', "%{$key}%")->get();

        if($providers){
            return response()->json([
                'success' => true,
                'message' => 'Providers fetch data Sucessfully',
                'data' => [
                    'providers' => $providers,
                ],
            ]);
        }    
        else{
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch Providers',
                'data' => [
                    'providers' => [],
                ],
            ]);

        }    
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ThirdPartyProviders $thirdPartyProviders)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ThirdPartyProviders $thirdPartyProviders)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ThirdPartyProviders $thirdPartyProviders)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ThirdPartyProviders $thirdPartyProviders)
    {
        //
    }
}

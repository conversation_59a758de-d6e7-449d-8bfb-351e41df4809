@use "../theme/var";

.howit_works {
  padding: 100px 0;
  background-color: var.$clr001331;
  position: relative;
  z-index: 1;

  @media screen and (max-width: 767px) {
    padding: 50px 0;
  }

  .icons_big_tabs {
    margin-top: 70px;

    @media screen and (max-width: 767px) {
      margin-top: 2rem;
    }

    .tab-content {
      margin-top: 130px;

      @media screen and (max-width: 991px) {
        margin-top: 3rem;
      }
    }

    .howit_content {
      max-width: 850px;
      margin: 0 auto;

      figure {
        img {
          max-width: 210px;

          @media screen and (max-width: 991px) {
            max-width: 150px;
          }

          @media screen and (max-width: 767px) {
            max-width: 100px;
            margin: 0 auto 10px;
            display: table;
          }
        }
      }

      &_text {
        padding-left: 50px;

        @media screen and (max-width: 767px) {
          padding-left: 0;
          text-align: center;
        }

        h3 {
          @media screen and (max-width: 991px) {
            font-size: 36px;
            font-weight: 800;
            line-height: 45px;
          }
        }

        p {
          font-size: 24px;
          font-weight: 600;
          line-height: 36px;
          margin: 1.25rem 0;

          @media screen and (max-width: 767px) {
            font-size: 18px;
            line-height: 26px;
          }
        }
      }
    }
  }
}

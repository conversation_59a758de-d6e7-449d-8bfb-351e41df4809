<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Blog>
 */
class BlogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(6), 
            'content' => $this->faker->paragraph(5), 
            'slug' => Str::slug($this->faker->unique()->sentence(3)), 
            'feature_image' => 'uploads/education/feature_images/default.png', 
            'tags' => implode(',', $this->faker->words(5)), 
            'primary_category_id' => \App\Models\Category::inRandomOrder()->first()->id ?? 1, 
            'is_featured' => $this->faker->boolean(20), 
            'clicks' => $this->faker->numberBetween(1 , 100), 
        ];
    }
}

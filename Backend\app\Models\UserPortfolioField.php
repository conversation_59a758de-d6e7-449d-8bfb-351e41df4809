<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPortfolioField extends Model
{
    protected $fillable = [
          'user_id',
          'portfolio_field_definition_id',
          'value'
    ];

    public function definition()
    {
        return $this->belongsTo(PortfolioFieldDefinition::class, 'portfolio_field_definition_id');
    }
}


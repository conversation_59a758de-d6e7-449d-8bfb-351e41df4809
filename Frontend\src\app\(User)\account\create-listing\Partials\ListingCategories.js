import React, { useState, useRef, useEffect } from 'react'
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon, WhiteCrossCircle } from "@/assets/svgIcons/SvgIcon";
import { ChevronRight, ChevronLeft } from 'lucide-react';

const categories = [
    {
        label: "Courses & Webinars",
        children: [
            {
                label: "Trading Strategies Courses",
                children: [
                    { label: "Day Trading Courses" },
                    { label: "Swing Trading Courses" },
                    { label: "Options Trading Courses" },
                    { label: "Forex Trading Courses" },
                    { label: "Crypto Trading Courses" }
                ]
            },
            {
                label: "Analysis & Research Courses",
                children: [
                    { label: "Technical Analysis Masterclasses" },
                    { label: "Fundamental Analysis Courses" }
                ]
            },
            {
                label: "Algorithmic & Bot Trading Courses",
                children: [
                    { label: "Algorithmic & Bot Trading Courses" }
                ]
            },
            {
                label: "Risk Management & Psychology",
                children: [
                    { label: "Risk Management & Psychology Training" }
                ]
            },
            {
                label: "Webinars & Workshops",
                children: [
                    { label: "Live Webinars & Workshops" },
                    { label: "On-Demand Recorded Webinars" }
                ]
            }
        ]
    },
    {
        label: "Ebooks & Guides",
        children: [
            {
                label: "Trading Strategy Guides",
                children: [
                    { label: "Trading Strategy Ebooks" },
                    { label: "Technical & Chart Analysis Guides" },
                    { label: "Fundamental Analysis Handbooks" }
                ]
            },
            {
                label: "Risk Management & Psychology",
                children: [
                    { label: "Risk Management & Psychology Ebooks" }
                ]
            },
            {
                label: "Algorithmic & Bot Trading Guides",
                children: [
                    { label: "Algorithmic & Bot Trading Guides" }
                ]
            },
            {
                label: "Stock, Forex, and Crypto Investing",
                children: [
                    { label: "Stock Market Investing Books" },
                    { label: "Forex & Crypto Trading Guides" }
                ]
            },
            {
                label: "Research & Reporting",
                children: [
                    { label: "Case Studies & Market Analysis" },
                    { label: "Exclusive PDF Reports & Whitepapers" }
                ]
            },
            {
                label: "Tools & Templates",
                children: [
                    { label: "Trading Journal Templates & Worksheets" }
                ]
            }
        ]
    },
    {
        label: "Trading Tools & Indicators",
        children: [
            {
                label: "Indicators & Scripts",
                children: [
                    { label: "TradingView Indicators & Scripts" },
                    { label: "MetaTrader (MT4/MT5) Indicators & Expert Advisors" }
                ]
            },
            {
                label: "Bots & Automated Systems",
                children: [
                    { label: "Custom Algorithmic Trading Bots" },
                    { label: "Grid Trading & AI-Based Bots" },
                    { label: "Crypto Arbitrage & Automated Strategies" }
                ]
            },
            {
                label: "Performance Tools",
                children: [
                    { label: "Backtesting Tools & Strategy Optimizers" },
                    { label: "Trade Logging & Performance Trackers" }
                ]
            },
            {
                label: "Calculators & Alerts",
                children: [
                    { label: "Risk Management Calculators" },
                    { label: "Options Strategy Calculators" },
                    { label: "Automated Trading Signals & Alerts" }
                ]
            }
        ]
    },
    {
        label: "Market Research & Insights",
        children: [
            {
                label: "Market Reports",
                children: [
                    { label: "Daily & Weekly Market Reports" },
                    { label: "Sector-Specific Research Reports" },
                    { label: "Macroeconomic Analysis & Forecasts" }
                ]
            },
            {
                label: "Trade Ideas & Strategies",
                children: [
                    { label: "Stock & Crypto Trade Ideas" },
                    { label: "Backtested Strategy Reports" }
                ]
            },
            {
                label: "Advanced Data & Analysis",
                children: [
                    { label: "Earnings & Fundamental Data Reports" },
                    { label: "Insider Trading & Institutional Flow Analysis" },
                    { label: "AI-Powered Market Predictions" },
                    { label: "On-Chain & Blockchain Data Analysis" },
                    { label: "Custom Research Reports & Data Sets" }
                ]
            }
        ]
    },
    {
        label: "Mentorship & Premium Access",
        children: [
            {
                label: "Coaching & Mentorship",
                children: [
                    { label: "1-on-1 Trading Coaching & Mentorship" },
                    { label: "Direct Messaging & Coaching Packages" }
                ]
            },
            {
                label: "Private Access & Groups",
                children: [
                    { label: "Private Trading Groups & Discord Access" },
                    { label: "Access to Elite Trading Networks" }
                ]
            },
            {
                label: "Exclusive Analysis & Events",
                children: [
                    { label: "Exclusive VIP Market Analysis" },
                    { label: "Exclusive Member-Only Webinars & Events" }
                ]
            },
            {
                label: "Strategy Optimization & Challenges",
                children: [
                    { label: "Strategy Optimization Consultations" },
                    { label: "Trading Challenge & Bootcamps" },
                    { label: "Live Portfolio Reviews & Trade Feedback" }
                ]
            },
            {
                label: "Prop Firm Access",
                children: [
                    { label: "Proprietary Trading Firm Access" }
                ]
            }
        ]
    }
];

export default function ListingCategories() {
    const [level1, setLevel1] = useState(null);
    const [level2, setLevel2] = useState(null);
    const [level3, setLevel3] = useState(null);
    const [openDropdown, setOpenDropdown] = useState(false);
    const [selected, setSelected] = useState([]);
    const dropdownRef = useRef(null);

    useEffect(() => {
        function handleClickOutside(event) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setOpenDropdown(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const toggleDropdown = () => {
        const opening = !openDropdown;
        setOpenDropdown(opening);

        if (opening) {
            if (level3) {
                setLevel1(null);
                setLevel2(null);
            }
        }
    };

    const handleLevel1Click = (item) => {
        setLevel1(item);
        setLevel2(null);
        setLevel3(null);
    };

    const handleLevel2Click = (item) => {
        setLevel2(item);
        setLevel3(null);
    };

    const handleLevel3Click = (item) => {
        const newSel = {
            l1: level1?.label ?? "",
            l2: level2?.label ?? "",
            l3: item.label,
        };

        const exists = selected.some(
            (s) => s.l1 === newSel.l1 && s.l2 === newSel.l2 && s.l3 === newSel.l3
        );
        if (!exists) {
            setSelected((prev) => [...prev, newSel]);
        }

        setLevel3(item);
        setOpenDropdown(false);

        setLevel1(null);
        setLevel2(null);
    };

    const goBackFromLevel2 = () => {
        setLevel1(null);
        setLevel2(null);
    };

    const goBackFromLevel3 = () => {
        setLevel2(null);
        setLevel3(null);
    };

    const removeSelectedItem = (index) => {
        setSelected((prev) => prev.filter((_, i) => i !== index));
    };

    const clearAllSelections = () => {
        setSelected([]);
        setLevel1(null);
        setLevel2(null);
        setLevel3(null);
    };

    const ulClass = level1 && !level2 ? "sub_list" : level2 ? "sub_list" : "";

    return (
        <>
            <div className="d-flex gap-2 align-items-center mb-2">
                <label className="form-label mb-0">Category</label>
                <CommonTooltip
                    className="d-flex align-items-center"
                    content={
                        <>
                            <p >Select up to 5 Level 3 categories.</p>
                            <p>You can choose from up to 2 different Level 1 groups.</p>
                        </>
                    }
                    position="top-left"
                >
                    <SolidInfoIcon />
                </CommonTooltip>
            </div>
            <div className="create-listing-categories" ref={dropdownRef}>
                <div className="field">
                    <span onClick={toggleDropdown} className="category-toggle">
                        Category
                    </span>

                    {openDropdown && (
                        <div className="dropdown">
                            {/* Header with back chevron when viewing level2 or level3 */}
                            {(level1 || level2) && (
                                <div className="dropdown-header">
                                    <button
                                        onClick={level2 ? goBackFromLevel3 : goBackFromLevel2}
                                        className="back-button"
                                        aria-label="Go back"
                                    >
                                        <ChevronLeft />
                                    </button>

                                    <strong className="dropdown-title">
                                        {level2 ? level2.label : level1 ? level1.label : "Select category"}
                                    </strong>
                                </div>
                            )}

                            <ul className={ulClass}>
                                {/* LEVEL 1 */}
                                {!level1 &&
                                    categories.map((item) => (
                                        <li
                                            key={item.label}
                                            onClick={() => handleLevel1Click(item)}
                                            className="dropdown-item"
                                        >
                                            <span>{item.label}</span>
                                            {item.children && <ChevronRight />}
                                        </li>
                                    ))}

                                {/* LEVEL 2 */}
                                {level1 && !level2 &&
                                    level1.children?.map((item) => (
                                        <li
                                            key={item.label}
                                            onClick={() => handleLevel2Click(item)}
                                            className="dropdown-item"
                                        >
                                            <span>{item.label}</span>
                                            {item.children && <ChevronRight />}
                                        </li>
                                    ))}

                                {/* LEVEL 3 */}
                                {level2 &&
                                    level2.children?.map((item) => (
                                        <li
                                            key={item.label}
                                            onClick={() => handleLevel3Click(item)}
                                            className="dropdown-item"
                                        >
                                            <span>{item.label}</span>
                                        </li>
                                    ))}
                            </ul>
                        </div>
                    )}
                </div>

                <div className="selected-values-container">
                    {selected.length > 0 && (
                        <>
                            <div className="selected-values-list">
                                {selected.map((s, i) => (
                                    <div className="selected-values" key={`${s.l1}-${s.l2}-${s.l3}-${i}`}>
                                        <span>
                                            {s.l1} <ChevronRight /> {s.l2} <ChevronRight /> {s.l3}
                                        </span>
                                        <button
                                            className="remove-selected"
                                            onClick={() => removeSelectedItem(i)}
                                            aria-label={`Remove ${s.l3}`}
                                        >
                                            <WhiteCrossCircle />
                                        </button>
                                    </div>
                                ))}
                            </div>

                            <button className="clear-all" onClick={clearAllSelections}>
                                Clear all
                            </button>
                        </>
                    )}
                </div>
            </div>
        </>
    )
}

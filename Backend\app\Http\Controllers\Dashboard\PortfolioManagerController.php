<?php

namespace App\Http\Controllers\Dashboard;

use Carbon\Carbon;
use DateTimeZone;
use Illuminate\Http\Request;
use App\Models\UserPortfolioField;
use Illuminate\Support\Facades\File;
use App\Http\Controllers\Controller;
use App\Models\PortfolioFieldDefinition;
use Illuminate\Support\Facades\Log;

class PortfolioManagerController extends Controller
{
     private $accountDetails = [
        'PORTFOLIO_TIMEZONE',
        'PORTFOLIO_CURRENCY',
        'NUMBER_FORMAT_DISPLAY',
        'CURRENCY_DECIMALS',
        'PERCENTAGE_DECIMALS',
        'PORTFOLIO_STOCK_UNIT_OF_MEASUREMENT',
        'PORTFOLIO_CRYPTO_UNIT_OF_MEASUREMENT'
     ];

     private $cashFlow = [
         'PORTFOLIO_ACCOUNT_INITIAL_BALANCE',
         'PORTFOLIO_ACCOUNT_INITIAL_BALANCE_TYPE',
         'PORTFOLIO_ACCOUNT_AVAILABLE',
         'PORTFOLIO_ACCOUNT_SIZE',
         'PORTFOLIO_ACCOUNT_SIZE_AT_GOAL_CREATION',
         'PORTFOLIO_MANUAL_DEPOSIT',
         'PORTFOLIO_MANUAL_DEPOSIT_TYPE',
         'PORTFOLIO_WITHDRAWAL',
         'PORTFOLIO_TOTAL_CASH',
         'PORTFOLIO_ALLOCATED_CASH',
         'PORTFOLIO_UNALLOCATED_CASH',
         'PORTFOLIO_NET_CASH_CHANGES',
         'PORTFOLIO_CASH_RETENTION_RATIO',
         'PORTFOLIO_CAPITAL_RESERVE',
         'PORTFOLIO_TRADE_RESERVE'
     ];

     private $riskManagement = [
         'PORTFOLIO_MAX_RISK_PERCENTAGE',
         'PORTFOLIO_MAX_RISK_TOLERANCE',
         'PORTFOLIO_ACCOUNT_STOP_LOSS_VALUE',
         'PORTFOLIO_ACCOUNT_STOP_LOSS_PERCENTAGE',
         'PORTFOLIO_ACCOUNT_STOP_RISK_VALUE',
         'PORTFOLIO_ACCOUNT_STOP_RISK_PERCENTAGE',
         'PORTFOLIO_ACCOUNT_FIXED_STOP_VALUE',
         'PORTFOLIO_ACCOUNT_FIXED_STOP_PERCENTAGE'
     ];

     private $growth = [
          'PORTFOLIO_ACCOUNT_GROWTH_GOAL',
          'PORTFOLIO_ACCOUNT_GROWTH_GOAL_FACTOR',
          'PORTFOLIO_ACCOUNT_GROWTH_VALUE_OF_GOAL',
          'PORTFOLIO_ACCOUNT_GROWTH_PERCENTAGE_OF_GOAL',
          'PORTFOLIO_ACCOUNT_GROWTH_FACTOR_OF_GOAL',
          'PORTFOLIO_PROFIT_ALLOCATION_TO_CAPITAL_RESERVE_PERCENTAGE'
     ];

     public function index()
     {
         $fields = PortfolioFieldDefinition::query()
                      ->with(['fieldDefinition', 'userFieldValue'])
                      ->where('account_field', 'YES')
                      ->get()
                      ->keyBy(function ($item) {
                           return strtoupper($item->database_field);
                       });

         return response()->json([
             'account_details' => $this->filterGroup($this->accountDetails, $fields),
             'cash_flow' => $this->filterGroup($this->cashFlow, $fields),
             'risk_management' => $this->filterGroup($this->riskManagement, $fields),
             'growth' => $this->filterGroup($this->growth, $fields),
         ]);
     }

    public function store(Request $request)
    {
        $request->validate([
            'portfolio_field_definition_id' => 'required|exists:portfolio_field_definitions,id',
            'value' => 'nullable|string'
        ]);

        $userId = auth()->id();
        $definition = PortfolioFieldDefinition::find($request->portfolio_field_definition_id);
        $changedField = strtoupper($definition->database_field);

        Log::info('[FORMULA_TRACE] Changed field: ' . $changedField . ' | Value: ' . $request->value);

        $this->saveUserValue($userId, $definition->id, $request->value);
        $this->maybeCloneToInitialBalanceFields($userId, $changedField, $request->value);

        if (in_array($changedField, ['PORTFOLIO_MANUAL_DEPOSIT', 'PORTFOLIO_MANUAL_DEPOSIT_TYPE', 'PORTFOLIO_WITHDRAWAL'])) {
            $this->accumulateCashRelatedFields($userId, $request->value, $changedField);
        }

        $this->calculateAndSaveFormulas($userId, $changedField, $request->value);

        return response()->json(['success' => true]);
    }

    private function accumulateCashRelatedFields($userId, $amount, $changedField)
    {
        $fieldsToAdjust = [
            'PORTFOLIO_ACCOUNT_SIZE',
            'PORTFOLIO_TOTAL_CASH',
            'PORTFOLIO_UNALLOCATED_CASH',
            'PORTFOLIO_NET_CASH_CHANGES'
        ];

        $value = is_numeric($amount) ? floatval($amount) : 0;
        if ($value <= 0) return;

        foreach ($fieldsToAdjust as $fieldName) {
            $fieldDef = PortfolioFieldDefinition::whereRaw('UPPER(database_field) = ?', [$fieldName])->first();
            if (!$fieldDef) continue;

            $existing = UserPortfolioField::where('user_id', $userId)
                ->where('portfolio_field_definition_id', $fieldDef->id)
                ->first();

            $existingValue = $existing->value ?? 0;

            if ($changedField === 'PORTFOLIO_WITHDRAWAL') {
                $withdrawalValue = is_numeric($value) ? floatval($value) : 0;
                $newTotal = floatval($existingValue) - $withdrawalValue;
            } else {
                $newTotal = $existingValue + $value;
            }

            if ($newTotal < 0) $newTotal = 0;

            UserPortfolioField::updateOrCreate(
                [
                    'user_id' => $userId,
                    'portfolio_field_definition_id' => $fieldDef->id
                ],
                [
                    'value' => max($newTotal, 0)
                ]
            );
        }
    }

    private function saveUserValue($userId, $definitionId, $value)
    {
        UserPortfolioField::updateOrCreate(
            ['user_id' => $userId, 'portfolio_field_definition_id' => $definitionId],
            ['value' => max($value, 0)]
        );
    }

    private function maybeCloneToInitialBalanceFields($userId, $changedField, $value)
    {
        $map = [
            'PORTFOLIO_MANUAL_DEPOSIT' => 'PORTFOLIO_ACCOUNT_INITIAL_BALANCE',
            'PORTFOLIO_MANUAL_DEPOSIT_TYPE' => 'PORTFOLIO_ACCOUNT_INITIAL_BALANCE_TYPE'
        ];

        if (!array_key_exists($changedField, $map)) return;

        $targetField = $map[$changedField];

        $targetDef = PortfolioFieldDefinition::whereRaw('UPPER(database_field) = ?', [$targetField])->first();

        if (!$targetDef) return;

        $existing = UserPortfolioField::where('user_id', $userId)
            ->where('portfolio_field_definition_id', $targetDef->id)
            ->first();

        if (!$existing || empty($existing->value)) {
            UserPortfolioField::updateOrCreate(
                ['user_id' => $userId, 'portfolio_field_definition_id' => $targetDef->id],
                ['value' => max($value, 0)]
            );
        }
    }

    private function calculateAndSaveFormulas($userId, $changedField, $value)
    {
        $jsonFile = public_path('TradeReply_Formulas.json');
        $formulaData = json_decode(File::get($jsonFile), true) ?? [];

        $portfolioScopes = collect($formulaData)
            ->map(fn($item) => $item['SCOPES']['PORTFOLIO'] ?? null)
            ->filter()
            ->keyBy('DATABASE FIELD')
            ->toArray();

        if (
            in_array($changedField, $this->riskManagement) &&
            $changedField !== 'PORTFOLIO_MAX_RISK_PERCENTAGE'
        ) {
            return;
        }

        if (in_array($changedField, $this->accountDetails)) {
            return;
        }

        if ($changedField === 'PORTFOLIO_PROFIT_ALLOCATION_TO_CAPITAL_RESERVE_PERCENTAGE') {
            return;
        }

        $maxPasses = 5;
        $lastState = [];

        for ($i = 0; $i < $maxPasses; $i++) {
            $result = $this->runFormulaPass($userId, $portfolioScopes, $changedField, $value);

            $current = collect($result)->pluck('value', 'definition_id')->toArray();
            if ($lastState == $current) break;

            $lastState = $current;
        }
    }

    private function runFormulaPass($userId, array $portfolioScopes, $changedField, $value)
    {
        $userValues = UserPortfolioField::where('user_id', $userId)->pluck('value', 'portfolio_field_definition_id');
        $fieldMap = PortfolioFieldDefinition::all()->pluck('database_field', 'id')->map(fn($v) => strtoupper($v));

        $inputs = [];
        foreach ($userValues as $id => $val) {
            $key = $fieldMap[$id] ?? null;
            if ($key) $inputs[$key] = is_numeric($val) ? floatval($val) : $val;
        }

        $calculatedFields = [];

        $fieldsToAccumulate = [
            'PORTFOLIO_ACCOUNT_SIZE',
            'PORTFOLIO_TOTAL_CASH',
            'PORTFOLIO_UNALLOCATED_CASH',
            'PORTFOLIO_NET_CASH_CHANGES'
        ];

        foreach ($portfolioScopes as $targetField => $scope) {
            $formula = $scope['FORMULA'] ?? null;
            if (!$formula) continue;

            Log::info("[FORMULA_TRACE] Evaluating formula for: {$targetField}");

            $dependentFields = $this->extractFormulaDependencies($formula);

            Log::info("[FORMULA_TRACE] Dependencies for {$targetField}: " . implode(', ', $dependentFields));

            foreach ($dependentFields as $depField) {
                if (in_array($changedField, ['PORTFOLIO_MANUAL_DEPOSIT', 'PORTFOLIO_MANUAL_DEPOSIT_TYPE', 'PORTFOLIO_WITHDRAWAL'])
                    && in_array(strtoupper($targetField), ['PORTFOLIO_ACCOUNT_GROWTH_GOAL', 'PORTFOLIO_ACCOUNT_GROWTH_GOAL_FACTOR'])) {
                    continue;
                }

                if (in_array($changedField, ['PORTFOLIO_ACCOUNT_GROWTH_GOAL', 'PORTFOLIO_ACCOUNT_GROWTH_GOAL_FACTOR'])
                    && in_array(strtoupper($targetField), $fieldsToAccumulate)) {
                    continue;
                }

                if ($changedField == 'PORTFOLIO_WITHDRAWAL'
                    && in_array(strtoupper($targetField), $fieldsToAccumulate)) {
                    continue;
                }

                if (in_array($changedField, ['PORTFOLIO_MANUAL_DEPOSIT', 'PORTFOLIO_MANUAL_DEPOSIT_TYPE', 'PORTFOLIO_WITHDRAWAL'])
                    && strtoupper($targetField) === 'PORTFOLIO_ACCOUNT_GROWTH_VALUE_OF_GOAL'
                    && !isset($inputs['PORTFOLIO_ACCOUNT_GROWTH_GOAL'])
                ) {
                    continue;
                }

                if (in_array($changedField, ['PORTFOLIO_MANUAL_DEPOSIT', 'PORTFOLIO_MANUAL_DEPOSIT_TYPE', 'PORTFOLIO_WITHDRAWAL'])
                    && in_array(strtoupper($targetField), ['PORTFOLIO_ACCOUNT_SIZE_AT_GOAL_CREATION'])) {
                    continue;
                }

                if ($changedField === 'PORTFOLIO_MAX_RISK_PERCENTAGE' &&
                    in_array(strtoupper($targetField), $this->growth)) {
                    continue;
                }

                if ($changedField === 'PORTFOLIO_MAX_RISK_PERCENTAGE' &&
                    in_array(strtoupper($targetField), $this->cashFlow)) {
                    continue;
                }

                if (array_key_exists(strtoupper($depField), $inputs)) {
                    if (strtoupper($targetField) == $changedField) {
                        continue;
                    }

                    $calculated = $this->evaluateFallbackFormula($formula, $inputs);
                    if (!is_null($calculated)) {
                        Log::info("[FORMULA_TRACE] Formula RUN for: {$targetField}");
                        Log::info("[FORMULA_TRACE] → Using inputs: " . json_encode($inputs, true));
                        Log::info("[FORMULA_TRACE] → Formula object: " . json_encode($formula, true));
                        Log::info("[FORMULA_TRACE] → Result: {$calculated}");

                        $fieldDef = PortfolioFieldDefinition::whereRaw('UPPER(database_field) = ?', [strtoupper($targetField)])->first();
                        if ($fieldDef) {
                            $calculatedFields[] = [
                                'definition_id' => $fieldDef->id,
                                'value' => $calculated
                            ];
                            $inputs[strtoupper($fieldDef->database_field)] = $calculated;
                        }
                    }

                }
            }
        }

        foreach ($calculatedFields as $item) {
            $fieldDef = PortfolioFieldDefinition::find($item['definition_id']);
            $fieldKey = strtoupper($fieldDef->database_field ?? '');

            if (in_array($fieldKey, $fieldsToAccumulate)) {
                $alreadyExists = UserPortfolioField::where('user_id', $userId)
                    ->where('portfolio_field_definition_id', $item['definition_id'])
                    ->exists();

                if ($alreadyExists && in_array($changedField, ['PORTFOLIO_MANUAL_DEPOSIT', 'PORTFOLIO_MANUAL_DEPOSIT_TYPE'])) {
                    continue;
                }
            }

            UserPortfolioField::updateOrCreate(
                [
                    'user_id'                       => $userId,
                    'portfolio_field_definition_id' => $item['definition_id']
                ],
                [
                    'value' => max($item['value'], 0)
                ]
            );
        }

        return $calculatedFields;
    }

     private function filterGroup(array $keys, $fields)
     {
         return collect($keys)
             ->map(function ($key) use ($fields) {
                 $field = $fields->get($key);
                 $field->label = $field->fieldDefinition->field_name ?? $field->database_field;
                 $expectedValues = $field->fieldDefinition->expected_values ?? null;

                 if ($key === 'PORTFOLIO_TIMEZONE') {
                        $field->options = $this->getTimezonesForDropdown();
                 } else {
                       $expectedValues = $field->fieldDefinition->expected_values ?? null;
                       $field->options = !empty($expectedValues)
                                      ? array_map('trim', explode(',', $expectedValues))
                                      : [];
                 }

                 $field->value = $field->account_field_value ?? ($field->userFieldValue->value ?? null);
                 $field->hasFormula = $field->has_formula;
                 $field->data_type = $field->fieldDefinition->datatype ?? '';
                 return $field;
             })
             ->filter()
             ->values();
    }

    private function getTimezonesForDropdown()
    {
        $now = Carbon::now();
        $timezones = DateTimeZone::listIdentifiers();
        $list = [];

        foreach ($timezones as $tz) {
            $offset = (new DateTimeZone($tz))->getOffset($now);
            $hours = floor(abs($offset) / 3600);
            $minutes = abs($offset % 3600 / 60);
            $sign = $offset >= 0 ? '+' : '-';
            $utc = sprintf("UTC%s%02d:%02d", $sign, $hours, $minutes);

            $list[] = [
                'label' => "($utc) " . str_replace('_', ' ', $tz),
                'value' => $tz,
                'offset' => $offset, // Store offset for sorting
            ];
        }

        // Sort by UTC offset first (UTC−12:00 to UTC+14:00), then alphabetically by timezone name
        usort($list, function($a, $b) {
            // First sort by offset
            if ($a['offset'] !== $b['offset']) {
                return $a['offset'] - $b['offset'];
            }
            // Then sort alphabetically by timezone name (not the full label)
            return strcmp($a['value'], $b['value']);
        });

        // Remove offset from final result as it's only needed for sorting
        return array_map(function($item) {
            unset($item['offset']);
            return $item;
        }, $list);
    }

    private function evaluateFallbackFormula(array $formula, array $inputs)
    {
        for ($i = 1; $i <= 5; $i++) {
            if (($formula["f$i"] ?? 0) == 1 && isset($formula["f{$i}v"])) {
                return $this->evaluateFormula($formula["f{$i}v"], $inputs);
            }
        }
        return null;
    }

   private function evaluateFormula(array|string $formula, array $inputs)
   {
       if (is_string($formula)) {
           $upper = strtoupper($formula);
           return array_key_exists($upper, $inputs) ? $inputs[$upper] : $formula;
       }

       $op = strtoupper($formula['operation'] ?? '');
       $fields = $formula['fields'] ?? [];
       switch ($op) {
           case 'ADD':
               $values = array_map(function ($f) use ($inputs) {
                   $val = $this->evaluateFormula($f, $inputs);
                   return is_numeric($val) ? floatval($val) : 0;
               }, $fields);

               return array_sum($values);
           case 'SUBTRACT':
               $a = $this->evaluateFormula($fields[0], $inputs);
               $b = $this->evaluateFormula($fields[1], $inputs);
               return (is_numeric($a) ? floatval($a) : 0) - (is_numeric($b) ? floatval($b) : 0);
           case 'MULTIPLY':
               return array_product(array_map(function ($f) use ($inputs) {
                   $val = $this->evaluateFormula($f, $inputs);
                   return is_numeric($val) ? floatval($val) : 1;
               }, $fields));
           case 'DIVIDE':
               $numerator = $this->evaluateFormula($fields[0], $inputs);
               $denominator = $this->evaluateFormula($fields[1], $inputs);

               $num = is_numeric($numerator) ? (float)$numerator : 0;
               $den = is_numeric($denominator) ? (float)$denominator : 0;

               return $den == 0 ? 0 : $num / $den;
           case 'AVERAGE':
               $values = array_map(fn($f) => $this->evaluateFormula($f, $inputs), $fields);
               $numericValues = array_filter($values, fn($v) => is_numeric($v));
               return count($numericValues) ? array_sum($numericValues) / count($numericValues) : null;
           case 'ABS': return abs($this->evaluateFormula($fields[0], $inputs));
           case 'REFERENCE': return $inputs[strtoupper($formula['field'] ?? '')] ?? null;
           case 'WHEN_SET':
               $conditionKey = strtoupper($formula['condition'] ?? '');
               $valueKey = strtoupper($formula['value'] ?? '');

               if (!empty($inputs[$conditionKey])) {
                   return $inputs[$valueKey] ?? null;
               }
               return null;
           case 'IF':
               $cond = $formula['condition'];

               $evaluateCondition = function ($cond) use (&$evaluateCondition, $inputs) {
                   $op = strtoupper($cond['operation'] ?? '');

                   if ($op === 'OR' || $op === 'AND') {
                       $results = array_map(fn($c) => $evaluateCondition($c), $cond['conditions'] ?? []);
                       return $op === 'OR' ? in_array(true, $results, true) : !in_array(false, $results, true);
                   }

                   $field = isset($cond['field']) && is_string($cond['field']) ? strtoupper($cond['field']) : '';
                   $val = $inputs[$field] ?? null;

                   return match ($op) {
                       '=', '==' => $val == $cond['value'],
                       '!=', '<>' => $val != $cond['value'],
                       '>' => $val > $cond['value'],
                       '>=' => $val >= $cond['value'],
                       '<' => $val < $cond['value'],
                       '<=' => $val <= $cond['value'],
                       'IN' => in_array($val, $cond['values'] ?? []),
                       'IS_NULL' => is_null($val) || $val === '',
                       default => false
                   };
               };

               $match = $evaluateCondition($cond);
               return $this->evaluateFormula($match ? $formula['true_case'] : $formula['false_case'], $inputs);
           case 'DATE_FORMAT':
               $field = strtoupper($formula['field']);
               $timestamp = strtotime($inputs[$field] ?? '');
               return $timestamp ? date($formula['format'] ?? 'Y-m-d', $timestamp) : null;
           case 'YEAR': return date('Y', strtotime($inputs[strtoupper($formula['field'])] ?? ''));
           case 'WEEK': return date('W', strtotime($inputs[strtoupper($formula['field'])] ?? ''));
           case 'TIMESTAMPDIFF':
               $unit = $formula['unit'] ?? 'day';
               $start = strtotime($this->evaluateFormula($formula['field1'], $inputs));
               $end = strtotime($this->evaluateFormula($formula['field2'], $inputs));
               if (!$start || !$end) return null;
               return match ($unit) {
                   'hour' => round(($end - $start) / 3600),
                   'day' => round(($end - $start) / 86400),
                   'month' => date('n', $end) - date('n', $start) + 12 * (date('Y', $end) - date('Y', $start)),
                   'year' => date('Y', $end) - date('Y', $start),
                   default => null,
               };
           default: return null;
       }
   }

    private function extractFormulaDependencies(array $formula): array
    {
        $dependencies = [];

        for ($i = 1; $i <= 5; $i++) {
            $flag = $formula["f{$i}"] ?? 0;
            $value = $formula["f{$i}v"] ?? null;

            if ($flag && $value) {
                $dependencies = array_merge($dependencies, $this->scanFieldsFromFormula($value));
            }
        }

        return array_unique(array_map('strtoupper', array_filter($dependencies, 'is_string')));
    }

    private function scanFieldsFromFormula($node): array
    {
        $fields = [];

        if (is_string($node)) {
            $fields[] = $node;
        }

        if (is_array($node)) {
            if (!empty($node['field'])) {
                $fields[] = $node['field'];
            }

            if (isset($node['condition']) && is_string($node['condition'])) {
                $fields[] = $node['condition'];
            }

            if (isset($node['value']) && is_string($node['value'])) {
                $fields[] = $node['value'];
            }

            if (!empty($node['condition']['field'])) {
                $fields[] = $node['condition']['field'];
            }

            if (!empty($node['fields']) && is_array($node['fields'])) {
                foreach ($node['fields'] as $child) {
                    $fields = array_merge($fields, $this->scanFieldsFromFormula($child));
                }
            }

            if (!empty($node['true_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['true_case']));
            }

            if (!empty($node['false_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['false_case']));
            }
        }

        return $fields;
    }

    public function deleteAllEntries()
    {
        $fields = UserPortfolioField::all();

        foreach ($fields as $field) {
            $field->delete();
        }

        return response()->noContent(200);
    }
}

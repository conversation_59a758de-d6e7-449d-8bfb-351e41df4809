<?php

namespace App\Http\Controllers;

use App\Http\Resources\CategoryResource;
use App\Http\Resources\EducationResource;
use App\Models\Article;
use App\Models\Education;
use App\Http\Requests\EducationRequest;
use App\Models\UserArticleProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use App\Models\Category;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EducationController extends Controller
{

    public function index(Request $request, $page = null  )
    {
        $key = $request->input('key', null);
        $articlesPerPage = $request->input('articlesPerPage', 25);
        $currentPage = $page ?? $request->query('page', 1);
        $perPage = $articlesPerPage;

        // Fetch & sort at the database level
        $educations = Education::with(['primaryCategory:id,title' , 'progressPercentage'])
            ->when(!empty($key), function ($query) use ($key) {
                return $query
                            ->where('title', 'LIKE', "{$key}%")
                            ->orWhere('title', 'LIKE', "%{$key}%")
                            ->orWhere('summary', 'LIKE', "{$key}%")
                            ->orWhere('summary', 'LIKE', "%{$key}%");
            })
            ->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END, title ASC")
            ->paginate($perPage, ['*'], 'page', $currentPage);

            $averageProgress = $educations->map(function ($education) {
                return $education->progressPercentage->progress ?? 0;
            })->avg();

            $canonicalUrl = url("/education/page/{$educations->currentPage()}");

        return response()->json([
            'success' => true,
            'message' => 'Education data retrieved successfully',
            'data' => EducationResource::collection($educations), // Use Resource Collection
            'averageProgress' => $averageProgress,
            'meta' => [
                'current_page' => $educations->currentPage(),
                'next_page' => $educations->nextPageUrl(),
                'prev_page' => $educations->previousPageUrl(),
                'total' => $educations->lastPage(),
                'total_records' => $educations->total(),
                'per_page' => $educations->perPage(),
                'canonical_url' => $canonicalUrl,
            ]
        ]);
    }



    /**
     * Show the listing for admin dashboard.
     */
    public function educationArticlesList()
    {
        $educationArticles = Education::with(['primaryCategory:id,title'])
            ->orderBy('created_at','desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Education Articles List',
            'data' => EducationResource::collection($educationArticles), // Apply transformation
            'pagination' => [
                'total' => $educationArticles->lastPage(),
                'count' => $educationArticles->count(),
                'per_page' => $educationArticles->perPage(),
                'current_page' => $educationArticles->currentPage(),
                'next_page_url' => $educationArticles->nextPageUrl(),
                'prev_page_url' => $educationArticles->previousPageUrl(),
            ],
        ]);
    }



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::select('id', 'title','slug','database_field')->get();

        return response()->json([
            'success' => true,
            'message' => 'Categories data retrieved successfully',
            'data' => CategoryResource::collection($categories),
        ]);
    }




    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

            $data = $request->only([
                'title',
                'content',
                'summary',
                'primary_category_id',
            ]);

            $data['is_featured'] = $request->has('is_featured') ? 1 : 0;

            $educationRecord = Education::create($data);



        $educationRecord->refresh();
        return response()->json([
            'success' => true,
            'message' => 'Education article added successfully.',
            'imported_records' => new EducationResource($educationRecord),
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Education $education)
    {

        $education->load('primaryCategory');

        $nextEducation = Education::with(['primaryCategory' , 'progressPercentage:progress'])
            ->where('title', '>', $education->title)
            ->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END, title ASC")
            ->first();

        $progress = UserArticleProgress::where('article_id' , $education->id)->first();

        return response()->json([
            'success' => true,
            'message' => 'education data',
            'data' => new EducationResource($education),
            'progress' => $progress->progress ?? 0,
            'next_blog' => $nextEducation ? new EducationResource($nextEducation) : null,
        ]);
    }

    /**
     * Calculate the progress of article readibility
     */
    public function updateProgress(Request $request, Education $article )
    {
        $request->validate([
            'progress' => 'required|integer|min:0|max:100',
        ]);

        $user = Auth::user();

        // Update or insert progress
        $user->educationProgress()->syncWithoutDetaching([
            $article->id => ['progress' => $request->progress]
        ]);

        return response()->json([
            'message' => 'Progress updated successfully',
            'progress' => $request->progress
        ]);
    }

    public function edit(Education $education)
    {
        $categories = Category::all();

        return response()->json([
            'success' => true,
            'message' => 'Education data',
            'data' => [
                'categories' => $categories,
                'education' => $education,
            ],
        ]);
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Education $education)
    {
        // Debugging: Log request data to see if secondary_categories is received
        \Log::info('Request Data:', $request->all());

        // Extract primary fields
        $data = $request->only([
            'title',
            'content',
            'summary',
            'primary_category_id',
        ]);

        // Check if "is_featured" is set
        $data['is_featured'] = $request->has('is_featured') ? 1 : 0;

        // Update the education model
        $education->fill($data)->save();

        // Refresh and return updated data
        $education->refresh();

        return response()->json([
            'success' => true,
            'message' => 'Education data updated successfully',
            'data' => new EducationResource($education),
        ]);
    }




    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Education $education)
    {
        $education->delete();

        return response()->json([
            'success' => true,
            'message' => 'Education data deleted successfully',
            'data' => '',
        ]);
    }


    /**
     * Filters education records based on request parameters.
     *
     * @param \Illuminate\Http\Request $request The request containing filter parameters.
     * @return \Illuminate\Http\JsonResponse The filtered education records.
     */

     public function filter(Request $request)
    {
        $filterType = $request->input('filter');
        $filterValue = $request->input('value');

        if ($filterType == 'tags' && !empty($filterValue)) {
            $educations = Education::with(['primaryCategory:id,title'])
                ->where('tags', $filterValue)
                ->orderBy('title', 'asc')
                ->paginate(25);

            $educations->getCollection()->transform(function ($education) {
                $education->summary = mb_strimwidth($education->summary, 0, 250, '...');
                return $education;
            });

            return response()->json([
                'success' => true,
                'message' => 'Education data retrieved successfully',
                'data' => $educations,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid filter type or missing value.',
            'data' => [],
        ], 400);
    }



}

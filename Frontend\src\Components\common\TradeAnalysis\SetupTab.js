import { Col, Row, Dropdown } from "react-bootstrap";
import React, { useState, useRef, useEffect } from "react";
import {
  TripleDotsMenu,
  BlackDownIcon,
  OpenNewtabIcon,
  DeleteDarkIcon,
  RenameIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonButton from "@/Components/UI/CommonButton";

export default function FilterTradeDetails() {
  const [isSavedReport, setIsSavedReport] = useState(false);
  const [openIndex, setOpenIndex] = useState(null);
  const containerRefs = useRef({}); // ✅ Multi-ref tracking

  const [sellerResponse, setSellerResponse] = useState("");
  const [externalLink, setExternalLink] = useState("");

  const MAX_Response_LENGTH = 70;
  const MAX_External_LENGTH = 220;

  const responseRefDesktop = useRef(null);
  const externalRefDesktop = useRef(null);
  const responseRefMobile = useRef(null);
  const externalRefMobile = useRef(null);

  const dimensionData = [
    { id: 1, name: "Max Risk Percentage" },
    { id: 2, name: "PROFIT ALLOCATION TO CAPITAL" },
    { id: 3, name: "ACCOUNT STOP Risk VALUE" },
    { id: 4, name: "STOCK UNIT OF MEASUREMENT" },
  ];

  const addStrategies = () => {
    setIsSavedReport((prev) => !prev);
  };

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    autoResize(responseRefDesktop);
    autoResize(responseRefMobile);
  }, [sellerResponse]);

  useEffect(() => {
    autoResize(externalRefDesktop);
    autoResize(externalRefMobile);
  }, [externalLink]);

  useEffect(() => {
    const handleResize = () => {
      autoResize(responseRefDesktop);
      autoResize(responseRefMobile);
      autoResize(externalRefDesktop);
      autoResize(externalRefMobile);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // ✅ Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      const isClickInsideAny = Object.values(containerRefs.current).some(
        (ref) => ref && ref.contains(event.target)
      );
      if (!isClickInsideAny) {
        setOpenIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = (index) => {
    setOpenIndex((prev) => (prev === index ? null : index));
  };

  return (
    <div className="trade_analysis_card">
      <Row>
        <Col lg={4} xs={12} className="order-lg-1 order-2">
          <div className="trade_analysis_setup">
            <div
              className={`innerCard relative ${isSavedReport ? "rounded-0" : ""}`}
            >
              <div
                className={`save_reports ${isSavedReport ? "rotate-icon" : ""}`}
                onClick={addStrategies}
              >
                <label>Saved Reports</label>
              </div>
              {isSavedReport && (
                <div className="condition-dropdown saved_popup">
                  <div className="right_side">
                    <div className="scope_dimension_show">
                      <div className="d-flex gap-2">
                        <p className="select_category">Name</p>
                        <BlackDownIcon />
                      </div>
                      <div className="d-flex gap-2">
                        <BlackDownIcon />
                        <p className="select_category">Last Modified</p>
                      </div>
                    </div>

                    {dimensionData.map((item, index) => (
                      <div
                        className="scope_dimension_show position-relative"
                        key={index}
                      >
                        <p className="name">{item.name}</p>
                        <div
                          className="d-flex gap-2 align-items-center"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleDropdown(index);
                          }}
                          style={{ cursor: "pointer" }}
                          ref={(el) => (containerRefs.current[index] = el)}
                        >
                          <p className="report_time">9:30 AM</p>
                          <div className="TripleDotBtn">
                            <TripleDotsMenu />
                          </div>
                        </div>
                        {openIndex === index && (
                          <Dropdown.Menu
                            show
                            style={{
                              position: "absolute",
                              bottom: "75%",
                              right: 8,
                              zIndex: 1000,
                            }}
                          >
                            <Dropdown.Item eventKey="2">
                              <div className="dropdownlist">
                                <OpenNewtabIcon />
                                <span>Open in new tab</span>
                              </div>
                            </Dropdown.Item>
                            <Dropdown.Item eventKey="3">
                              <div className="dropdownlist">
                                <RenameIcon />
                                <span>Rename</span>
                              </div>
                            </Dropdown.Item>
                            <Dropdown.Item eventKey="4">
                              <div className="dropdownlist">
                                <DeleteDarkIcon />
                                <span>Delete</span>
                              </div>
                            </Dropdown.Item>
                          </Dropdown.Menu>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="innerCard">
            <div className="trade_analysis_setup">
              <div className="report_name">
                <label>Report Name</label>
                <div className="customInput_inner">
                  <textarea
                    ref={responseRefDesktop}
                    rows="1"
                    maxLength={MAX_Response_LENGTH}
                    className="table_form_textarea w-full resize-none overflow-hidden"
                    placeholder="Untitled 1"
                    value={sellerResponse}
                    onChange={(e) => setSellerResponse(e.target.value)}
                  />
                  <p className="character-count">
                    Characters: {sellerResponse.length}/{MAX_Response_LENGTH}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="innerCard">
            <div className="trade_analysis_setup">
              <div className="report_name">
                <label>Report Description</label>
                <div className="customInput_inner">
                  <textarea
                    ref={externalRefDesktop}
                    rows="2"
                    maxLength={MAX_External_LENGTH}
                    className="table_form_textarea w-full resize-none overflow-hidden"
                    value={externalLink}
                    onChange={(e) => setExternalLink(e.target.value)}
                    placeholder="e.g. This report is for profit deviations on stocks between $3-$5"
                  />
                  <p className="character-count">
                    Characters: {externalLink.length}/{MAX_External_LENGTH}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Col>

        <Col lg={8} xs={12} className="order-lg-2 order-1">
          <div className="trade_analysis_setup_btns">
            <CommonButton
              type="button"
              title="Duplicate Report"
              className="white-btn"
            />
            <CommonButton
              type="button"
              title="Delete Report"
              className="red-btn"
            />
          </div>
        </Col>
      </Row>
    </div>
  );
}

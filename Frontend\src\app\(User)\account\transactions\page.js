'use client';
import { Col, Nav, Row, Tab } from "react-bootstrap";
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonTable from "@/Components/UI/CommonTable";
import CustomPagination from "@/Components/UI/CustomPagination";
import React from "react";

export default function Transactions() {
    const fields = ["Date", "Purchase", "Total", "Status", "Order Id"];
    const paymentdata = [
        {
            date: "Apr 30, 2024",
            name: "TradeReply Essential Subscription - 1 month",
            price: "14.99",
            id: "US991234784",
        },
        {
            date: "Apr 30, 2024",
            name: "TradeReply Essential Subscription - 1 month",
            price: "14.99",
            id: "US991234784",
        },
        {
            date: "Apr 30, 2024",
            name: "TradeReply Essential Subscription - 1 month",
            price: "14.99",
            id: "US991234784",
        },
    ];

    const metaArray = {
        noindex: true,
        title: "Account Transactions | Payment History | TradeReply",
        description: "Review your transaction history on TradeReply.com. Access detailed records of payments, subscriptions, and other account activities.",
        canonical_link: "https://www.tradereply.com/account/transactions",
        og_site_name: "TradeReply",
        og_title: "Transaction History | Payments & Orders | TradeReply",
        og_description: "Review your transaction history on TradeReply. Access details of your past payments, orders, and subscription renewals.",
        twitter_title: "Transaction History | Payments & Orders | TradeReply",
        twitter_description: "Review your transaction history on TradeReply. Access details of your past payments, orders, and subscription renewals.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray}/>
                <div className="account_transaction_history">
                    <SidebarHeading title="Transaction History" />
                    <Row>
                        <Col lg={12} xs={12} className="mb-4 mb-lg-4">
                            <Tab.Container id="left-tabs-example" defaultActiveKey="purchases">
                                <Nav variant="pills" className="borderTabs">
                                    <Nav.Item>
                                        <Nav.Link eventKey="purchases">Purchases</Nav.Link>
                                    </Nav.Item>
                                    <Nav.Item>
                                        <Nav.Link eventKey="refer">Refer a Friend claims</Nav.Link>
                                    </Nav.Item>
                                </Nav>
                                <Tab.Content className="mt-8">
                                    <Tab.Pane eventKey="purchases">
                                        <div className="common_blackcard">
                                            <div className="account_card">
                                                <div className="account_card_table">
                                                    <CommonTable fields={fields} className="simple_table">
                                                        {paymentdata?.map((item, index) => (
                                                            <tr key={index}>
                                                                <td>{item.date}</td>
                                                                <td>{item.name}</td>
                                                                <td>${item.price}</td>
                                                                <td>Complete</td>
                                                                <td className="blue_text">{item.id}</td>
                                                            </tr>
                                                        ))}
                                                    </CommonTable>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="d-flex justify-content-center mt-3">
                                            <CustomPagination />
                                        </div>
                                    </Tab.Pane>
                                    <Tab.Pane eventKey="refer">
                                        You have not referred any friends
                                    </Tab.Pane>
                                </Tab.Content>
                            </Tab.Container>
                        </Col>
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}

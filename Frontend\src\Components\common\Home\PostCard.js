"use client";

import Link from "next/link";
import DOMPurify from "dompurify";
import { useEffect, useState } from "react";

const PostCard = ({ img, title, text, className, redirectHref }) => {
  const [sanitizedHtml, setSanitizedHtml] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      setSanitizedHtml(DOMPurify.sanitize(text));
    }
  }, [text]);

  return (
    <Link href={redirectHref} className={`blog_postcard ${className}`}>
      <div>
        <div className="blog_postcard_img">
          <div className="blog_postcard_img_overlay">
            <img
              src={img ?? "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png"}
              alt={title?.trim() ? `Featured blog post: ${title}` : "TradeReply blog article thumbnail"}
            />
          </div>
        </div>
        <div className="blog_postcard_content">
          <h3>{title}</h3>
          {sanitizedHtml && (
            <p dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />
          )}
        </div>
      </div>
    </Link>
  );
};

export default PostCard;

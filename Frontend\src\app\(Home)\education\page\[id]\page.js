// 'use client';

// import Education from "@/app/(Home)/education/page"
// import { useParams } from "next/navigation";

// const EducationPage = () => {
    
//     const params = useParams();
//     const page = params.id;
//     return(
//         <>
//         <Education pages={page}/>
//         </>
//     )
// }

// export default EducationPage;

// app/education/page/[id]/TwoFactorComponent.js
import EducationPage from "../../page"; // ✅ this is allowed if done right

export default function EducationDynamicPageWrapper({ params, searchParams }) {
  return <EducationPage params={params} searchParams={searchParams} />;
}

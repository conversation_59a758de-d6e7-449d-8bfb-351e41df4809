"use client";
import React, { useState, useEffect, useRef } from "react";
import { Row, Col } from "react-bootstrap";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import {
  SolidInfoIcon,
  LightEyeIcon,
  LicenseIcon,
  DigitaLAssetIcon,
  CoinWhiteIcon,
  ProductFormatWhiteIcon,
  GuageWhiteIcon,
  TradingPlatformWhiteIcon,
  ShuffleWhiteIcon,
  ClockWhiteIcon
} from "@/assets/svgIcons/SvgIcon";
import Select from "react-select";
import Form from "react-bootstrap/Form";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import ImageUploader from "./Partials/ImageUploader";
import ListingCategories from "./Partials/ListingCategories";

const MAX_TAGS = 10;
const MIN_TAG_LENGTH = 2;
const MAX_TAG_LENGTH = 35;
export default function CreateListing() {
  const [fileName, setFileName] = useState("File Upload");
  const [stockLimited, setStockLimited] = useState(false);
  const [digitalFile, setDigitalFile] = useState(true);
  const [downloadLink, setDownloadLink] = useState(false);
  const [tags, setTags] = useState([]);
  const [tagInput, setTagInput] = useState("");
  const [titleData, setTitleData] = useState("");
  const [descData, setDescData] = useState("");
  const [externalDownloadData, setExternalDownloadData] = useState("");
  const [licenseData, setLicenseData] = useState("");
  const [selectedAssetTypeOptions, setSelectedAssetTypeOptions] = useState([]);
  const [selectedFormatOptions, setSelectedFormatOptions] = useState([]);
  const [selectedSkillOptions, setSelectedSkillOptions] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState([]);
  const [selectedStyle, setSelectedStyle] = useState([]);
  const [selectedDuration, setSelectedDuration] = useState([]);
  const [tagError, setTagError] = useState("");

  const handleSelectChange = (selected, setSelected, max) => {
    if (selected.length <= max) {
      setSelected(selected);
    }
  };

  const assetTypeOptions = [
    { value: "stocks", label: "Stocks" },
    { value: "forex", label: "Forex (Foreign Exchange)" },
    { value: "crypto", label: "Cryptocurrency" },
    { value: "options", label: "Options" },
    { value: "futures", label: "Futures" },
    { value: "indices", label: "Indices" },
    { value: "commodities", label: "Commodities" },
    { value: "bonds", label: "Bonds & Fixed Income" },
    {
      value: "general_trading", label: "General Trading (Applies to Multiple Markets)",
    },
  ];
  const formatOptions = [
    "Video Course",
    "Ebook / PDF Guide",
    "Live Webinar",
    "Recorded Webinar",
    "Trading Indicator (TradingView, MT4, MT5, etc.)",
    "Trading Bot / Automation Script",
    "Spreadsheet / Calculator",
    "Market Report / Research Document",
    "Private Mentorship / Coaching Session",
    "Community / Membership Access"
  ].map(label => ({
    label,
    value: label
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }));

  const skillLevelOptions = [
    "Beginner-Friendly",
    "Intermediate",
    "Advanced",
    "All Levels"
  ].map(label => ({
    label,
    value: label
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }));

  const platformOptions = [
    "TradingView",
    "MetaTrader 4 (MT4)",
    "MetaTrader 5 (MT5)",
    "cTrader",
    "ThinkorSwim",
    "NinjaTrader",
    "Python / Algorithmic Trading",
    "Excel / Google Sheets",
    "General (Not Platform-Specific)"
  ].map(label => ({
    label,
    value: label
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }));

  const tradingStyleOptions = [
    "Day Trading",
    "Swing Trading",
    "Scalping",
    "Trend Trading",
    "Algorithmic & Bot Trading",
    "Options Selling / Income Strategies",
    "Crypto Arbitrage & DeFi Trading",
    "Long-Term Investing",
    "Market Psychology & Risk Management"
  ].map(label => ({
    label,
    value: label
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '_')
      .replace(/^_+|_+$/g, '')
  }));
  const timeCommitmentsOptions = [
    { value: "under-1-hour", label: "Under 1 Hour" },
    { value: "1-3-hours", label: "1 - 3 Hours" },
    { value: "3-10-hours", label: "3 - 10 Hours" },
    { value: "10-plus-hours-in-depth-masterclasses", label: "10+ Hours (In-Depth Masterclasses)" }
  ];

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  const getTruncatedFileName = (name, maxLength = 25) => {
    return name.length > maxLength
      ? name.slice(0, maxLength - 3) + "..."
      : name;
  };

  const getFile = (file) => {
    console.log(file);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
  };
  const indicatorSeparatorStyle = {
    alignSelf: "stretch",
    backgroundColor: "gray",
    marginBottom: 8,
    marginTop: 8,
    width: 1,
  };
  const IndicatorSeparator = ({ innerProps }) => {
    return <span style={indicatorSeparatorStyle} {...innerProps} />;
  };
  // const containsProfanity = (text) => {
  //   const lower = text.toLowerCase();
  //   return enBadWords.some(word => lower.includes(word));
  // };
  const handleTagKeyDown = (e) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      const newTag = tagInput.trim();

      if (!newTag) return;

      // if (containsProfanity(newTag)) {
      //   setTagError("The tag contains inappropriate or offensive language.");
      //   return;
      // }

      if (tags.length >= MAX_TAGS) {
        setTagError("You can only add up to 10 tags.");
        return;
      }
      if (newTag.length < MIN_TAG_LENGTH) {
        setTagError("Tags must be at least 2 characters.");
        return;
      }
      if (newTag.length > MAX_TAG_LENGTH) {
        setTagError("Tags can’t exceed 35 characters. Try something shorter.");
        return;
      }
      if (tags.includes(newTag)) {
        setTagError("This tag is already added.");
        return;
      }

      setTags([...tags, newTag]);
      setTagInput("");
      setTagError("");
    }
  };


  const removeTag = (index) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const MAX_Title_LENGTH = 100;
  const MAX_Descrip_LENGTH = 2000;
  const MAX_ExternalDownload_LENGTH = 1000;
  const MAX_License_LENGTH = 500;

  const descRef = useRef(null);
  const licenseRef = useRef(null);

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(descRef);
  }, [descData]);
  useEffect(() => {
    autoResize(licenseRef);
  }, [licenseData]);
  useEffect(() => {
    const handleResize = () => {
      autoResize(descRef);
      autoResize(licenseRef);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const metaArray = {
    noindex: true,
    title: "Marketplace Listings | Create New Listing | TradeReply",
    description:
      "Create a new marketplace listing on TradeReply. Upload digital products, set pricing, and reach potential traders globally.",
    canonical_link: "https://www.tradereply.com/account/create-listing",
    og_site_name: "TradeReply",
    og_title: "Marketplace Listings | Create New Listing | TradeReply",
    og_description:
      "Create a new marketplace listing on TradeReply. Upload digital products, set pricing, and reach potential traders globally.",
    og_url: "https://www.tradereply.com/account/create-listing",
    og_type: "website",
    og_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    og_image_width: "1200",
    og_image_height: "630",
    og_locale: "en_US",
    twitter_card: "summary_large_image",
    twitter_title: "Marketplace Listings | Create New Listing | TradeReply",
    twitter_description:
      "Create a new marketplace listing on TradeReply. Upload digital products, set pricing, and reach potential traders globally.",
    twitter_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    twitter_site: "@JoinTradeReply",
    robots: "noindex, nofollow",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="create_listing">
          <div className="account_card_create_listing">
            <div className="container form-wrapper">
              <SidebarHeading title="Create New Listing" />
              <form
                onSubmit={(e) => {
                  handleSubmit(e);
                }}
              >
                <Row>
                  <Col lg={6} xs={12}>
                    <ImageUploader getFile={getFile} />
                  </Col>
                  <Col lg={6} xs={12} className="listing-rightside">
                    <div>
                      <input
                        type="text"
                        required
                        placeholder="Title"
                        className="form-input mb-1"
                        maxLength={MAX_Title_LENGTH}
                        value={titleData}
                        onChange={(e) => setTitleData(e.target.value)}
                      />
                      <div className="outer-character-count">
                        Characters left: {titleData.length}/{MAX_Title_LENGTH}
                      </div>
                    </div>
                    <input
                      required
                      type="number"
                      placeholder="Price"
                      className="form-input"
                    />
                    <div className="checkbox-wrapper">
                      <input
                        className="custom_checkbox_input form-check-input"
                        type="checkbox"
                        id="stock_level_applies"
                        style={{ pointerEvents: "auto" }}
                        checked={stockLimited}
                        onChange={(e) => setStockLimited(e.target.checked)}
                      />
                      <label
                        className="name custom_checkbox_label ps-1"
                        htmlFor="stock_level_applies"
                      >
                        Stock Level Applies
                      </label>
                      <CommonTooltip
                        className="d-flex align-items-center"
                        content="Enable this if your product has a limited quantity available for sale. Leave it off for unlimited digital assets.  "
                        position="top-left"
                      >
                        <SolidInfoIcon />
                      </CommonTooltip>
                    </div>
                    {stockLimited && (
                      <input
                        type="number"
                        placeholder="Enter available stock (total items on hand)"
                        className="form-input"
                      />
                    )}
                    <ListingCategories />


                    <div className="relative">
                      <textarea
                        className="form-textarea  w-full resize-none overflow-hidden mb-0"
                        rows="4"
                        placeholder="Description"
                        ref={descRef}
                        maxLength={MAX_Descrip_LENGTH}
                        value={descData}
                        onChange={(e) => setDescData(e.target.value)}
                      ></textarea>
                      <div className="outer-character-count mb-lg-0 mb-2">
                        Characters left: {descData.length}/{MAX_Descrip_LENGTH}
                      </div>
                    </div>
                  </Col>
                </Row>
                <div className="checkbox-wrapper">
                  <DigitaLAssetIcon />
                  <label>Digital Asset (Upload or Link)</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Choose how your digital product will be delivered to buyers — either by uploading a file or providing a secure external download link."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="upload_digital_file"
                    style={{ pointerEvents: "auto" }}
                    checked={digitalFile}
                    onChange={(e) => setDigitalFile(e.target.checked)}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="upload_digital_file"
                  >
                    Upload Digital File
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Upload the actual file you want to deliver to buyers after purchase. Accepted formats include ZIP, PDF, XLSX, DOCX, and more."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                {digitalFile && (
                  <label className="file-upload-wrapper">
                    {getTruncatedFileName(fileName)}
                    <input
                      type="file"
                      name="file"
                      onChange={handleFileChange}
                    />
                  </label>
                )}
                <div className="checkbox-wrapper">
                  <input
                    className="custom_checkbox_input form-check-input"
                    type="checkbox"
                    id="external_download_link"
                    style={{ pointerEvents: "auto" }}
                    checked={downloadLink}
                    onChange={(e) => setDownloadLink(e.target.checked)}
                  />
                  <label
                    className="name custom_checkbox_label ps-1"
                    htmlFor="external_download_link"
                  >
                    External Download Link
                  </label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Paste a direct link to your hosted file (e.g., Google Drive, Dropbox). Make sure the link is accessible and does not require login."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                {downloadLink && (
                  <>
                    <input
                      type="text"
                      placeholder="Enter URL to digital content (e.g. private video or file link)"
                      className="form-input"
                      maxLength={MAX_ExternalDownload_LENGTH}
                      value={externalDownloadData}
                      onChange={(e) => setExternalDownloadData(e.target.value)}
                    />
                    <div className="outer-character-count">
                      Characters left: {externalDownloadData.length}/{MAX_ExternalDownload_LENGTH}
                    </div>
                  </>
                )}
                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <CoinWhiteIcon />
                    <Form.Label className="mb-0">
                      Asset Type ({selectedAssetTypeOptions.length}/3)
                    </Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={assetTypeOptions}
                    value={selectedAssetTypeOptions}
                    onChange={(selected) =>
                      handleSelectChange(selected, setSelectedAssetTypeOptions, 3)
                    }
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Asset Type"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <ProductFormatWhiteIcon />
                    <Form.Label className="mb-0">Format ({selectedFormatOptions.length}/3)</Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={formatOptions}
                    value={selectedFormatOptions}
                    onChange={(selected) =>
                      handleSelectChange(selected, setSelectedFormatOptions, 3)
                    }
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Format"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <GuageWhiteIcon />
                    <Form.Label className="mb-0">Skill Level ({selectedSkillOptions.length}/1)</Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={skillLevelOptions}
                    value={selectedSkillOptions}
                    onChange={(selected) =>
                      handleSelectChange(selected, setSelectedSkillOptions, 1)
                    }
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Skill Level"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>

                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <TradingPlatformWhiteIcon />
                    <Form.Label className="mb-0">Trading Platform ({selectedPlatform.length}/3)</Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={platformOptions}
                    value={selectedPlatform}
                    onChange={(selected) => handleSelectChange(selected, setSelectedPlatform, 3)}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Trading Platform"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <ShuffleWhiteIcon />
                    <Form.Label className="mb-0">Trading Style ({selectedStyle.length}/3)</Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={tradingStyleOptions}
                    value={selectedStyle}
                    onChange={(selected) => handleSelectChange(selected, setSelectedStyle, 3)}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Trading Style"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>
                <Form.Group controlId="tradingStyles">
                  <div className="checkbox-wrapper">
                    <ClockWhiteIcon />
                    <Form.Label className="mb-0">Time Commitment ({selectedDuration.length}/1)</Form.Label>
                  </div>
                  <Select
                    isMulti
                    name="colors"
                    options={timeCommitmentsOptions}
                    value={selectedDuration}
                    onChange={(selected) => handleSelectChange(selected, setSelectedDuration, 1)}
                    className="basic-multi-select form-multi-select mb-3"
                    placeholder="Time Commitment"
                    classNamePrefix="select"
                    isSearchable={false}
                    components={{ IndicatorSeparator }}
                  />
                </Form.Group>

                <div className="checkbox-wrapper d-flex align-items-center gap-2 mb-2">
                  <LightEyeIcon />
                  <label className="mb-0">Tags ({tags.length}/10)</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content='Add descriptive keywords related to your product (e.g., “scalping,” “crypto,” “MACD”). Tags help buyers discover your listing through search and filtering.'
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="tags-input d-flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="tag d-flex align-items-center gap-1 bg-gray-200 px-2 py-1 rounded"
                    >
                      {tag}
                      <svg
                        onClick={() => removeTag(index)}
                        xmlns="http://www.w3.org/2000/svg"
                        width="15"
                        height="15"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="cursor-pointer"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                        <path d="M18 6l-12 12" />
                        <path d="M6 6l12 12" />
                      </svg>
                    </span>
                  ))}
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => {
                      setTagInput(e.target.value);
                      setTagError("");
                    }}
                    onKeyDown={handleTagKeyDown}
                    placeholder="Add tags to help users find your product"
                    className="outline-none border-0 flex-grow-1"
                  />
                </div>
                {tagError && (
                  <p className="error-message mb-2" >
                    {tagError}
                  </p>
                )}
                <div className="checkbox-wrapper">
                  <LicenseIcon />
                  <label>License/Usage Terms</label>
                  <CommonTooltip
                    className="d-flex align-items-center"
                    content="Explain how buyers are allowed to use your product. For example: personal use only, no redistribution, or includes lifetime access."
                    position="top-right"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                </div>
                <div className="relative">
                  <textarea
                    className="form-textarea w-full resize-none overflow-hidden mb-0"
                    rows="4"
                    placeholder="Describe any usage or redistribution restrictions"
                    ref={licenseRef}
                    maxLength={MAX_License_LENGTH}
                    value={licenseData}
                    onChange={(e) => setLicenseData(e.target.value)}
                  ></textarea>
                  <div className="outer-character-count">
                    Characters left: {licenseData.length}/{MAX_License_LENGTH}
                  </div>
                </div>
                <CommonButton
                  title="Publish"
                  type="submit"
                  className="view_res_btn w-100"
                />
              </form>
            </div>
          </div>
        </div>
      </AccountLayout >
    </>
  );
}

/**
 * Mask name for display
 * Shows only first letter + asterisks: A**** M*****
 * Follows project requirements for name display
 */
export const maskName = (name) => {
    if (!name || name.length === 0) return '';
    if (name.length === 1) return name;
    return name[0] + '*'.repeat(name.length - 1);
};

/**
 * Mask full name (first and last name)
 * Shows only first letter of each part + asterisks: A**** M*****
 */
export const maskFullName = (fullName) => {
    if (!fullName) return '';
    
    const parts = fullName.split(' ');
    return parts
        .map((part) => {
            if (part.length <= 1) return part;
            return part[0] + '*'.repeat(part.length - 1);
        })
        .join(' ');
};

/**
 * Mask address for display
 * Shows only first character + asterisks: 2****************
 * Follows project requirements for address display
 */
export const maskAddress = (address) => {
    if (!address || address.length === 0) return '';
    if (address.length === 1) return address;
    return address[0] + '*'.repeat(address.length - 1);
};

/**
 * Format address for display
 * Combines city, state, and zip code
 */
export const formatCityStateZip = (city, state, zipCode) => {
    const parts = [city, state, zipCode].filter(Boolean);
    return parts.join(', ');
};

/**
 * Get display name for address
 * Returns masked full name or "Add Name" if empty
 */
export const getDisplayName = (firstName, lastName) => {
    if (!firstName && !lastName) return '+ Add Name';
    
    const fullName = [firstName, lastName].filter(Boolean).join(' ');
    return maskFullName(fullName);
};

/**
 * Get display address
 * Returns masked address or "Add Address" if empty
 */
export const getDisplayAddress = (address) => {
    if (!address) return '+ Add Address';
    return maskAddress(address);
};

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Article;

class UpdateFeaturedResource extends Command
{
    protected $signature = 'feature:resource'; // Command name
    protected $description = 'Updates the weekly featured resource from the Education Center';

    public function handle()
    {
        $updatedRows = Article::where('type', 'education')->where('is_featured', true)->update(['is_featured' => false]);

        $featured = Article::where('type', 'education')->inRandomOrder()->first();

        \Log::INFO('HELLO', [$featured]);
        if ($featured) {
            $featured->update(['is_featured' => true]);
            $this->info("Featured article updated to: " . $featured->title);
        } else {
            $this->warn("⚠No education articles found in the database.");
        }

        $this->info("Reset previous featured articles: $updatedRows affected rows.");
    }

}

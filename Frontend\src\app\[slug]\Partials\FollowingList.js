"use client";
import React, { useState } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { RightArrowIconSvg, ProfileUserDarkIcon } from "@/assets/svgIcons/SvgIcon";
import FollowingListPopup from "./FollowingListPopUp";

export default function FollowingList({ following = [], user }) {
  const [isFollowingListModal, setFollowingListModal] = useState(false);

  const showFollowingListModal = () => {
    setFollowingListModal(true);
  }
  const hideFollowingListModal = () => {
    setFollowingListModal(false);
  }
  const linkProps =
  {
    Linktext: "View all",
    Linkicon: <RightArrowIconSvg />,
    onClick: showFollowingListModal,
  }

  return (
    <>
      <CommonWhiteCard
        title={`Following (${following.length})`}
        {...linkProps}
        className="account_card"
      >
        <div className="account_card_following">
          {following.slice(0, 3).map((user, index) => (
            <div className="main_inform" key={index}>
              <div className="profile_photo">
                <ProfileUserDarkIcon />
              </div>
              <a
                href={`/@${user.username || user.name}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: "#00adef",
                }}
              >
                <h6>{user.name}</h6>
              </a>
            </div>
          ))}
        </div>
      </CommonWhiteCard>
      {isFollowingListModal && (
        <FollowingListPopup
          title={`${user.fullName} Following`}
          following={following}
          closeModal={hideFollowingListModal}
        />
      )}
    </>
  );
}

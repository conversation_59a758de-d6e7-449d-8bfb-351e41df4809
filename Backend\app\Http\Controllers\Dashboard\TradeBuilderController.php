<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\PortfolioFieldDefinition;
use App\Models\TradeFieldDefinition;
use App\Models\TransactionFieldDefinition;
use Illuminate\Http\Request;
use App\Services\TradeBuilderService;
use App\Services\FormulaEvaluatorService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Trade;
use App\Models\TradeAccount;
use App\Models\TradeForm;
use App\Models\TradeFormSection;
use App\Models\TradeSnapshot;
use App\Services\TradeFormSectionService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;

class TradeBuilderController extends Controller
{
    public function __construct(protected TradeBuilderService $tradeBuilderService, protected FormulaEvaluatorService $evaluator)
    {
    }

    /**
     * fetch a trade default entry and exit form fields.
     */
    public function index(): JsonResponse
    {
        return response()->json(
            $this->tradeBuilderService->getEntryAndExitFields()
        );
    }
    public function initialize(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|in:entry,exit',
            'index' => 'required|integer|min:1',
            'trade_id' => 'nullable|integer',
            'trade_account_id' => 'required|integer|exists:trade_accounts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $selectedTradeAccount = TradeAccount::where('id', $request->trade_account_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$selectedTradeAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Trade account not found or unauthorized',
            ], 404);
        }

        $trade = null;
        if ($request->has('trade_id')) {
            $trade = Trade::where('trade_account_id', $selectedTradeAccount->id)
                ->where('account_trade_id', $request->trade_id)
                ->where('user_id', $user->id)
                ->first();
        }

        if (!$trade) {
            $nextId = Trade::max('id') ?? 0;
            $trade = Trade::create([
                'user_id' => $user->id,
                'trade_account_id' => $selectedTradeAccount->id,
                'is_published' => false,
                'draft_title' => 'Draft ' . (Trade::where('trade_account_id', $selectedTradeAccount->id)
                    ->where('is_published', false)->count() + 1),
            ]);

            $trade->update([
                'account_trade_id' => $trade->id,
            ]);
        }

        $maxIndex = TradeForm::where('trade_id', $trade->id)
            ->where('type', $request->type)
            ->max('index') ?? 0;

        $form = TradeForm::create([
            'trade_id' => $trade->id,
            'type' => $request->type,
            'index' => $maxIndex + 1,
            'is_published' => false,
        ]);

        return response()->json([
            'success' => true,
            'trade_id' => $trade->account_trade_id,
            'trade_account_id' => $trade->trade_account_id,
            'form_key' => $form->id,
            'index' => $form->index,
            'is_published' => $form->is_published,
            'limitPerSubscription' => $user?->activeSubscription?->plan?->rules->where('key', 'trade_builder_fields')->first()->value ?? 0,
            'plan_name' => $user?->activeSubscription?->plan?->title
        ], 200);
    }
    public function getDraftTrades(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'trade_account_id' => 'required|integer|exists:trade_accounts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $selectedTradeAccount = TradeAccount::where('id', $request->trade_account_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$selectedTradeAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Trade account not found or unauthorized',
            ], 404);
        }

        $trades = Trade::where('user_id', $user->id)
            ->where('trade_account_id', $selectedTradeAccount->id)
            ->whereNull('deleted_at')
            ->get()
            ->map(function ($trade) {
                return [
                    'id' => $trade->account_trade_id,
                    'trade_account_id' => $trade->trade_account_id,
                    'draft_title' => $trade->draft_title,
                    'created_at' => $trade->created_at->toIso8601String(),
                    'updated_at' => $trade->updated_at->toIso8601String(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $trades,
        ], 200);
    }
    public function getTradeData(Request $request, $accountTradeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'trade_account_id' => 'required|integer|exists:trade_accounts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $selectedTradeAccount = TradeAccount::where('id', $request->trade_account_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$selectedTradeAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Trade account not found or unauthorized',
            ], 404);
        }

        $trade = Trade::where('trade_account_id', $selectedTradeAccount->id)
            ->where('account_trade_id', $accountTradeId)
            ->where('user_id', $user->id)
            ->whereNull('deleted_at')
            ->first();

        if (!$trade) {
            return response()->json([
                'success' => false,
                'message' => 'Trade not found, unauthorized, or deleted',
            ], 404);
        }

        $forms = TradeForm::where('trade_id', $trade->id)
            ->with(['sections' => function ($query) {
                $query->select('trade_form_id', 'section', 'data', 'computed_data');
            }])
            ->get();

        return response()->json([
            'success' => true,
            'limitPerSubscription' => $user?->activeSubscription?->plan?->rules->where('key', 'trade_builder_fields')->first()->value ?? 0,
            'plan_name' => $user?->activeSubscription?->plan?->title,
            'trade' => [
                'trade_id' => $trade->account_trade_id,
                'trade_account_id' => $trade->trade_account_id,
                'draft_title' => $trade->draft_title,
                'is_published' => $trade->is_published
            ],
            'forms' => $forms->map(function ($form) {
                return [
                    'form_key' => $form->id,
                    'type' => $form->type,
                    'index' => $form->index,
                    'is_published' => $form->is_published,
                    'sections' => $form->sections->map(function ($section) {
                        return [
                            'section' => $section->section,
                            'data' => $section->data,
                            'computed_data' => $section->computed_data
                        ];
                    }),
                ];
            }),
        ], 200);
    }
    public function saveSection(Request $request, TradeFormSectionService $service)
    {
        $validator = $service->validateRequest($request);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = $service->findForm($request->formKey, $user->id, $request->trade_account_id);

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        $section = $service->saveSectionData($form, $request);
        $service->propagateData($form, $section, $request);

        if ($form->type === 'exit') {
            $this->updateLinkedEntryLastExitPrice($form);
        }

        $computed = $this->computeFormulas(
            $form,
            $request->input('inputs', []),
            $request->input('locked', []),
            $request->input('formulaModeInputs', []),
            [$request->input('changedFields', '')],
            true
        );
        $section->computed_data = $computed;
        $section->save();

        if ($form->is_published) {
            $this->createSnapshot($form);
        }

        return response()->json([
            'success' => true,
            'message' => 'Section data saved successfully',
            'data' => $section,
        ], 200);
    }
    public function removeExtraField(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'section' => 'required|string|in:overview,projection,outcome',
            'input' => 'required|string',
            'trade_account_id' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();

        $form = TradeForm::where('id', $request->formKey)
            ->whereHas(
                'trade',
                fn ($query) =>
                $query->where('user_id', $user->id)
                      ->where('trade_account_id', $request->trade_account_id)
                      ->whereNull('deleted_at')
            )
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        $section = TradeFormSection::where('trade_form_id', $form->id)
            ->where('section', $request->section)
            ->first();

        if (!$section) {
            return response()->json([
                'success' => false,
                'message' => 'Section not found',
            ], 404);
        }

        $originalData = collect($section->data ?: []);
        $updatedData = $originalData->map(function ($item) use ($request) {
            if ($item['input'] === $request->input) {
                if (isset($item['is_resolved'])) {
                    $item['is_resolved'] = true;
                    return $item;   
                } elseif (!empty($item['is_extra'])) {
                    return null;
                }
            }
            return $item;
        })->filter()->values()->toArray(); 

        $section->update([
            'data' => $updatedData,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Field updated/removed successfully',
        ]);
    }
    public function publishForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'data' => 'required|array',
            'data.overview' => 'required|array',
            'data.projection' => 'required|array',
            'data.outcome' => 'required|array',
            'data.notes' => 'nullable|string|max:500',
            'data.chartUrl' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
        ->whereHas(
            'trade',
            fn ($query) => $query
            ->where('user_id', $user->id)
            ->where('trade_account_id', $request->trade_account_id)
            ->whereNull('deleted_at')
        )
        ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        foreach (['overview', 'projection', 'outcome'] as $section) {
            TradeFormSection::updateOrCreate(
                [
                    'trade_form_id' => $form->id,
                    'section' => $section,
                ],
                ['data' => $request->data[$section]]
            );
        }

        $form->update([
            'is_published' => true,
            'notes' => $request->data['notes'],
            // 'chart_url' => $request->data['chartUrl'],
        ]);

        $this->createSnapshot($form);

        return response()->json([
            'success' => true,
            'message' => 'Form published successfully',
            'data' => $form,
        ], 200);
    }

    public function publishTrade(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tradeId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $trade = Trade::where('account_trade_id', $request->tradeId)
            ->where('trade_account_id', $request->trade_account_id)
            ->where('user_id', $user->id)
            ->whereNull('deleted_at')
            ->first();

        if (!$trade || $trade->is_published) {
            return response()->json([
                'success' => false,
                'message' => 'Trade not found, unauthorized, or already published',
            ], 404);
        }

        $forms = TradeForm::where('trade_id', $trade->id)
            ->where('is_published', true)
            ->get();

        if ($forms->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'At least one form must be published before publishing the trade',
            ], 422);
        }

        $trade->update(['is_published' => true]);

        $forms->each(function ($form) {
            $this->createSnapshot($form);
        });

        return response()->json([
            'success' => true,
            'message' => 'Trade published successfully',
            'data' => $trade,
        ], 200);
    }

    protected function createSnapshot(TradeForm $form)
    {
        $sections = $form->sections()->get()->pluck('data', 'section')->toArray();
        $snapshotData = [
            'overview' => $sections['overview'] ?? [],
            'projection' => $sections['projection'] ?? [],
            'outcome' => $sections['outcome'] ?? [],
            'notes' => $form->notes,
        ];

        TradeSnapshot::create([
            'trade_id' => $form->trade_id,
            'trade_form_id' => $form->id,
            'data' => $snapshotData,
        ]);
    }

    protected function updateLinkedEntryLastExitPrice(TradeForm $exitForm)
    {
        $overviewSection = $exitForm->sections->firstWhere('section', 'overview');
        if (!$overviewSection) {
            return;
        }

        $linkedEntryIndex = collect($overviewSection->data)->firstWhere('input', 'linked_entry')['value'] ?? null;
        if (!$linkedEntryIndex) {
            return;
        }

        $entryForm = TradeForm::where('trade_id', $exitForm->trade_id)
            ->where('type', 'entry')
            ->where('index', $linkedEntryIndex)
            ->first();

        if (!$entryForm) {
            return;
        }

        $linkedExits = TradeForm::where('trade_id', $exitForm->trade_id)
            ->where('type', 'exit')
            ->whereHas('sections', function ($query) use ($linkedEntryIndex) {
                $query->where('section', 'overview')
                      ->whereJsonContains('data', ['input' => 'linked_entry', 'value' => $linkedEntryIndex]);
            })
            ->orderBy('index', 'desc')
            ->get();

        $lastExitPrice = null;
        foreach ($linkedExits as $linkedExit) {
            $exitOverview = $linkedExit->sections->firstWhere('section', 'overview');
            if ($exitOverview) {
                $exitPrice = collect($exitOverview->data)->firstWhere('input', 'transaction_exit_price')['value'] ?? null;
                if (!is_null($exitPrice)) {
                    $lastExitPrice = $exitPrice;
                    break;
                }
            }
        }

        $entryOverview = $entryForm->sections->firstWhere('section', 'overview');
        if (!$entryOverview) {
            $entryOverview = TradeFormSection::create([
                'trade_form_id' => $entryForm->id,
                'section' => 'overview',
                'data' => [],
                'formula_data' => [],
            ]);
        }

        $updatedData = collect($entryOverview->data)->map(function ($item) use ($lastExitPrice) {
            if ($item['input'] === 'transaction_last_exit_price') {
                $item['value'] = $lastExitPrice;
                $item['isFormula'] = true;
            }
            return $item;
        })->toArray();

        $hasField = collect($updatedData)->contains('input', 'transaction_last_exit_price');
        if (!$hasField) {
            $updatedData[] = [
                'input' => 'transaction_last_exit_price',
                'value' => $lastExitPrice,
                'isFormula' => true,
                'is_extra' => false
            ];
        }

        $entryOverview->update(['data' => $updatedData]);
    }

    protected function setNullLinkedEntryLastExitPrice($entryForm)
    {
        $entryOverview = $entryForm->sections->firstWhere('section', 'overview');
        if (!$entryOverview) {
            $entryOverview = TradeFormSection::create([
                'trade_form_id' => $entryForm->id,
                'section' => 'overview',
                'data' => [],
                'formula_data' => [],
            ]);
        }

        $updatedData = collect($entryOverview->data)->map(function ($item) {
            if ($item['input'] === 'transaction_last_exit_price') {
                $item['value'] = null;
                $item['isFormula'] = true;
            }
            return $item;
        })->toArray();

        $hasField = collect($updatedData)->contains('input', 'transaction_last_exit_price');
        if (!$hasField) {
            $updatedData[] = [
                'input' => 'transaction_last_exit_price',
                'value' => null,
                'isFormula' => true,
                'is_extra' => false,
            ];
        }

        $entryOverview->update(['data' => $updatedData]);
    }

    public function getFormStatus($formKey)
    {
        $form = TradeForm::where('id', $formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', auth()->id()))
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Trade form not found or unauthorized',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'is_published' => $form->is_published,
        ], 200);
    }

    public function deleteForm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $form = TradeForm::where('id', $request->formKey)
            ->whereHas('trade', fn ($query) => $query->where('user_id', $user->id()))
            ->first();

        if (!$form) {
            return response()->json([
                'success' => false,
                'message' => 'Form not found, unauthorized, or already published',
            ], 404);
        }

        if ($form->type === 'entry') {
            $exitForms = TradeForm::with('sections')
                ->where('trade_id', $form->trade_id)
                ->where('type', 'exit')
                ->get();

            foreach ($exitForms as $exit) {
                $overview = $exit->sections->firstWhere('section', 'overview');
                if (! $overview) {
                    continue;
                }

                foreach ($overview->data as $field) {
                    if (
                        isset($field['input'], $field['value'])
                        && $field['input'] === 'linked_entry'
                        && (string)$field['value'] === (string)$form->index
                    ) {
                        $exit->delete();
                        break;
                    }
                }
            }
        }

        if ($form->type === 'exit') {
            $linkedEntryIndex = optional($form->sections->firstWhere('section', 'overview'))->data;
            $linkedEntryIndex = collect($linkedEntryIndex)->firstWhere('input', 'linked_entry')['value'] ?? null;

            $form->delete();

            if ($linkedEntryIndex !== null) {
                $latestExit = TradeForm::where('trade_id', $form->trade_id)
                    ->where('type', 'exit')
                    ->whereHas('sections', function ($query) use ($linkedEntryIndex) {
                        $query->where('section', 'overview')
                              ->whereJsonContains('data', ['input' => 'linked_entry', 'value' => $linkedEntryIndex]);
                    })
                    ->orderBy('index', 'desc')
                    ->first();

                if ($latestExit) {
                    $this->updateLinkedEntryLastExitPrice($latestExit);
                } else {
                    $entryForm = TradeForm::where('trade_id', $form->trade_id)
                        ->where('type', 'entry')
                        ->where('index', $linkedEntryIndex)
                        ->first();

                    if ($entryForm) {
                        $this->setNullLinkedEntryLastExitPrice($entryForm);
                    }
                }
            }
        } else {
            $form->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Form deleted successfully',
        ], 200);
    }
    public function deleteTrade(Request $request, $accountTradeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'trade_account_id' => 'required|integer|exists:trade_accounts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = Auth::user();
        $selectedTradeAccount = TradeAccount::where('id', $request->trade_account_id)
            ->where('user_id', $user->id)
            ->first();

        if (!$selectedTradeAccount) {
            return response()->json([
                'success' => false,
                'message' => 'Trade account not found or unauthorized',
            ], 404);
        }

        $trade = Trade::where('trade_account_id', $selectedTradeAccount->id)
            ->where('account_trade_id', $accountTradeId)
            ->where('user_id', $user->id)
            ->whereNull('deleted_at')
            ->first();

        if (!$trade) {
            return response()->json([
                'success' => false,
                'message' => 'Trade not found, unauthorized, or already deleted',
            ], 404);
        }

        foreach ($trade->forms as $form) {
            $form->sections()->delete();
            $form->delete();
        }

        $trade->delete();

        return response()->json([
            'success' => true,
            'message' => 'Trade and all associated forms deleted',
        ], 200);
    }
    public function uploadChart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'chart' => 'required|file|mimes:jpeg,png|max:5120',
            'formKey' => 'required|string',
            'tradeId' => 'required|string',
            'formType' => 'required|in:entry,exit',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ], 400);
        }

        $userId = auth()->id();

        // try {
        $file = $request->file('chart');
        $formKey = $request->input('formKey');
        $tradeId = $request->input('tradeId');
        $formType = $request->input('formType');

        $allowedMimes = ['image/jpeg', 'image/png'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return response()->json([
                'success' => false,
                'message' => 'Only JPG and PNG files are allowed.',
            ], 400);
        }

        $s3Path = "user-uploads/trade-builder/{$userId}/{$formType}s/{$tradeId}/chart.jpg";

        Storage::disk('s3')->put($s3Path, file_get_contents($file->getRealPath()), [
            'ContentType' => $file->getMimeType(),
            'visibility' => 'public',
        ]);

        $chartUrl = Storage::disk('s3')->url($s3Path);

        $updated = TradeForm::where('form_key', $formKey)
            ->where('trade_id', $tradeId)
            ->update(['chart_url' => $chartUrl]);

        if (!$updated) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update chart URL in database.',
            ], 500);
        }

        return response()->json([
            'success' => true,
            'chartUrl' => $chartUrl,
        ], 200);
        // } catch (\Exception $e) {
        //     Log::error('Chart upload error', [
        //         'userId' => $userId,
        //         'tradeId' => $tradeId,
        //         'error' => $e->getMessage(),
        //     ]);
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Server error during upload.',
        //     ], 500);
        // }
    }
    public function calculateFormulas(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'formKey' => 'required',
            'inputs' => 'array',
            'locked' => 'array',
            'formulaModeInputs' => 'array',
            'changedFields' => 'nullable', // Allow string or array
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $form = TradeForm::with('sections', 'trade')->find($request->formKey);

        if (!$form || $form->trade->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Form not found or unauthorized',
            ], 404);
        }

        $inputs = collect($request->input('inputs', []))->mapWithKeys(function ($v, $k) {
            return [strtolower($k) => $v];
        })->toArray();

        $locked = $request->input('locked', []);

        $formulaModeInputs = collect($request->input('formulaModeInputs', []))->mapWithKeys(function ($v, $k) {
            return [strtolower($k) => $v];
        })->toArray();

        $changedFieldsInput = $request->input('changedFields', []);
        $changedFields = is_array($changedFieldsInput) ? $changedFieldsInput : [$changedFieldsInput];
        $changedFields = array_filter($changedFields);
        $changedFields = array_map('strtolower', $changedFields);

        $updatedFields = $this->computeFormulas($form, $inputs, $locked, $formulaModeInputs, $changedFields, false);

        return response()->json([
            'success' => true,
            'calculated' => $updatedFields,
        ]);
    }

    private function computeFormulas(TradeForm $form, array $inputs, array $locked, array $formulaModeInputs, array $changedFields, bool $computeAll = false): array
    {
        $form_type = strtoupper($form->type);
        $trade = $form->trade;
        $all_forms = TradeForm::with('sections')->where('trade_id', $trade->id)->get();
        $portfolioValues = PortfolioFieldDefinition::with(['userFieldValue' => function ($q) use ($trade) {
            $q->where('user_id', $trade->user_id);
        }])->get()->mapWithKeys(function ($p) {
            return [$p->database_field => $p->userFieldValue?->value ?? null];
        })->toArray();

        $context = [
            'form' => $form,
            'trade' => $trade,
            'all_forms' => $all_forms,
            'portfolioValues' => $portfolioValues,
            'resolved_forms' => [],
        ];

        $local_data = [];
        foreach ($form->sections as $section) {
            foreach ($section->data ?? [] as $item) {
                $local_data[strtolower($item['input'] ?? '')] = $item['value'] ?? null;
            }
        }
        $inputs = array_merge($local_data, $inputs);

        $lockedFields = array_map('strtolower', array_keys(array_filter($locked, function ($v) {
            return $v === true;
        })));

        $formulaModeInputs = collect($formulaModeInputs)->mapWithKeys(function ($v, $k) {
            return [strtolower($k) => $v];
        })->toArray();

        $changedFields = array_map('strtolower', $changedFields);

        $jsonFile = public_path('TradeReply_Formulas.json');
        $formulaData = json_decode(file_get_contents($jsonFile), true) ?? [];

        $formulaMap = [];
        foreach ($formulaData as $item) {
            $dbField = strtolower($item['DATABASE FIELD'] ?? '');
            if ($dbField) {
                $formulaMap[$dbField] = $item;
            }
        }

        $updatedFields = [];
        $iterationCount = 0;
        $maxIterations = 20;

        $scopeOrder = ['PORTFOLIO', 'TRADE', 'TRANSACTION'];

        do {
            $newUpdates = [];

            foreach ($formulaData as $formulaItem) {
                foreach ($scopeOrder as $scopeKey) {
                    $scope = $formulaItem['SCOPES'][$scopeKey] ?? null;
                    if (!$scope) {
                        continue;
                    }

                    $targetField = strtolower($scope['DATABASE FIELD'] ?? '');
                    if (!$targetField) {
                        continue;
                    }

                    $formula = $scope['FORMULA'] ?? null;
                    if (!$formula && !isset($scope['value_resolution_type'])) {
                        continue;
                    }

                    $dependentFields = $this->extractFormulaDependencies($formula);

                    $shouldCalculate = array_intersect($changedFields, $dependentFields) || array_intersect(array_keys($updatedFields), $dependentFields);

                    if (!$computeAll && !$shouldCalculate) {
                        continue;
                    }

                    if (in_array($targetField, $lockedFields, true)) {
                        continue;
                    }

                    if (isset($formulaModeInputs[$targetField]) && !$formulaModeInputs[$targetField]) {
                        continue;
                    }

                    $transFormType = $scope['transaction_form_type'] ?? null;
                    $scopeResolve = $scope['scope'] ?? null;

                    $resolve = 'self';

                    if (!isset($scope['scope']) && $scopeKey === 'TRANSACTION' && $form_type === 'EXIT') {
                        $resolve = 'linked_entry';
                    }

                    $lowerFormType = strtolower($form_type);
                    if ($scopeResolve && isset($scopeResolve[$lowerFormType])) {
                        $resolve = $scopeResolve[$lowerFormType]['resolve'] ?? $resolve;
                    }

                    if ($form_type === 'ENTRY' && $resolve === 'last_linked_exit') {
                        $resolve = 'self';
                    }

                    $vars = [];
                    if ($transFormType && isset($transFormType[$form_type])) {
                        $vars = $transFormType[$form_type];
                    }

                    $calculated = $this->evaluateFallbackFormula($formula, $inputs, $context, $targetField, $resolve, $vars);

                    if (!is_null($calculated) && $calculated != ($inputs[$targetField] ?? null)) {
                        $newUpdates[$targetField] = $calculated;
                        $inputs[$targetField] = $calculated;
                    }
                }
            }

            $updatedFields = array_merge($updatedFields, $newUpdates);
            $iterationCount++;
        } while (!empty($newUpdates) && $iterationCount < $maxIterations);

        return $updatedFields;
    }

    private function evaluateFallbackFormula(array $formula, array &$inputs, array $context, string $targetField, string $resolve, array $vars)
    {
        for ($i = 1; $i <= 5; $i++) {
            if (($formula["f$i"] ?? 0) == 1 && isset($formula["f{$i}v"])) {
                $f_formula = $this->replaceVariables($formula["f{$i}v"], $vars);
                $calculated = $this->evaluateFormula($f_formula, $inputs, $context, $targetField, $resolve);
                if (!is_null($calculated)) {
                    return $calculated;
                }
            }
        }
        return null;
    }
    private function replaceVariables($node, array $vars)
    {
        if (is_string($node)) {
            return preg_replace_callback('/\{(\w+)\}/', function ($matches) use ($vars) {
                return $vars[$matches[1]] ?? $matches[0];
            }, $node);
        }
        if (is_array($node)) {
            return array_map(function ($value) use ($vars) {
                return $this->replaceVariables($value, $vars);
            }, $node);
        }
        return $node;
    }

    private function evaluateFormula($node, array &$inputs, array $context, string $targetField, string $resolve)
    {
        if (is_numeric($node)) {
            return floatval($node);
        }

        if (is_string($node)) {
            $upperNode = strtoupper($node);
            if ($upperNode === 'TODAY()') {
                return date('Y-m-d');
            }
            if ($upperNode === 'NOW()') {
                return date('Y-m-d H:i:s');
            }
            return $this->getFieldValue($node, $inputs, $context, $resolve) ?? 0;
        }

        if (!is_array($node)) {
            return null;
        }

        if (isset($node['aggregate']) && array_key_exists('aggregate', $node)) {
            $agg = $node['aggregate'];
            $source = $agg['source'];
            $operation = strtoupper($agg['operation']);
            $field_node = $agg['field'];

            $linked_forms = collect();
            $form_type = strtoupper($context['form']->type);

            if ($source === 'self') {
                $linked_forms = collect([$context['form']]);
            } elseif ($source === 'linked_exits') {
                if ($form_type === 'ENTRY') {
                    $linked_forms = $context['all_forms']->filter(function ($f) use ($context) {
                        return $f->type === 'exit' && $this->getFormFieldValue($f, 'linked_entry') == $context['form']->index;
                    });
                }
            } elseif ($source === 'sequential_exits') {
                if ($form_type === 'EXIT') {
                    $linked_entry = $this->getFormFieldValue($context['form'], 'linked_entry');
                    if ($linked_entry !== null) {
                        $linked_forms = $context['all_forms']->filter(function ($f) use ($linked_entry, $context) {
                            return $f->type === 'exit' && $f->index < $context['form']->index && $this->getFormFieldValue($f, 'linked_entry') == $linked_entry;
                        });
                    }
                }
            }

            $values = $linked_forms->map(function ($f) use ($field_node, $context, $targetField, $resolve) {
                if (is_string($field_node)) {
                    return floatval($this->getFormFieldValue($f, $field_node) ?? 0);
                } elseif (is_array($field_node)) {
                    $local_data_f = [];
                    foreach ($f->sections as $section) {
                        foreach ($section->data ?? [] as $item) {
                            $local_data_f[strtolower($item['input'] ?? '')] = $item['value'] ?? null;
                        }
                    }
                    $inputs_f = $local_data_f;
                    $context_f = $context;
                    $context_f['form'] = $f;
                    return floatval($this->evaluateFormula($field_node, $inputs_f, $context_f, $targetField, $resolve) ?? 0);
                } else {
                    return 0;
                }
            })->toArray();

            if (empty($values)) {
                return 0;
            }

            if ($operation === 'SUM') {
                return array_sum($values);
            } elseif ($operation === 'AVERAGE') {
                return array_sum($values) / count($values);
            }
            return 0;
        }

        if (isset($node['operation']) && $node['operation'] === 'MIN' && isset($node['window']) && $node['window'] === 'OVER ()') {
            $field_node = $node['field'] ?? '';
            $timestamps = $context['all_forms']->map(function ($f) use ($field_node, $context, $targetField, $resolve) {
                if (is_string($field_node)) {
                    return strtotime($this->getFormFieldValue($f, $field_node) ?? '');
                } elseif (is_array($field_node)) {
                    $local_data_f = [];
                    foreach ($f->sections as $section) {
                        foreach ($section->data ?? [] as $item) {
                            $local_data_f[strtolower($item['input'] ?? '')] = $item['value'] ?? null;
                        }
                    }
                    $inputs_f = $local_data_f;
                    $context_f = $context;
                    $context_f['form'] = $f;
                    return strtotime($this->evaluateFormula($field_node, $inputs_f, $context_f, $targetField, $resolve) ?? '');
                } else {
                    return null;
                }
            })->filter()->toArray();

            if (empty($timestamps)) {
                return null;
            }

            $min_ts = min($timestamps);
            return date('Y-m-d H:i:s', $min_ts);
        }

        $op = strtoupper($node['operation'] ?? '');
        $fields = $node['fields'] ?? [];

        switch ($op) {
            case 'ADD':
                $values = array_map(function ($f) use ($inputs, $context, $targetField, $resolve) {
                    $val = $this->evaluateFormula($f, $inputs, $context, $targetField, $resolve);
                    return is_numeric($val) ? floatval($val) : 0;
                }, $fields);
                return array_sum($values);

            case 'SUBTRACT':
                $a = $this->evaluateFormula($fields[0] ?? 0, $inputs, $context, $targetField, $resolve);
                $b = $this->evaluateFormula($fields[1] ?? 0, $inputs, $context, $targetField, $resolve);
                return (is_numeric($a) ? floatval($a) : 0) - (is_numeric($b) ? floatval($b) : 0);

            case 'MULTIPLY':
                return array_product(array_map(function ($f) use ($inputs, $context, $targetField, $resolve) {
                    $val = $this->evaluateFormula($f, $inputs, $context, $targetField, $resolve);
                    return is_numeric($val) ? floatval($val) : 1;
                }, $fields));

            case 'DIVIDE':
                $numerator = $this->evaluateFormula($fields[0] ?? 0, $inputs, $context, $targetField, $resolve);
                $denominator = $this->evaluateFormula($fields[1] ?? 1, $inputs, $context, $targetField, $resolve);
                $num = is_numeric($numerator) ? floatval($numerator) : 0;
                $den = is_numeric($denominator) ? floatval($denominator) : 0;
                return $den == 0 ? 0 : $num / $den;

            case 'AVERAGE':
                $values = array_map(function ($f) use ($inputs, $context, $targetField, $resolve) {
                    return $this->evaluateFormula($f, $inputs, $context, $targetField, $resolve);
                }, $fields);
                $numericValues = array_filter($values, 'is_numeric');
                return count($numericValues) ? array_sum($numericValues) / count($numericValues) : null;

            case 'ABS':
                return abs($this->evaluateFormula($fields[0] ?? 0, $inputs, $context, $targetField, $resolve));

            case 'REFERENCE':
                $field_node = $node['field'] ?? '';
                if (is_array($field_node)) {
                    return $this->evaluateFormula($field_node, $inputs, $context, $targetField, $resolve);
                } else {
                    return $this->getFieldValue($field_node, $inputs, $context, $resolve);
                }

                // no break
            case 'WHEN_SET':
                $conditionKey = $node['condition'] ?? '';
                $valueKey = $node['value'] ?? '';
                if (!empty($inputs[$conditionKey])) {
                    return $inputs[$valueKey] ?? null;
                }
                return null;

            case 'IF':
                $cond = $node['condition'] ?? [];
                $evaluateCondition = function ($cond) use (&$evaluateCondition, $inputs, $context, $targetField, $resolve) {
                    $op = strtoupper($cond['operation'] ?? '');

                    if ($op === 'OR' || $op === 'AND') {
                        $results = array_map(function ($c) use ($evaluateCondition, $inputs, $context, $targetField, $resolve) {
                            return $evaluateCondition($c);
                        }, $cond['conditions'] ?? []);
                        return $op === 'OR' ? in_array(true, $results) : !in_array(false, $results);
                    }

                    $field = isset($cond['field']) ? $cond['field'] : null;
                    if (is_array($field)) {
                        $fieldValue = $this->evaluateFormula($field, $inputs, $context, $targetField, $resolve);
                    } else {
                        $fieldValue = $field ? $this->getFieldValue($field, $inputs, $context, $resolve) : null;
                    }

                    $value = isset($cond['value']) ? $cond['value'] : null;
                    if (is_array($value)) {
                        $value = $this->evaluateFormula($value, $inputs, $context, $targetField, $resolve);
                    }

                    return match ($op) {
                        '=', '==' => $fieldValue == $value,
                        '!=', '<>' => $fieldValue != $value,
                        '>' => $fieldValue > $value,
                        '>=' => $fieldValue >= $value,
                        '<' => $fieldValue < $value,
                        '<=' => $fieldValue <= $value,
                        'IN' => in_array($fieldValue, $cond['values'] ?? []),
                        'IS_NULL' => is_null($fieldValue) || $fieldValue === '',
                        default => false,
                    };
                };

                $match = $evaluateCondition($cond);
                return $this->evaluateFormula($match ? $node['true_case'] : $node['false_case'], $inputs, $context, $targetField, $resolve);

            case 'DATE_FORMAT':
                $field_node = $node['field'] ?? '';
                $field_value = is_array($field_node) ? ($this->evaluateFormula($field_node, $inputs, $context, $targetField, $resolve) ?? '') : ($this->getFieldValue($field_node, $inputs, $context, $resolve) ?? '');
                $timestamp = strtotime($field_value);
                return $timestamp ? date($node['format'] ?? 'Y-m-d', $timestamp) : null;

            case 'YEAR':
                $field_node = $node['field'] ?? '';
                $field_value = is_array($field_node) ? ($this->evaluateFormula($field_node, $inputs, $context, $targetField, $resolve) ?? '') : ($this->getFieldValue($field_node, $inputs, $context, $resolve) ?? '');
                return date('Y', strtotime($field_value));

            case 'WEEK':
                $field_node = $node['field'] ?? '';
                $field_value = is_array($field_node) ? ($this->evaluateFormula($field_node, $inputs, $context, $targetField, $resolve) ?? '') : ($this->getFieldValue($field_node, $inputs, $context, $resolve) ?? '');
                return date('W', strtotime($field_value));

            case 'TIMESTAMPDIFF':
                $unit = strtolower($node['unit'] ?? 'day');
                $start = strtotime($this->evaluateFormula($node['field1'], $inputs, $context, $targetField, $resolve) ?? '');
                $end = strtotime($this->evaluateFormula($node['field2'], $inputs, $context, $targetField, $resolve) ?? '');
                if (!$start || !$end) {
                    return null;
                }
                $diff = $end - $start;
                return match ($unit) {
                    'hour' => round($diff / 3600),
                    'day' => round($diff / 86400),
                    'week' => round($diff / 604800),
                    'month' => (date('Y', $end) - date('Y', $start)) * 12 + (date('m', $end) - date('m', $start)),
                    'year' => date('Y', $end) - date('Y', $start),
                    default => null,
                };

            case 'DATEDIF':
            case 'DATEDIFF':
                if (!isset($fields[0], $fields[1])) {
                    return null;
                }
                $start = strtotime($this->evaluateFormula($fields[0], $inputs, $context, $targetField, $resolve) ?? '');
                $end = strtotime($this->evaluateFormula($fields[1], $inputs, $context, $targetField, $resolve) ?? '');
                $unit = $fields[2] ?? 'D';
                if (!$start || !$end) {
                    return null;
                }
                if ($unit === 'D') {
                    return round(($end - $start) / 86400);
                }
                return null;

            default:
                return null;
        }
    }

    private function getFieldValue(string $field, array $inputs, array $context, string $resolve)
    {
        $field = strtolower($field);

        if (strpos($field, 'portfolio_') === 0) {
            return $context['portfolioValues'][$field] ?? null;
        }

        if ($resolve === 'self') {
            return $inputs[$field] ?? null;
        }

        $cacheKey = $resolve;
        if (!array_key_exists($cacheKey, $context['resolved_forms'])) {
            $resolved_form = null;
            if ($resolve === 'linked_entry') {
                $linked_index = $this->getFormFieldValue($context['form'], 'linked_entry');
                if ($linked_index !== null) {
                    $resolved_form = $context['all_forms']->first(function ($f) use ($linked_index) {
                        return $f->type === 'entry' && $f->index == $linked_index;
                    });
                }
            } elseif ($resolve === 'first_entry_in_trade') {
                $resolved_form = $context['all_forms']->where('type', 'entry')->sortBy('index')->first();
            } elseif ($resolve === 'last_linked_exit') {
                $form_type = strtoupper($context['form']->type);
                if ($form_type === 'ENTRY') {
                    $linked_exits = $context['all_forms']->filter(function ($f) use ($context) {
                        return $f->type === 'exit' && $this->getFormFieldValue($f, 'linked_entry') == $context['form']->index;
                    })->sortByDesc('index');
                    $resolved_form = $linked_exits->first();
                }
            }
            $context['resolved_forms'][$cacheKey] = $resolved_form;
        }

        $resolved_form = $context['resolved_forms'][$cacheKey];
        if (!$resolved_form && $resolve !== 'self') {
            return $inputs[$field] ?? null;
        }

        if (!$resolved_form) {
            return null;
        }

        return $this->getFormFieldValue($resolved_form, $field) ?? null;
    }

    private function getFormFieldValue(TradeForm $form, string $field)
    {
        $field = strtolower($field);
        foreach ($form->sections as $section) {
            foreach ($section->data ?? [] as $item) {
                if (strtolower($item['input'] ?? '') === $field) {
                    return $item['value'] ?? null;
                }
            }
        }
        return null;
    }

    private function extractFormulaDependencies($formula): array
    {
        $dependencies = [];
        $formula = is_array($formula) ? $formula : [$formula];

        array_walk_recursive($formula, function ($value) use (&$dependencies) {
            if (is_string($value) && !is_numeric($value) && strpos($value, '{') === false) {
                $dependencies[] = strtolower($value);
            }
        });

        return array_unique($dependencies);
    }

    /**
     * Fetch all dynamic fields for Transaction, Trade, and Portfolio.
     */
    public function fetchAllFields(): JsonResponse
    {
        $formulas = $this->tradeBuilderService->getTradeReplayFormulas();

        $tradeFields       = [];
        $portfolioFields   = [];
        $transactionFields = [];

        foreach ($formulas as $item) {
            $scopes = $item['SCOPES'] ?? [];

            if (isset($scopes['TRANSACTION']['DATABASE FIELD'])) {
                $transactionFields[] = $scopes['TRANSACTION']['DATABASE FIELD'];
            }

            if (isset($scopes['TRADE']['DATABASE FIELD'])) {
                $tradeFields[] = $scopes['TRADE']['DATABASE FIELD'];
            }

            if (isset($scopes['PORTFOLIO']['DATABASE FIELD'])) {
                $portfolioFields[] = $scopes['PORTFOLIO']['DATABASE FIELD'];
            }
        }

        $transactions = TransactionFieldDefinition::query()
            ->join('field_definitions', 'transaction_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('transaction_field_definitions.database_field', $transactionFields)
            ->whereNotIn('transaction_field_definitions.database_field', ['transaction_manual_deposit', 'transaction_manual_deposit_type', 'transaction_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'transaction_field_definitions.database_field',
                'transaction_field_definitions.summary',
                'transaction_field_definitions.account_field',
                'transaction_field_definitions.account_field_value'
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $trades = TradeFieldDefinition::query()
            ->join('field_definitions', 'trade_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('trade_field_definitions.database_field', $tradeFields)
            ->whereNotIn('trade_field_definitions.database_field', ['trade_manual_deposit', 'trade_manual_deposit_type', 'trade_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'trade_field_definitions.database_field',
                'trade_field_definitions.summary',
                'trade_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $portfolios = PortfolioFieldDefinition::query()
            ->with(['userFieldValue' => function ($query) {
                $query->where('user_id', auth()->id());
            }])
            ->join('field_definitions', 'portfolio_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('portfolio_field_definitions.database_field', $portfolioFields)
            ->whereNotIn('portfolio_field_definitions.database_field', ['portfolio_manual_deposit', 'portfolio_manual_deposit_type', 'portfolio_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'portfolio_field_definitions.database_field',
                'portfolio_field_definitions.summary',
                'portfolio_field_definitions.account_field',
                'portfolio_field_definitions.account_field_value',
                'portfolio_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['portfolioValue']  = $item->userFieldValue->value ?? null;
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        return response()->json([
            'transactions' => $transactions,
            'trades'       => $trades,
            'portfolios'   => $portfolios
        ]);
    }
}

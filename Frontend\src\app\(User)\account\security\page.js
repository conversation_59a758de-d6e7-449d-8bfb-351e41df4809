'use client';

import React, { useState, useEffect } from 'react'
import { Col, Row } from "react-bootstrap";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { EditIconSvg } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/Security.scss";
import "@/css/account/AccountDetails.scss";
import TwoFactorSecurity from "./partial/TwoFactorSecurity";
import SecretQuestions from "./partial/SecretQuestions";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import RecentLoginActivity from "./partial/RecentLoginActivity";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { get } from "@/utils/apiUtils";
import dayjs from "dayjs";



export default function Security() {
    const router = useRouter();
    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    const [hasLoggedOut, setHasLoggedOut] = useState(false);
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const recentdata = [
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Android",
            date: "May 5, 2024 2:06 PM",
            city: "Florida , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Windows PC",
            date: "Jun 16, 2024 2:06 PM",
            city: "Miami , United States",
        },
        {
            site: "Tradereply.com Website",
            sitepc: "Android",
            date: "May 5, 2024 2:06 PM",
            city: "Florida , United States",
        },
    ];

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Security: Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Always fetch fresh data first to ensure we have the latest from DB
        fetchUserData();

        // Also check for cached data as fallback
        const storedUser = localStorage.getItem('user');

        if (reduxUser) {
            // Use Redux data if available, but still fetch fresh data
            setUserData(reduxUser);
            setLoading(false);
        } else if (storedUser) {
            // Use localStorage data as immediate fallback while API loads
            try {
                const parsedUser = JSON.parse(storedUser);
                setUserData(parsedUser);
                // Update Redux store
                dispatch(setUser(parsedUser));
                setLoading(false);
            } catch (err) {
                console.error('Error parsing stored user data:', err);
            }
        }
    }, []); // Empty dependency array to run only on mount

    // Format password last updated date
    const formatPasswordLastUpdated = (passwordLastUpdated) => {
        if (!passwordLastUpdated) {
            return 'Never updated';
        }

        try {
            const date = dayjs(passwordLastUpdated);
            if (!date.isValid()) {
                return 'Date unavailable';
            }
            return date.format('MMMM DD, YYYY');
        } catch (error) {
            console.error('Error formatting password date:', error);
            return 'Date unavailable';
        }
    };




    const logoutUser = () => {
        debugger;
        Cookies.remove("authToken");
        localStorage.setItem("loggedOut", Date.now());

        // Call logout API
        fetch("/api/logout", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
        }).finally(() => {
            router.replace("/login");
        });
    };

    const metaArray = {
        noindex: true,
        title: "Account Security | Protect Your Account | TradeReply",
        description: "Secure your TradeReply.com account. Update your password, enable two-factor authentication, and monitor recent security activity.",
        canonical_link: "https://www.tradereply.com/account/security",
        og_site_name: "TradeReply",
        og_title: "Security Settings | Protect Your Account | TradeReply",
        og_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
        twitter_title: "Security Settings | Protect Your Account | TradeReply",
        twitter_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="security_sec">
                    <SidebarHeading title="Security" />
                    <Row>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <CommonBlackCard
                                title="Password"
                                Linktext="Update"
                                link="/account/change-password?from=/account/security"
                                editicon={<EditIconSvg />}
                                className="account_card pullcontent"
                                text="We recommend updating your password periodically to prevent unauthorized access."
                            >
                                <div className="account_card_list">
                                    <ul>
                                        <li>
                                            <span>Password </span>
                                            {loading ? (
                                                <span>Loading...</span>
                                            ) : error ? (
                                                <span className="text-danger">Failed to load</span>
                                            ) : (
                                                <span>Last Updated {formatPasswordLastUpdated(userData?.password_last_updated)}</span>
                                            )}
                                        </li>
                                    </ul>
                                </div>
                            </CommonBlackCard>
                        </Col>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <TwoFactorSecurity />

                        </Col>
                        <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4">
                            <SecretQuestions />
                        </Col>
                        <Col xs={12} className="d-flex mb-4 mb-lg-4 ">
                            <RecentLoginActivity />
                        </Col>
                    </Row>
                </div>
            </AccountLayout>
        </>
    )
}

'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function AuthCallback() {
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        const token = searchParams.get('token');
        const error = searchParams.get('error');

        if (error) {
            console.error('OAuth Error:', error);
            return;
        }

        if (token) {
            localStorage.setItem('auth_token', token);
            router.replace('/dashboard');
        }
    }, [searchParams, router]);

    return <p>Logging you in...</p>;


}
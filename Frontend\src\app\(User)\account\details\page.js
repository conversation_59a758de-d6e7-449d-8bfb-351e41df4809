'use client';
import { Row } from "react-bootstrap";
import React from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/AccountDetails.scss";
import PersonalInformation from "./Partial/PersonalInformation";
import LocalizationSettings from "./Partial/LocalizationSettings";
import PhoneNumber from "./Partial/PhoneNumber";
import Email from "./Partial/Email";
import Username from "./Partial/Username";
import AddressBook from "./Partial/AddressBook";

export default function AcoountDetails() {
  const metaArray = {
    noindex: true,
    title: "Account Details | Update Info | TradeReply",
    description: "Update your personal information on TradeReply.com. Manage your phone number, email, language, time zone, and other account settings.",
    canonical_link: "https://www.tradereply.com/account/details",
    og_site_name: "TradeReply",
    og_title: "Account Details | Update Info | TradeReply",
    og_description: "Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.",
    twitter_title: "Account Details | Update Info | TradeReply",
    twitter_description: "Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_details">
          <SidebarHeading title="Account Details" />
          <Row>
            <PersonalInformation />
            <LocalizationSettings />
            <PhoneNumber />
            <Email />
            <Username />
            <AddressBook />
          </Row>
        </div>
      </AccountLayout>
    </>
  )
}

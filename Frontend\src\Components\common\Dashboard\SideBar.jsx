"use client";
import NavLink from "@/Components/UI/NavLink";
import Link from "next/link";
import {
  CalculatorIcon,
  PlusIcon,
  RightArrowIcon
} from "@/assets/svgIcons/SvgIcon";
import { useEffect, useRef, useState, useLayoutEffect } from "react";
import "../../../css/dashboard/Sidebar.scss";
import { SYSTEM_ROLES } from "@/constants";
import Cookies from "js-cookie";
import AuthOverlayMessage from "../AuthOverlayMessage";
import LoadingSpinner from "../LoadingSpinner";

const Sidebar = () => {
  const sliderRef = useRef(null);
  const [disableLeft, setDisableLeft] = useState(true);
  const [disableRight, setDisableRight] = useState(false);
  const [user, setUser] = useState(null);

  const [isMobile, setIsMobile] = useState(false);
  const [loginToken, setLoginToken] = useState(undefined); // Initially undefined

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens || null); // Set token once fetched

    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
      checkScrollPosition();
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);

    return () => {
      window.removeEventListener("resize", checkScreenSize);
    };
  }, []);
  useLayoutEffect(() => {
    checkScrollPosition();
  }, []);

  const smoothScroll = (amount) => {
    if (sliderRef.current) {
      const start = sliderRef.current.scrollLeft;
      const end = start + amount;
      const duration = 300;
      const startTime = performance.now();

      const step = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const scrollAmount = start + (end - start) * progress;

        sliderRef.current.scrollLeft = scrollAmount;

        if (progress < 1) {
          requestAnimationFrame(step);
        } else {
          checkScrollPosition(); // <-- Update buttons after scroll
        }
      };
      requestAnimationFrame(step);
    }
  };

  const scrollLeft = () => {
    if (isMobile) {
      smoothScroll(-260);
    }
  };

  const scrollRight = () => {
    if (isMobile) {
      smoothScroll(260);
    }
  };
  const checkScrollPosition = () => {
    if (!sliderRef.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = sliderRef.current;

    const isAtStart = scrollLeft <= 0;
    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 1;

    setDisableLeft(isAtStart);
    setDisableRight(isAtEnd);
  };

  useEffect(() => {
    const slider = sliderRef.current;
    if (!slider) return;

    slider.addEventListener("scroll", checkScrollPosition);
    window.addEventListener("resize", checkScrollPosition);

    checkScrollPosition();

    return () => {
      slider.removeEventListener("scroll", checkScrollPosition);
      window.removeEventListener("resize", checkScrollPosition);
    };
  }, []);
  return (
    <>
      <div className="admin_sidebar">
        {loginToken === undefined ? (
          <LoadingSpinner />
        ) : (
          <>
            <div className={`${!loginToken ? "auth-blur-effect" : "w-100"}`}>
              <button
                className={`scroll-btn left ${disableLeft ? "disabled" : ""}`}
                disabled={disableLeft}
                onClick={scrollLeft}
              >
                <RightArrowIcon />
              </button>
              <div className="admin_sidebar_wrapper" ref={sliderRef}>
                {user?.role === "Super admin" ? (
                  superAdminRoutes.map((item) => (
                    <div className="linkList" key={item.path}>
                      <NavLink href={item.path}>
                        <span className="linktext">{item.name.slice(0, 1)}</span>
                        <span className="fulltext"> {item.name}</span>
                      </NavLink>
                    </div>
                  ))
                ) : (
                  <>
                    <div className="linkList">
                      <NavLink href="/dashboard/portfolio-manager">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-portfolio-manager-coin.svg" alt="Portfolio Manager Icon" />
                        </span>
                        <span className="fulltext">Portfolio Manager</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/trade-manager">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-trade-manager-icon.svg" alt="Trade Manager Icon" />
                        </span>
                        <span className="fulltext">Trade Manager</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/strategy-manager">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-strategy-icon.svg" alt="Strategy Manager Icon" />
                        </span>
                        <span className="fulltext">Strategy Manager</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/tag-manager">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-tag-manager-icon.svg" alt="Tag Manager Icon" />
                        </span>
                        <span className="fulltext">Tag Manager</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/trade-analysis">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-analysis-icon.svg" alt="Trade Analysis Icon" />
                        </span>
                        <span className="fulltext">Trade Analysis</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/trade-replay">
                        <span className="linktext">
                          <img src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-replay-icon.svg" alt="Trade Replay Icon" />
                        </span>
                        <span className="fulltext">Trade Replay</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="/dashboard/trading-calculator">
                        <span className="linktext">
                          <CalculatorIcon />
                        </span>
                        <span className="fulltext">Trading Calculators</span>
                      </NavLink>
                    </div>
                    {user?.role == "Super admin" && (
                      <>
                        <div className="linkList">
                          <NavLink href="/category">
                            <span className="linktext">
                              <CalculatorIcon />
                            </span>
                            <span className="fulltext">Category</span>
                          </NavLink>
                        </div>
                        <div className="linkList">
                          <NavLink href="/education">
                            <span className="linktext">
                              <CalculatorIcon />
                            </span>
                            <span className="fulltext">Education</span>
                          </NavLink>
                        </div>
                        <div className="linkList">
                          <NavLink href="/blogs">
                            <span className="linktext">
                              <CalculatorIcon />
                            </span>
                            <span className="fulltext">Blogs</span>
                          </NavLink>
                        </div>
                      </>
                    )}
                    <div className="linkList">
                      <NavLink href="/dashboard">
                        <span className="linktext">1</span>
                        <span className="fulltext"> Dashboard1</span>
                      </NavLink>
                    </div>
                    <div className="linkList">
                      <NavLink href="#">
                        <span className="linktext">
                          <PlusIcon />
                        </span>
                        <span className="fulltext">Add Dashboard</span>
                      </NavLink>
                    </div>
                  </>
                )}
              </div>
              <button
                className={`scroll-btn right ${disableRight ? "disabled" : ""}`}
                disabled={disableRight}
                onClick={scrollRight}
              >
                <RightArrowIcon />
              </button>
            </div>
            <AuthOverlayMessage isLoggedIn={loginToken} />
          </>
        )}
      </div>
    </>
  );
};

const superAdminRoutes = [
  // Admin Routes
  {
    name: "Blog",
    path: "/super-admin/blogs",
    role: SYSTEM_ROLES.SUPER_ADMIN
  },
  {
    name: "Education",
    path: "/super-admin/education",
    role: SYSTEM_ROLES.SUPER_ADMIN
  },
  {
    name: "Category",
    path: "/super-admin/category",
    role: SYSTEM_ROLES.SUPER_ADMIN
  }
];

export default Sidebar;

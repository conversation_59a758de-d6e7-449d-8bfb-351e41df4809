<?php

namespace App\Console\Commands;

use App\Models\Article;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class UpdateEducationArticleBody extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-education-article-body';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $csvPath = public_path('CSV/Education_articleBody-fields.csv');

        if (!File::exists($csvPath)) {
            $this->error("CSV file not found: $csvPath");
            return 1;
        }

        $csv = array_map('str_getcsv', file($csvPath));
        $header = array_map('trim', array_shift($csv));

        $databaseIndex = array_search('DATABASE FIELD', $header);
        $fieldIndex = array_search('articleBody', $header);

        if ($databaseIndex === false || $fieldIndex === false) {
            $this->error("CSV must have DATABASE and FIELD columns.");
            return 1;
        }

        $educationArticles = Article::where('type', 'education')->get()->keyBy('slug');

        $updated = 0;

        foreach ($csv as $row) {
            $id = trim($row[$databaseIndex]);
            $body = trim($row[$fieldIndex]);

            $slug = str_replace('_', '-', $id) . '-education';

            if ($educationArticles->has($slug)) {
                $article = $educationArticles[$slug];
                $article->body = $body;
                $article->save();
                $updated++;
            }
        }

        $this->info("Updated $updated education articles with articleBody from CSV.");
        return 0;
    }
}

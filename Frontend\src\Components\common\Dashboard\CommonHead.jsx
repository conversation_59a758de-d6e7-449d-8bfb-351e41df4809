"use client";

import { Col, Row, Container } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import "../../../css/dashboard/CommonHead.scss";
import { useEffect, useState } from "react";
import { DropArrowIcon, DropArrowUpIcon } from "@/assets/svgIcons/SvgIcon";
import Cookies from "js-cookie";
import AuthOverlayMessage from "../AuthOverlayMessage";
import LoadingSpinner from "../LoadingSpinner";
import {
  CheckIcon,
  RedCircleCrossIcon,
  GreyCheckIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonHeadSearch from "./Partials/CommonHeadSearch";
import CommonHeadDatebox from "./Partials/CommonHeadDatebox";

const CommonHead = ({
  isShowCalender = true,
  isSaving,
  isDefault,
  isSuccess,
  isError,
  tradeAccounts = [],
  selectedTradeAccountId = null,
  setSelectedTradeAccountId = () => { },
}) => {
  const [loginToken, setLoginToken] = useState(undefined);
  const [user, setUser] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleCollapse = () => {
    setIsExpanded((prev) => !prev);
  };

  useEffect(() => {
    const tokens = Cookies.get("authToken");
    setLoginToken(tokens || null);

    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  if (loginToken === undefined) {
    return (
      <div className="common_head position-relative z-3">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <>
      <div className="common_head_container">
        <Container>
          {loginToken && (
            <div className="common_head z-3">
              <div className="common_head_md">
                <Row>
                  <Col
                    xl={3}
                    sm={6}
                    xs={12}
                    className={`mb-${isShowCalender ? "0" : "3"} mb-xl-0 order-1`}
                  >
                    <div className="commom_tradeacct">
                      <div className="account">
                        <h6>Trade Account</h6>
                      </div>
                      <select
                        value={selectedTradeAccountId || ""}
                        onChange={(e) => setSelectedTradeAccountId(e.target.value)}
                        className="form-select"
                      >
                        <option value="" disabled>
                          Select Trade Account
                        </option>
                        {tradeAccounts.length > 0 ? (
                          tradeAccounts.map((account) => (
                            <option key={account.id} value={account.id}>
                              {account.name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            No accounts available
                          </option>
                        )}
                      </select>
                    </div>
                  </Col>
                  <Col
                    xl={6}
                    sm={isShowCalender ? 6 : undefined}
                    xs={12}
                    className={`order-xl-2 order-3 mb-${isShowCalender ? "0" : "3"} mb-sm-0`}
                  >
                    <CommonHeadSearch />
                  </Col>
                  {!isShowCalender && (
                    <Col
                      xl={3}
                      md={6}
                      sm={6}
                      xs={12}
                      className="mb-3 mb-sm-0 order-xl-3 order-2"
                    >
                      <CommonHeadDatebox />
                    </Col>
                  )}
                </Row>
                <Row>
                  <Col
                    xs={12}
                    className={`mb-${isShowCalender ? "0" : "3"} mb-xl-0 order-1`}
                  >
                    <div className="trade_head_title">
                      <div className="head_draft">
                        {isSaving ? (
                          <div className="head_draft_icons">
                            <img
                              src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                              alt="Saving Icon"
                            />
                            <p>Saving....</p>
                          </div>
                        ) : isSuccess ? (
                          <div className="head_draft_icons">
                            <CheckIcon />
                            <p>Auto-saved</p>
                          </div>
                        ) : isError ? (
                          <div className="head_draft_icons">
                            <RedCircleCrossIcon />
                            <p>Unable to save, retrying</p>
                          </div>
                        ) : (
                          <div className="head_draft_icons">
                            <GreyCheckIcon />
                            <p>All changes saved</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
              <div className="common_head_small">
                <div className="layout_fix">
                  <div className="trade_head_title">
                    <div className="head_draft mt-0 mb-1">
                      {isSaving ? (
                        <div className="head_draft_icons">
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                            alt="Saving Icon"
                          />
                          <p>Saving....</p>
                        </div>
                      ) : isSuccess ? (
                        <div className="head_draft_icons">
                          <CheckIcon />
                          <p>Auto-saved</p>
                        </div>
                      ) : isError ? (
                        <div className="head_draft_icons">
                          <RedCircleCrossIcon />
                          <p>Unable to save, retrying</p>
                        </div>
                      ) : (
                        <div className="head_draft_icons">
                          <GreyCheckIcon />
                          <p>All changes saved</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="d-flex justify-content-end">
                    <button onClick={toggleCollapse}>
                      {isExpanded ? <DropArrowUpIcon /> : <DropArrowIcon />}
                    </button>
                  </div>
                </div>
                {isExpanded && (
                  <Row>
                    <Col xs={12} className="mt-2 mb-3">
                      <div className="commom_tradeacct">
                        <div className="account">
                          <h6>Trade Account</h6>
                        </div>
                        <select
                          value={selectedTradeAccountId || ""}
                          onChange={(e) => setSelectedTradeAccountId(e.target.value)}
                          className="form-select"
                        >
                          <option value="" disabled>
                            Select Trade Account
                          </option>
                          {tradeAccounts.length > 0 ? (
                            tradeAccounts.map((account) => (
                              <option key={account.id} value={account.id}>
                                {account.name}
                              </option>
                            ))
                          ) : (
                            <option value="" disabled>
                              No accounts available
                            </option>
                          )}
                        </select>
                      </div>
                    </Col>
                    <Col xs={12} className={`mb-${isShowCalender ? "0" : "3"}`}>
                      <CommonHeadSearch />
                    </Col>
                    {!isShowCalender && (
                      <Col xs={12}>
                        <CommonHeadDatebox />
                      </Col>
                    )}
                  </Row>
                )}
              </div>
            </div>
          )}
          {!loginToken && (
            <div className="common_head position-relative">
              <div className="auth-blur-effect">
                <Row className="items-center">
                  <Col sm={3} className="mb-3 mb-sm-0 d-none d-md-block">
                    <div className="commom_tradeacct">
                      <h6>Trade Account</h6>
                      <select className="form-select" disabled>
                        <option value="act-123">act-123</option>
                      </select>
                    </div>
                  </Col>
                  <Col sm={6} xs={12} className="mb-3 mb-sm-0">
                    <div className="common_head_search">
                      <CommonSearch
                        icon={true}
                        placeholder="Search & Explore"
                        name="commonsearch2"
                      />
                    </div>
                  </Col>
                  <Col sm={3} className="mb-3 mb-sm-0 d-none d-md-block">
                    <div className="common_head_datebox">
                      <h5>Last 28 days</h5>
                      <h6>May 14 - Jun 10, 2024</h6>
                    </div>
                  </Col>
                </Row>
              </div>
              <AuthOverlayMessage
                Overlay="AuthOverlayRound"
                isLoggedIn={loginToken}
              />
            </div>
          )}
        </Container>
      </div>
    </>
  );
};

export default CommonHead;
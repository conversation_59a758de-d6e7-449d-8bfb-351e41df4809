"use client";
import React, { useEffect, useState, useCallback } from "react";
import DashboardLayout from "@/Layouts/DashboardLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import { Col, Container, Row } from "react-bootstrap";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import "@/css/dashboard/PortfolioManager.scss";
import TradeAccountDetails from "@/Components/common/PortfolioManager/TradeAccountDetails";
import CapitalCashFlow from "@/Components/common/PortfolioManager/CapitalCashFlow";
import RiskManagement from "@/Components/common/PortfolioManager/RiskManagement";
import GrowthProfitAllocation from "@/Components/common/PortfolioManager/GrowthProfitAllocation";
import DeleteAll from "@/Components/common/PortfolioManager/DeleteAll";
import DeleteTradeAccount from "@/Components/common/PortfolioManager/DeleteTradeAccount";
import { get as GET, post } from "@/utils/apiUtils";
import MiniTabs from "@/Components/common/PortfolioManager/MiniTabs";

export default function PortfolioManager() {
  const [portfolioData, setPortfolioData] = useState({
    account_details: [],
    cash_flow: [],
    risk_management: [],
    growth: [],
  });
  const [accountSizeField, setAccountSizeField] = useState([]);
  const [saveStatus, setSaveStatus] = useState(null);
  const [activeTab, setActiveTab] = useState("Portfolio");
  const tabsTAnalysis = ["Portfolio", "Settings"];
  const addActiveTab = (active) => {
    setActiveTab(active);
  };

  const fetchPortfolioData = useCallback(async () => {
    try {
      const response = await GET("/portfolio");
      setPortfolioData(response);
      setAccountSizeField(
        response?.cash_flow?.find(
          (item) =>
            item.database_field?.toLowerCase() === "portfolio_account_size"
        )
      );
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
    }
  }, []);

  useEffect(() => {
    fetchPortfolioData();
  }, [fetchPortfolioData]);

  const handleFieldChange = async (definitionId, value) => {
    if (!value) {
      return;
    }
    try {
      setSaveStatus("loading");
      await post("portfolio/store", {
        portfolio_field_definition_id: definitionId,
        value: value,
      });
      await fetchPortfolioData();
      setSaveStatus("success");
    } catch (err) {
      setSaveStatus("error");
      console.error("Error saving field:", err);
    } finally {
      setTimeout(() => setSaveStatus(null), 3000);
    }
  };

  const resetData = async () => {
    try {
      await post("portfolio/delete");
      await fetchPortfolioData();
    } catch (err) {
      console.error("Error saving field:", err);
    }
  };

  const metaArray = {
    noindex: true,
    title: "Portfolio Manager | Manually Build Your Trades | TradeReply",
    description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    canonical_link: "https://www.tradereply.com/dashboard/trade-builder",
    og_site_name: "TradeReply",
    og_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    ogdescription:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
    twitter_title: "Trade Builder | Manually Build Your Trades | TradeReply",
    twitter_description:
      "Manually build and customize your trades with TradeReply's Trade Builder. Create precise trades tailored to your strategy.",
  };
  return (
    <DashboardLayout>
      <MetaHead props={metaArray} />
      <CommonHead
        isShowCalender="false"
        isDefault={saveStatus === "default"}
        isSaving={saveStatus === "loading"}
        isSuccess={saveStatus === "success"}
        isError={saveStatus === "error"}
      />
      <Container className="portfolio_manager">
        <div className="trade_head">
          <AdminHeading heading="Portfolio Manager" />
        </div>
        <button className="mb-3" onClick={resetData}>
          Reset Values
        </button>
        <MiniTabs
          onClick={addActiveTab}
          activeTab={activeTab}
          tabsTAnalysis={tabsTAnalysis}
        />
        <div className="portfolio_manager_card">
          <p>
            Data applies to <span>acct-123</span> Trade Account.
          </p>

          {activeTab == "Portfolio" && (
            <Row>
              <Col xl={6} xs={12}>
                <TradeAccountDetails
                  data={portfolioData.account_details || []}
                  onChangeField={handleFieldChange}
                />
                <CapitalCashFlow
                  data={portfolioData.cash_flow || []}
                  onChangeField={handleFieldChange}
                />
              </Col>
              <Col xl={6} xs={12}>
                <RiskManagement
                  data={portfolioData.risk_management || []}
                  onChangeField={handleFieldChange}
                />
                <GrowthProfitAllocation
                  data={portfolioData.growth || []}
                  accountSizeField={accountSizeField}
                  onChangeField={handleFieldChange}
                />
              </Col>
            </Row>
          )}
          {activeTab == "Settings" && (
            <>
              <DeleteAll />
              <DeleteTradeAccount />
            </>
          )}
        </div>
      </Container>
    </DashboardLayout>
  );
}

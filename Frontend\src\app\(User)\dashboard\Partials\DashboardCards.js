import React from "react";
import { Col, Row } from "react-bootstrap";
import { PlusIcon, TradeIcon } from "@/assets/svgIcons/SvgIcon";

export default function DashboardCards() {
    const cards = [
        {
            type: "score",
            title: "Success Score: 97%",
            icon: <TradeIcon />,
            image: "https://cdn.tradereply.com/dev/site-assets/tradereply-for-crypto-traders.png",
        },
        {
            type: "evaluation",
            title: "Evaluation : 50%",
            image: "https://cdn.tradereply.com/dev/site-assets/tradereply-trade-metrics.png",
        },
    ];

    while (cards.length < 9) {
        cards.push({ type: "add" });
    }

    return (
        <Row className="dashboard_card gx-xl-5">
            {cards.map((card, index) => (
                <Col lg={4} sm={6} md={4} xs={12} key={index}>
                    <div
                        className={`overview_box w-100 ${card.type === "score"
                            ? "scorebox darkbox"
                            : card.type === "evaluation"
                                ? "evalbox darkbox"
                                : "scorebox"
                            }`}
                    >
                        {card.type === "add" ? (
                            <button type="button" className="overview_box_icon">
                                <PlusIcon />
                            </button>
                        ) : (
                            <div className="overview_box_fillbox">
                                {card.icon && <span className="mt-3">{card.icon}</span>}
                                <h4 className={`my-3 ${card.type === "evaluation" ? "mb-4" : ""}`}>
                                    {card.title}
                                </h4>
                                <div className="content-center">
                                    <figure>
                                        <img src={card.image} alt={card.title} />
                                    </figure>
                                </div>
                            </div>
                        )}
                    </div>
                </Col>
            ))}
        </Row>
    );
}

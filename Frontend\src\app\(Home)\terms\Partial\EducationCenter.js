import React from 'react'

export default function EducationCenter() {
    return (
        <>
            <div className="inner_content">
                <h4>14. Education Center and Blog Disclaimer</h4>
                <ul>
                    <li>
                        Content Accuracy: The information provided in our Education
                        Center and Blog articles is for informational and educational
                        purposes only. While we strive to ensure the accuracy of the
                        content, TradeReply, including the authors and contributors,
                        does not guarantee the completeness, reliability, or accuracy of
                        the information presented.
                    </li>
                    <li>
                        No Liability: TradeReply, its authors, and contributors are not
                        responsible or liable for any errors, omissions, or inaccuracies
                        in the content. The information is provided "as is" and should
                        not be relied upon for making trading or investment decisions.
                        Users are encouraged to conduct their own research and consult
                        with a qualified financial advisor before making any financial
                        decisions.
                    </li>
                    <li>
                        Opinions and Views: The views and opinions expressed in the
                        Education Center and Blog articles are those of the individual
                        authors and do not necessarily reflect the official policy or
                        position of TradeReply. These opinions should not be taken as
                        financial advice.
                    </li>
                    <li>
                        Use of Information: By using the information from our Education
                        Center and Blog, you acknowledge and agree that TradeReply, its
                        authors, and contributors shall not be held liable for any
                        losses or damages arising from the use of or reliance on this
                        information.
                    </li>
                </ul>
            </div>
        </>
    )
}

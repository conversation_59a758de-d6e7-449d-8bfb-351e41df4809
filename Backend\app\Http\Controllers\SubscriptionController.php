<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\SubscriptionFeature;
use App\Models\SubscriptionFeaturePlan;
use App\Models\UserSubscription;
use App\Services\SubscriptionService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Laravel\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    //
    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Create a new subscription for the user.
     */
    public function createSubscription(Request $request)
    {
        $request->validate([
            'payment_method_id' => 'required|string',
        ]);

        return response()->json($this->subscriptionService->create($request->payment_method_id));
    }

    /**
     * Cancel the user's subscription.
     */
    public function cancelSubscription()
    {
        return response()->json($this->subscriptionService->cancel());
    }

    /**
     * Downgrade the subscription (effective next billing cycle).
     */
    public function downgradeSubscription(Request $request)
    {
        $request->validate([
            'new_plan_id' => 'required|string',
        ]);

        return response()->json($this->subscriptionService->downgrade($request->new_plan_id));
    }

    /**
     * Upgrade the subscription (effective immediately).
     */
    public function upgradeSubscription(Request $request)
    {
        $request->validate([
            'new_plan_id' => 'required|string',
        ]);

        return response()->json($this->subscriptionService->upgrade($request->new_plan_id));
    }

    public function importSubscriptions()
    {
//        // Delete existing records
//        SubscriptionFeaturePlan::truncate();
//        SubscriptionFeature::truncate();
//        Subscription::truncate();

        // Path to the CSV file (Update if stored elsewhere)
        $filePath = storage_path('app/public/subscriptions.csv');

        // Check if file exists
        if (!file_exists($filePath)) {
            return response()->json(['error' => 'CSV file not found'], 404);
        }

        // Read CSV file
        $csvData = array_map('str_getcsv', file($filePath));
        $headers = array_shift($csvData); // Remove header row

        // Define subscription names and prices, including "Free"
        $subscriptions = [
            'Free' => ['price' => 0.00, 'expires_at' => null], // Free plan
            'Essential' => ['price' => 155.40, 'expires_at' => Carbon::now()->addYear()],
            'Plus' => ['price' => 299.40, 'expires_at' => Carbon::now()->addYear()],
            'Premium' => ['price' => 479.40, 'expires_at' => Carbon::now()->addYear()],
        ];

        // Insert subscriptions and get their IDs
        $subscriptionIds = [];
        foreach ($subscriptions as $name => $details) {
            // Check if subscription already exists
            $subscription = DB::table('subscriptions')->where('name', $name)->first();

            if (!$subscription) {
                // Insert new subscription and get ID
                $subscriptionId = DB::table('subscriptions')->insertGetId([
                    'name' => $name,
                    'expires_at' => $details['expires_at'],
                    'price' => $details['price']
                ]);
            } else {
                // Use existing subscription ID
                $subscriptionId = $subscription->id;
            }

            // Store subscription ID in an associative array
            $subscriptionIds[$name] = $subscriptionId;
        }

        // Process each row in CSV
        foreach ($csvData as $row) {
            $featureName = trim($row[0]); // Feature name (Column 1)

            if (!$featureName) continue; // Skip empty rows

            // Insert feature if not exists and get its ID
            $feature = DB::table('subscription_features')->where('name', $featureName)->first();

            if (!$feature) {
                $featureId = DB::table('subscription_features')->insertGetId(['name' => $featureName]);
            } else {
                $featureId = $feature->id;
            }

            // Associate feature with subscriptions based on CSV columns
            foreach (['Free', 'Essential', 'Plus', 'Premium'] as $index => $plan) {
                if (!isset($subscriptionIds[$plan])) continue; // Ensure subscription exists

                $featureLimit = trim($row[$index + 1]); // Get feature value (e.g., "10", "Unlimited", "Yes")

                if (strtolower($featureLimit) !== 'no') { // If feature is available in this plan
                    DB::table('subscription_feature_plans')->updateOrInsert([
                        'subscription_id' => $subscriptionIds[$plan],
                        'subscription_feature_id' => $featureId,
                    ], [
                        'feature_limit' => $featureLimit, // Store the limit (or "Yes"/"Unlimited")
                    ]);
                }
            }
        }

        return response()->json(['message' => 'Subscriptions and features imported successfully']);
    }


    public function startFreeTrial($userId,$trial)
    {
        $existingSubscription = UserSubscription::where('user_id', $userId)->first();

        // If user already had a trial, prevent starting a new one
        if ($existingSubscription && $existingSubscription->is_trial) {
            return response()->json(['message' => 'User has already used the free trial'], 403);
        }

        // If user has a non-trial subscription that hasn't expired, prevent trial activation
        if ($existingSubscription && !$existingSubscription->is_trial && Carbon::now()->lt($existingSubscription->expires_at)) {
            return response()->json(['message' => 'User already has an active subscription'], 403);
        }

        // Assign the free trial
        $subscription = Subscription::where('name', $trial)->first(); // Default trial plan

        $trialSubscription = UserSubscription::create([
            'user_id' => $userId,
            'subscription_id' => $subscription->id,
            'is_trial' => true,
            'trial_expires_at' => Carbon::now()->addDays(30),
        ]);

        return response()->json([
            'message' => 'Free trial activated!',
            'expires_at' => $trialSubscription->trial_expires_at
        ]);
    }



}

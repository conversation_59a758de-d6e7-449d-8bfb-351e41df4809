import axios from 'axios';
import Cookies from 'js-cookie';
import { useRouter } from "next/navigation";

const Url = process.env.NEXT_PUBLIC_API_BASE_URL;
const apiUrl = `${Url}/api/v1/auth`;

export const register = async (userData) => {
  try {
    const response = await axios.post(`${apiUrl}/register`, userData, {
      headers: {
        "Content-Type": "application/json",
      },
      withCredentials: true,
    });

    return response.data; // Return success response
  } catch (error) {
    console.log(error?.response?.data); // Log full response

    if (error.response && error.response.status === 422) {
      return {
        success: false,
        errors: error.response.data.errors || {},
        message: error.response.data.message,
      };
    }

    return {
      success: false,
      errors: { general: "Something went wrong. Please try again." },
    };
  }
};


export const resendVerificationCode = async (payload) => {
  try {
    const response = await axios.post(`${apiUrl}/resend-verification-code`, payload);
    return response.data;
  } catch (error) {
    return { errors: error.response?.data?.errors || {} };
  }
};

export const verifyEmailToken = async (payload) => {
  try {
    const response = await axios.get(`${apiUrl}/verify-token`, {
      params: payload,
      headers: { "Content-Type": "application/json" }
    });

    return response.data;
  } catch (error) {
    return {
      success: false,
      message: error.response?.data?.message || "Invalid token"
    };
  }
};

export const createUsername = async (payload) => {
  try {
    const response = await axios.post(`${apiUrl}/create-username`, payload);
    return response.data;
  } catch (error) {
    return { error };
  }
};

export const forgotPassword = async (type, value, uuid) => {
  try {
    const response = await axios.post(`${apiUrl}/forgot-password`, { type, value, uuid });
    return response.data;
  } catch (error) {
    if (error.response) {
      return { success: false, message: error.response.data.message || "Failed to send password reset link" };
    }
    return { success: false, message: "Something went wrong. Please try again later." };
  }
};

export const resetPassword = async (newPassword) => {
  try {
    const resetPasswordData = JSON.parse(sessionStorage.getItem("reset_password_data"));

    if (!resetPasswordData || !resetPasswordData.uuid) {
      throw new Error("No reset data found");
    }

    const { uuid } = resetPasswordData;

    const response = await axios.post(`${apiUrl}/reset-password`, {
      uuid,
      new_password: newPassword,
    });

    return response.data;
  } catch (error) {
    console.error("Error resetting password", error);
    return { success: false, message: error.response?.data?.message || "Error resetting password" };
  }
};

export const login = async (credentials) => {
  try {
    await axios.get(`${Url}/sanctum/csrf-cookie`, { withCredentials: true });

    const response = await axios.post(`${apiUrl}/login`, credentials, {
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    });

    localStorage.setItem("lastActivity", Date.now().toString());

    console.log('auth.js response.data', response.data);
    return response.data;

  } catch (error) {
    console.error("Login error authjs:", error.response);

    return {
      success: false,
      message: error.response?.data?.message || "Invalid credentials",
      errors: error.response?.data?.errors || [],
      captcha_required:
        error.response?.data?.type === 'captcha'
          ? error.response?.data?.state
          : false,
      lockout_redirect:
        error.response?.data?.type === 'redirect'
          ? error.response?.data?.state
          : false,
      redirect_to: "/locate-account"
    };
  }
};

export const logout = async () => {
  try {
    await axios.post(`${apiUrl}/logout`, {}, {
      headers: {
        Authorization: `Bearer ${Cookies.get("authToken")}`
      }
    });

    return true;

  } catch (error) {
    return false;
  }
};

export const getUser = async () => {
  const token = Cookies.get('authToken');
  if (!token) return null;

  try {
    const response = await axios.get(`${apiUrl}/user`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

'use client';

import React from "react";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import Link from "next/link";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import CommonTable from "@/Components/UI/CommonTable";
import { LogoutIcon } from "@/assets/svgIcons/SvgIcon";
import { Col } from "react-bootstrap";
import { get, post } from "@/utils/apiUtils";

export default function RecentLoginActivity() {
    const router = useRouter();
    const [hasLoggedOut, setHasLoggedOut] = useState(false);
    const [loginActivities, setLoginActivities] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isLoggingOut, setIsLoggingOut] = useState(false);

    // Fetch login activities on component mount
    useEffect(() => {
        fetchLoginActivities();
    }, []);

    const fetchLoginActivities = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await get('login-activity');

            if (response.success) {
                setLoginActivities(response.data);
            } else {
                setError('Failed to fetch login activities');
            }
        } catch (err) {
            console.error('Error fetching login activities:', err);
            setError('Failed to fetch login activities');
        } finally {
            setLoading(false);
        }
    };

    const logoutFromAllDevices = async () => {
        if (isLoggingOut) return;

        try {
            setIsLoggingOut(true);
            const response = await post('login-activity/logout-all-devices');

            if (response.success) {
                Cookies.remove("authToken");
                localStorage.removeItem("user");
                localStorage.removeItem("lastActivity");
                localStorage.setItem("loggedOut", Date.now());
                localStorage.setItem("sessionMessage", "session_expired");
                router.push("/login");
            } else {
                setError("Failed to logout from all devices");
                setIsLoggingOut(false);
            }
        } catch (err) {
            console.error("Error logging out from all devices:", err);
            setError("Failed to logout from all devices");
            setIsLoggingOut(false);
        }
    };


    return (

        <Col lg={12} xs={12} className="mb-3 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader mb-3">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Recent Login Activity</h6>
                    </div>

                    <div className="common_blackcard_innerheader_icon">
                        <button
                            className="d-flex align-items-center gap-2"
                            onClick={logoutFromAllDevices}
                            disabled={isLoggingOut}
                        >
                            {isLoggingOut ? 'Logging out...' : 'Logout from all the devices'} <LogoutIcon />
                        </button>
                    </div>
                </div>

                <div className="account_card_table">
                    <div className="table_heading">
                        <p className="font-weight-600">
                            Notice anything suspicious?{" "}
                            <Link href="/security-check" className="blue_text">
                                Update your password to secure your account.
                            </Link>
                        </p>
                    </div>

                    {error && (
                        <div className="alert alert-danger mb-3">
                            {error}
                        </div>
                    )}

                    {loading ? (
                        <div className="text-center py-4">
                            <div className="spinner-border text-primary" role="status">
                                <span className="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    ) : loginActivities.length === 0 ? (
                        <div className="text-center py-4">
                            <p className="text-muted">No recent login activity found.</p>
                        </div>
                    ) : (
                        <CommonTable fields={false} className="simple_table">
                            {loginActivities.map((item, index) => (
                                <React.Fragment key={`activity-${item.id || index}`}>
                                    {/* Desktop view (md and up) */}
                                    <tr className="hidden md:table-row">
                                        <td>
                                            <img
                                                className="simple_table_imgIcon"
                                                src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg"
                                                alt="Global Blue Icon"
                                            />
                                        </td>
                                        <td>
                                            <h6>{item.site}</h6>
                                            <p className="blue_text">{item.device}</p>
                                            {item.is_current && <small className="text-[#32CD33]">(Current Session)</small>}
                                        </td>
                                        <td>{item.date}</td>
                                        <td>{item.location}</td>
                                    </tr>

                                    {/* Mobile view (below md) */}
                                    <tr className="md:hidden table-row">
                                        <td className="align-top" colSpan={2}>
                                            <div className="flex gap-2">
                                                <img
                                                    className="simple_table_imgIcon"
                                                    src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-blue-global.svg"
                                                    alt="Global Blue Icon"
                                                />
                                                <div>
                                                    <h6>{item.site}</h6>
                                                    <p className="blue_text text-sm">{item.device}</p>
                                                    {item.is_current && <small className="text-[#32CD33]">(Current Session)</small>}
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr className="md:hidden table-row">
                                        <td className="text-sm text-gray-300" colSpan={2}>
                                            <div className="flex justify-between w-full">
                                                <span>{item.date}</span>
                                                <span>{item.location}</span>
                                            </div>
                                        </td>
                                    </tr>
                                </React.Fragment>
                            ))}
                        </CommonTable>
                    )}

                </div>



            </div>

        </Col>
    );
}

"use client";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import NavLink from "@/Components/UI/NavLink";
import Link from "next/link";

export default function UserAbout() {
  const [isEditing, setIsEditing] = useState(false);
  const [aboutData, setAboutData] = useState(
    `Crypto Market Strategist & Blockchain Advocate | Featured in Forbes 8+ Years in Crypto Trading & DeFi | Speaker at Global Crypto Summits | Advisor to Web3 Startups | Passionate about leveraging blockchain technology to drivefinancial empowerment and innovation.`
  );
  const [websiteData, setWebsiteData] = useState("youtube.com/@aaron");

  const MAX_Descrip_LENGTH = 500;
  const MAX_WebLink_LENGTH = 1000;

  const aboutRef = useRef(null);
  const websiteUrlRef = useRef(null);

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };
  useEffect(() => {
    autoResize(aboutRef);
  }, [aboutData, isEditing]);
  useEffect(() => {
    autoResize(websiteUrlRef);
  }, [websiteData, isEditing]);
  useEffect(() => {
    const handleResize = () => {
      autoResize(aboutRef);
      autoResize(websiteUrlRef);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const handleSave = () => {
    setAboutData(aboutData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setAboutData(aboutData);
  };
  const linkProps = !isEditing
    ? {
      Linktext: "Update",
    }
    : null;

  return (
    <CommonWhiteCard
      title="About"
      {...linkProps}
      className="account_card"
      onClick={() => setIsEditing(true)}
    >
      <div className="account_card_about">
        {isEditing ? (
          <div className="customInput_inner">
            <textarea
              ref={aboutRef}
              rows="3"
              maxLength={MAX_Descrip_LENGTH}
              className="form-control table_form_textarea w-full resize-none overflow-hidden"
              value={aboutData}
              onChange={(e) => setAboutData(e.target.value)}
            />
            <p className="character-count">
              Characters left: {aboutData.length}/{MAX_Descrip_LENGTH}
            </p>
          </div>
        ) : (
          <p className="para_desc">{aboutData}</p>
        )}
        {isEditing ? (
          <>
            <p className="para_desc mb-0">Website</p>
            <div className="customInput_inner">
              <textarea
                ref={websiteUrlRef}
                rows="1"
                maxLength={MAX_WebLink_LENGTH}
                className="form-control table_form_textarea w-full resize-none overflow-hidden"
                value={websiteData}
                onChange={(e) => setWebsiteData(e.target.value)}
              />
              <p className="character-count">
                Characters left: {websiteData.length}/{MAX_WebLink_LENGTH}
              </p>
            </div>
          </>
        ) : (
          <Link href={`${websiteData}`} legacyBehavior passHref>
            <a
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: "#00adef",
              }}
            >
              <p className="website-link">{websiteData}</p>
            </a>
          </Link>
        )}
        {isEditing && (
          <div className="account_card_list_btns mt-3">
            <button
              className="btn-style gray-btn"
              style={{ backgroundColor: "#ffffff4d" }}
              onClick={handleCancel}
            >
              Cancel
            </button>
            <button className="btn-style" onClick={handleSave}>
              Save
            </button>
          </div>
        )}
      </div>
    </CommonWhiteCard>
  );
}

/* eslint-disable react/no-unescaped-entities */
import { Container } from "react-bootstrap";
import HomeLayout from "@/Layouts/HomeLayout";
import "../../../css/Home/PrivacyPolicy.scss";
import MetaHead from "@/Seo/Meta/MetaHead";
import AcceptanceTerms from "./Partial/AcceptanceTerms";
import DescriptionServices from "./Partial/DescriptionServices";
import RegistrationAccountSecurity from "./Partial/RegistrationAccountSecurity";
import SubscriptionPlans from "./Partial/SubscriptionPlans";
import UserConduct from "./Partial/UserConduct";
import Ownership from "./Partial/Ownership";
import IntellectualProperty from "./Partial/IntellectualProperty";
import DataPrivacy from "./Partial/DataPrivacy";
import Disclaimer from "./Partial/Disclaimer";
import AttributionContent from "./Partial/AttributionContent";
import ThirdPartiesAdvertisers from "./Partial/ThirdPartiesAdvertisers";
import UserContributions from "./Partial/UserContributions";
import EducationCenter from "./Partial/EducationCenter";
import MarketplaceTransactions from "./Partial/MarketplaceTransactions";
import Indemnification from "./Partial/Indemnification";
import CommunicationNotifications from "./Partial/CommunicationNotifications";
import Modifications from "./Partial/Modifications";
import Termination from "./Partial/Termination";
import GoverningLaw from "./Partial/GoverningLaw";
import LimitationLiability from "./Partial/LimitationLiability";
import ReferFriendProgram from "./Partial/ReferFriendProgram";

const TermsService = () => {
  const metaArray = {
    title: "Terms and Conditions | TradeReply",
    description:
      "Review TradeReply.com's terms of service. Understand the rules and guidelines for using our platform, services, and accessing our trading tools.",
    canonical_link: "https://www.tradereply.com/terms",
    og_site_name: "TradeReply",
    og_title: "Terms and Conditions | TradeReply",
    og_description:
      "Read TradeReply's terms and conditions to understand the rules and guidelines for using our platform and services.",
    twitter_title: "Terms and Conditions | TradeReply",
    twitter_description:
      "Read TradeReply's terms and conditions to understand the rules and guidelines for using our platform and services.",
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <section className="commonPolicy">
        <Container>
          <h1>Terms of Service</h1>
          <AcceptanceTerms />
          <DescriptionServices />
          <RegistrationAccountSecurity />
          <SubscriptionPlans />

          {/* refered a frnd  */}
          <ReferFriendProgram />
          <UserConduct />
          <Ownership />
          <IntellectualProperty />
          <DataPrivacy />
          <Disclaimer />
          <AttributionContent />
          <ThirdPartiesAdvertisers />
          <UserContributions />
          <EducationCenter />
          <MarketplaceTransactions />
          <Indemnification />
          <CommunicationNotifications />
          <Modifications />
          <Termination />
          <GoverningLaw />
          <LimitationLiability />
        </Container>
      </section>
    </HomeLayout>
  );
};

export default TermsService;

'use client';

import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import StatusIndicator from '@/Components/UI/StatusIndicator';
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/Security.scss";
import "@/css/account/AccountDetails.scss";
import { Formik, Field, Form } from "formik";
import { changePasswordSchema } from "@/validations/schema";
import PasswordValidation from "@/Components/common/Auth/PasswordValidation";
import TextInput from "@/Components/UI/TextInput";
import InputError from "@/Components/UI/InputError";
import { CheckIcon } from "@/assets/svgIcons/SvgIcon";
import { changePassword } from "@/utils/apiUtils";
import { useRouter } from "next/navigation";

const initialValues = {
    current_password: "",
    new_password: "",
    confirm_new_password: ""
};
const metaArray = {
    noindex: true,
    title: "Account Security | Protect Your Account | TradeReply",
    description: "Secure your TradeReply.com account. Update your password, enable two-factor authentication, and monitor recent security activity.",
    canonical_link: "https://www.tradereply.com/account/security",
    og_site_name: "TradeReply",
    og_title: "Security Settings | Protect Your Account | TradeReply",
    og_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
    twitter_title: "Security Settings | Protect Your Account | TradeReply",
    twitter_description: "Enhance your account security on TradeReply. Update your password, enable two-factor authentication, and review security logs.",
};
export default function updatePassword() {
    const [showPasswordCheck, setShowPasswordCheck] = useState(false);
    const [password, setPassword] = useState("");
    const [isFocused, setIsFocused] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState('default'); // default, loading, success, error
    const [error, setError] = useState(null);
    const router = useRouter();

    const handleSubmit = async (values, { setSubmitting, setFieldError, resetForm }) => {
        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);

            const response = await changePassword({
                current_password: values.current_password,
                new_password: values.new_password,
                confirm_new_password: values.confirm_new_password
            });

            // Check if response indicates success
            if (response.success === false) {
                // Handle backend error response
                throw new Error(response.message || 'Failed to update password');
            } else {
                setSaveStatus('success');
                resetForm();
                setPassword("");
                setShowPasswordCheck(false);

                // Redirect to security page after successful password change
                setTimeout(() => {
                    router.push('/account/security');
                }, 1000);
            }

        } catch (error) {
            setSaveStatus('error');

            // Handle different types of errors
            if (error.response?.status === 422) {
                const responseData = error.response.data;
                const errors = responseData.errors;
                const message = responseData.message;

                // Check if errors is an object with field-specific errors
                if (errors && typeof errors === 'object' && !Array.isArray(errors) && Object.keys(errors).length > 0) {
                    // Set field-specific errors
                    Object.keys(errors).forEach(field => {
                        const fieldError = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
                        setFieldError(field, fieldError);
                    });
                } else {
                    // Show general error message
                    const errorMessage = message || 'Validation failed';
                    setError(errorMessage);
                }
            } else {
                // Handle other errors (network, 500, etc.)
                const errorMessage = error.response?.data?.message || error.message || 'Failed to update password. Please try again.';
                setError(errorMessage);
            }
        } finally {
            setIsLoading(false);
            setSubmitting(false);

            // Reset status after delay
            setTimeout(() => {
                setSaveStatus('default');
            }, 3000);
        }
    };

    const handleCancel = () => {
        router.push('/account/security');
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <SidebarHeading title="Update Password" />
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <div className="account_header_main">
                                <h6>Password</h6>
                                <div className="account_status_indicator">
                                    <StatusIndicator
                                        saveStatus={saveStatus}
                                        error={error}
                                        successText="Saved"
                                        defaultText="Not saved"
                                    />
                                </div>
                            </div>
                            <p>We recommend updating password periodically to prevent unauthorized access.</p>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <label className='mb-2'>Password</label>
                        <div className='col-lg-6 col-11'>
                            {error && (
                                <div className="alert alert-danger mb-3" role="alert">
                                    {error}
                                </div>
                            )}
                            <Formik
                                initialValues={initialValues}
                                validationSchema={changePasswordSchema}
                                onSubmit={handleSubmit}
                            >
                                {({
                                    errors,
                                    touched,
                                    submitCount,
                                    values,
                                    dirty,
                                    isSubmitting
                                }) => (
                                    <Form id="password-change-form">
                                        <div className="authCorrectIcon">
                                            <Field name="current_password">
                                                {({ field }) => (
                                                    <TextInput
                                                        {...field}
                                                        placeholder="Enter your current password"
                                                        type="password"
                                                        error={
                                                            (submitCount > 0 || touched.current_password) && errors.current_password ? (
                                                                <InputError message={errors.current_password} />
                                                            ) : null
                                                        }
                                                        isError={(submitCount > 0 || touched.current_password) && errors.current_password ? true : false}
                                                    />
                                                )}
                                            </Field>
                                        </div>
                                        <div className="authCorrectIcon">
                                            <Field name="new_password">
                                                {({ field, form: { errors } }) => (
                                                    <TextInput
                                                        {...field}
                                                        placeholder="Create a new password"
                                                        type="password"
                                                        maxLength={64}
                                                        onChange={(e) => {
                                                            const cleanedValue = e.target.value.replace(/\s/g, "");
                                                            field.onChange({ target: { name: e.target.name, value: cleanedValue } });
                                                            setPassword(cleanedValue);
                                                            setShowPasswordCheck(true);
                                                            setIsFocused(false);
                                                        }}
                                                        onBlur={(e) => {
                                                            field.onBlur(e);
                                                            if (e.target.value.replace(/\s/g, "").length === 0) {
                                                                setShowPasswordCheck(false);
                                                                setIsFocused(false);
                                                            }
                                                        }}
                                                        onFocus={() => {
                                                            setShowPasswordCheck(true);
                                                        }}
                                                        error={
                                                            submitCount > 0 && errors.new_password ? <InputError message={errors.new_password} /> : null
                                                        }
                                                        isError={submitCount > 0 && errors.new_password ? true : false}
                                                    />
                                                )}
                                            </Field>
                                            {showPasswordCheck && !(submitCount > 0 && errors.new_password) && (
                                                <PasswordValidation
                                                    isValid={touched.new_password && !errors.new_password}
                                                    password={password}
                                                />
                                            )}
                                            <div className="checkIcon">
                                                {values.new_password && !errors.new_password && dirty && <CheckIcon width="25" height="25" />}
                                            </div>
                                        </div>
                                        <div className="authCorrectIcon">
                                            <Field name="confirm_new_password">
                                                {({ field }) => (
                                                    <TextInput
                                                        {...field}
                                                        placeholder="Re-enter your new password"
                                                        type="password"
                                                        maxLength={64}
                                                        error={
                                                            values.confirm_new_password && errors.confirm_new_password ? (
                                                                <InputError message={errors.confirm_new_password} />
                                                            ) : null
                                                        }
                                                        isError={values.confirm_new_password && errors.confirm_new_password ? true : false}
                                                    />
                                                )}
                                            </Field>
                                            <div className="checkIcon">
                                                {values.confirm_new_password && !errors.confirm_new_password && dirty && <CheckIcon width="25" height="25" />}
                                            </div>
                                        </div>
                                    </Form>
                                )}
                            </Formik>
                        </div>
                    </div>
                </div>
                <div className="account_card_list_btns mt-3">
                    <button
                        type="button"
                        className="btn-style white-btn"
                        onClick={handleCancel}
                        disabled={isLoading}
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        form="password-change-form"
                        className="btn-style"
                        disabled={isLoading}
                    >
                        {isLoading ? 'Updating...' : 'Update Password'}
                    </button>
                </div>
            </AccountLayout>
        </>
    )
}

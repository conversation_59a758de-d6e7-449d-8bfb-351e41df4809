@use "../theme/_var.scss" as *;

.marketplace {
    padding: 5rem 0;

    &_inner {
        &_heading {
            border-bottom: 3px solid rgba(0, 173, 239, 0.3);
            margin-bottom: 30px;
            padding-bottom: 1.25rem;


            h4 {
                margin-bottom: 1.25rem;
                font-size: 1.65rem;
                line-height: 35px;
                font-weight: 600;
            }

            .breadcrumb {
                margin: 0;
            }
        }
    }

    &_heading {
        h1 {
            font-size: 3rem;
            font-weight: 800;

            @media (max-width: 1199px) {
                font-size: 2.5rem;
            }

            @media (max-width: 767px) {
                font-size: 1.5rem;
            }

            @media (max-width: 390px) {
                font-size: 1.30rem;
            }
        }
    }

    &_shopcart {
        margin: 30px 0;

        @media screen and (max-width: 767px) {
            flex-wrap: wrap;
        }

        .education_search {
            margin: 0 50px;
            width: 450px;

            @media screen and (max-width: 991px) {
                width: 350px;
                margin: 0 30px;
            }

            @media screen and (max-width: 767px) {
                width: 100%;
                margin: 0;
                padding-top: 1.25rem;
            }

            .commonSearch {
                max-width: 100%;
                width: 100%;
            }
        }

        &_btn {
            button {
                background-color: transparent;
                border: 0;
                font-weight: 600;
                font-size: 1.25rem;
                color: $white;
                transition: all ease-in-out 0.3s;

                svg {
                    margin-right: 0.625rem;
                }

                &:hover {
                    background-color: transparent;
                    color: $baseclr;
                }
            }
        }
    }

    .common_select {
        margin-bottom: 0;

        .select__control {
            padding: 0;
            border: 0;
            min-width: 80px;
            min-height: auto;
        }
    }

    &_products {
        &_sellerInfo {
            margin: 20px 0;
            border-top: 3px solid #00adef4d;

            @media (width <=767px) {
                border-top: none;
            }

            h4 {
                margin: 20px 0;
            }

            .sellerProfile {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 20px;

                img {
                    min-height: 50px;
                    max-height: 50px;
                    min-width: 50px;
                    max-width: 50px;
                    border-radius: 50px;
                }

                p {
                    font-size: 22px;
                    font-weight: 600;
                }
            }

            .sellerRating {
                display: flex;
                align-items: center;
                gap: 5px;
                margin-bottom: 20px;

                img {
                    height: 25px;
                }

                span {
                    font-size: 24px;
                    font-weight: 600;
                }
            }

            .sellerBtn {
                button {
                    width: 100%;
                    min-height: 50px;
                }
            }
        }

        &_filter {
            .accordion {
                border-radius: 0;

                &-item {
                    background-color: transparent;
                    border: 0;
                    border-bottom: 3px solid rgba(0, 173, 239, 0.2);
                    border-radius: 0;
                }

                &-button {
                    background-color: transparent;
                    border: 0;
                    color: $white;
                    font-weight: 600;
                    font-size: 1.25rem;
                    padding: 1.5rem 0;
                    text-transform: capitalize;
                    border-radius: 0;

                    @media screen and (max-width: 991px) {
                        font-size: 1rem;
                        padding: 1rem 0;
                    }

                    &:focus {
                        box-shadow: none;
                    }

                    &:not(.collapsed) {
                        box-shadow: none;
                        background-color: transparent;
                        color: $white;

                        &::after {
                            background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-minus.svg");
                            transform: none;
                        }
                    }

                    &::after {
                        background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-plus.svg");
                        transform: none;
                        background-position: center;
                    }
                }

                &-body {
                    padding: 1rem 0 1.25rem;

                    button {
                        background-color: transparent;
                        border: 0;
                        display: block;
                        color: $white;
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 26px;
                        padding: 6px 0;
                        width: 100%;
                        transition: all ease-in-out 0.3s;
                        text-align: left;

                        &:hover {
                            color: $baseclr;
                        }
                    }
                }
            }
        }

        &_sort {
            @media screen and (max-width: 991px) {
                justify-content: flex-end;
            }

            h5 {
                font-weight: 500;
                margin-right: 5px;
            }

            .common_select {

                .select__control {
                    min-width: 150px;
                }
            }
        }

        &_card {
            margin-top: 30px;

            &_rating {
                display: flex;
                align-items: center;
                gap: 5px;
                margin-bottom: 20px;

                img {
                    height: 25px;
                }

                span {
                    font-size: 24px;
                    font-weight: 600;
                    color: #fff;
                }
            }

            &_content {
                h4 {
                    @media screen and (max-width: 991px) {
                        font-size: 20px;
                        line-height: 30px;
                    }

                    @media screen and (max-width: 767px) {
                        font-size: 16px;
                        line-height: 24px;
                    }
                }
            }

            &_img {
                position: relative;

                &::after {
                    content: '';
                    // position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    padding-top: 90%;
                    display: block;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    position: absolute;
                    border-radius: 20px;
                }
            }
        }
    }

    .custom_breadcrumb {
        .home-item {
            display: none;
        }

        .secondary_link {
            padding: 0;

            &::before {
                display: none;
            }
        }
    }


    .marketplace_products_card_rating {
        img {
            height: 25px;
            width: 25px;

            @media screen and (max-width: 576px) {
                height: 18px;
                width: 18px;
            }
        }

        span {
            font-size: 16px;

            @media screen and (max-width: 576px) {
                font-size: 14px;
            }
        }
    }


    .megaMenu {
        position: relative;
        // z-index: 9999;

        &__toggle {
            padding: 0.5rem 1rem;
            color: #fff;
            font-family: 'Gilroy-SemiBold';
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: none;
            border: none;
        }

        &__dropdown {
            position: absolute;
            display: flex;
            top: 60px;
            left: -50px;
            width: 300px;
            max-width: 1059px;
            background: #fff;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0px 4px 10px rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            margin-top: 0.5rem;
            overflow: hidden;
            transition: width 0.3s ease;
            z-index: 9999;

            @media (min-width: 728px) {
                &.expanded {
                    width: 1059px;
                }
            }
        }

        &__categories {
            width: 300px;
            padding: 0;
            border-right: 1px solid #d9d9d9;
        }

        &__category {
            margin: 0;
            padding: 0;
            width: 300px;
            cursor: pointer;

            &.active,
            &:hover {
                background-color: #e6e6e6 !important;
            }
        }

        &__category-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #000;
            font-weight: 600;
            font-size: 20px;
            font-family: 'Gilroy';
            width: 100%;
            height: 100%;
        }

        &__icon {
            width: 1rem;
            height: 1rem;
        }

        &__subcategories {
            padding: 1.5rem;
            width: 100%;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;

            &.visible {
                opacity: 1;
                pointer-events: auto;
            }
        }

        &__columns {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }

        &__column {
            min-width: 220px;
        }

        &__subtitle {
            color: #000;
            font-family: 'Gilroy';
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 0.5rem;
        }

        &__items {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        &__link {
            display: block;
            padding: 0.3rem 0;
            font-size: 16px;
            color: #000000cc;
            font-weight: 500;
            white-space: normal; // <- allow wrapping
            overflow-wrap: break-word; // optional for aggressive breaking

            &:hover {
                text-decoration: underline;
            }
        }

        @media (max-width: 1050px) and (min-width: 728px) {
            &__dropdown {
                flex-direction: row !important;
                position: absolute;
                top: 60px;
                left: -30px;
                width: 247px;
                max-width: 750px;
                height: auto;
                border-radius: 15px !important;
                overflow: hidden;
            }

            &__categories {
                width: 100% !important;
                border-right: 1px solid #d9d9d9;
            }

            &__category {
                width: 247px !important;
            }

            &__subcategories {
                width: 100% !important;
                padding: 1rem !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }

            &__columns {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }

        @media (max-width: 727px) {
            &__dropdown {
                flex-direction: column;
                position: fixed;
                top: 72px;
                left: 0;
                right: 0;
                min-height: calc(100vh - 60px);
                max-height: calc(100vh - 60px);
                width: 100%;
                background: #FFFFFFE5;
                border-radius: 0;
                backdrop-filter: blur(10px);
                overflow-y: auto;
                max-height: 100vh;
                -webkit-overflow-scrolling: touch;
                z-index: 9999;
            }

            &__category {

                width: 100%;

            }


            &__mobile-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 1rem;
                border-bottom: 1px solid #ccc;
                color: #000;
                font-weight: 700;
                font-size: 20px;
                font-family: 'Gilroy';
                //   background-color: #f8f8f8;
            }

            &__back,
            &__close {
                background: none;
                border: none;
                font-size: 22px;
                font-weight: bold;
                cursor: pointer;
                margin-right: 8px;
            }

            &__logo {
                color: #000;
                font-weight: 700;
                font-size: 20px;
                font-family: 'Gilroy';
                margin-left: 8px;
            }

            &__categories,
            &__subcategories {
                width: 100%;
                padding: 1rem;

                @media (max-width:550px) {
                    max-height: calc(100vh - 180px);
                    max-height: calc(100dvh - 180px);
                    overflow-y: auto;
                    -webkit-overflow-scrolling: touch;
                }
            }

            .megaMenu__link {
                font-size: 15px;
                padding-top: 8px !important;
                padding-bottom: 8px !important;

                white-space: normal !important; // ✅ allows line wrapping
                word-break: break-word !important; // ✅ breaks long words
                overflow-wrap: break-word !important; // ✅ breaks phrases or long lines
                display: block !important; // ✅ necessary for width-based wrapping
                width: 100% !important; // ✅ prevent overflow
                line-height: 1.4; // ✅ optional: improve readability when wrapping
            }

            &__columns {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        @media (max-width: 388px) {
            &__dropdown {
                flex-direction: column;
                position: fixed;
                top: 11%;
                left: 0;
                right: 0;
                height: 89vh;
                width: 100%;
                background: #FFFFFFE5;
                border-radius: 0;
                backdrop-filter: blur(10px);
                overflow-y: auto;
                max-height: 100vh;
                -webkit-overflow-scrolling: touch;
                z-index: 9999;
            }

            .megaMenu__column {
                max-width: 310px !important;
            }

            .megaMenu__link {
                max-width: 100% !important;
            }

        }

    }




}
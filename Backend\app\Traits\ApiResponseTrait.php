<?php

namespace App\Traits;

trait ApiResponseTrait
{
    protected function successResponse($data = [], $message = 'Success', $statusCode = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
        ], $statusCode);
    }

    protected function errorResponse($message = 'Something went wrong', $statusCode = 400, $errors = [], $conditionalData = [])
    {
        $response = [
            'success' => false,
            'message' => $message,
            'errors' => $errors,
        ];

        // Merge optional extra data (like captcha or token_expired)
        if (!empty($conditionalData)) {
            $response = array_merge($response, $conditionalData);
        }

        return response()->json($response, $statusCode);
    }


}

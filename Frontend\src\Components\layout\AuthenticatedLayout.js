'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import useAuthHeartbeat from '@/Hooks/useAuthHeartbeat';

/**
 * Layout component for authenticated pages
 * Automatically monitors authentication status and handles logout
 */
export default function AuthenticatedLayout({ children }) {
  const router = useRouter();
  const { startHeartbeat, stopHeartbeat } = useAuthHeartbeat();

  useEffect(() => {
    // Check if user is authenticated
    const token = Cookies.get("authToken");
    
    if (!token) {
      // No token, redirect to login
      router.replace('/login');
      return;
    }

    // Start heartbeat monitoring for authenticated users
    startHeartbeat();

    // Cleanup on unmount
    return () => {
      stopHeartbeat();
    };
  }, [router, startHeartbeat, stopHeartbeat]);

  return (
    <>
      {children}
    </>
  );
}

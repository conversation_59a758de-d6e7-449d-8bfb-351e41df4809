@use "../theme/var";

.education_detail {
  @media (max-width: 991px) {
    padding-top: 40px !important;
  }

  &_tag {
    padding: 6px 20px;
    background-color: var.$baseclr;
    border-radius: 10px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
    letter-spacing: -0.10000000149011612px;
    text-transform: uppercase;
    color: var.$white;
    border: 0;
  }

  &_heading {
    h1 {
      font-size: 2.8rem;
      font-weight: 600;
      color: var.$white;
      padding: 30px 0;

      @media (max-width: 1199px) {
        font-size: 2.5rem;
      }

      @media (max-width: 767px) {
        font-size: 1.5rem;
      }

      @media (max-width: 390px) {
        font-size: 1.30rem;
      }
    }

    h5 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var.$white;
      padding-top: 30px;
    }
  }

  &_postimg {
    padding: 5rem 0;

    @media (max-width: 767px) {
      padding: 0.625rem 0 2rem;
    }

    img {
      border-radius: 60px;

      @media (max-width: 767px) {
        border-radius: 30px;
      }
    }
  }

  &_text {
    h2 {
      font-size: 4rem;
      overflow-wrap: break-word;

      @media (max-width: 1269px) {
        font-size: 3rem !important;
      }

      @media (max-width: 991px) {
        font-size: 3rem !important;
      }

      @media (max-width: 767px) {
        font-size: 2.5rem !important;
      }
    }

    p {
      font-size: 1.5rem;
      font-weight: 400;
      line-height: 36px;
      letter-spacing: -0.10000000149011612px;
      color: var.$white;
      padding-top: 20px;
      overflow-wrap: break-word;


      @media (max-width: 767px) {
        font-size: 15px;
        line-height: 23px;
        padding-top: 0;
      }
    }
  }

  &_author {
    padding-top: 5rem;

    @media (max-width: 767px) {
      padding-top: 3rem;
    }

    &_btn {
      background-color: transparent;
      border: 0;
      color: var.$baseclr;
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 24.5px;
      letter-spacing: -0.10000000149011612px;
      margin-bottom: 60px;

      @media (max-width: 767px) {
        font-size: 1rem;
        line-height: 1.25rem;
        margin-bottom: 30px;
      }
    }
  }

  .recent_post {
    background-color: transparent;
    border-radius: 0;
    border: 0;
    margin-bottom: 0;
    padding: 30px 0;
    border-top: 1px solid var.$borderclr;
    border-bottom: 1px solid var.$borderclr;
  }

  &_sidebar {
    position: relative;

    @media screen and (min-width: 992px) {
      position: sticky;
      top: 100px;
    }

    .collapse_btn {
      position: fixed;
      background-color: var.$baseclr;
      width: 40px;
      height: 40px;
      align-items: center;
      justify-content: center;
      right: 0;
      border-radius: 15px 0px 0px 15px;
      top: 90px;
      z-index: 99;
      display: none;

      @media screen and (max-width: 991px) {
        display: flex;
      }

      @media screen and (max-width: 767px) {
        top: 70px;
      }
    }

    .btn-style {
      @media screen and (max-width: 1599px) {
        font-size: 1rem;
      }
    }

    &_collapse {
      position: relative;

      @media screen and (max-width: 991px) {
        margin-top: 0;
        margin-top: 0;
        position: fixed;
        width: 100%;
        right: -100%;
        transition: all ease-in-out 0.2s;
        z-index: 98;
        top: 72px;
      }

      @media screen and (max-width: 767px) {
        top: 56px;
      }

      .collapse_wrap {
        @media screen and (max-width: 991px) {
          background-color: var.$clr031940;
          border-radius: 20px;
          padding: 70px 20px 20px;
          height: calc(100vh - 36px);
        }
      }

      &.active {
        @media screen and (max-width: 991px) {
          right: 0;
        }

        .collapse_wrap {
          display: block;
        }
      }
    }

    &_top {
      @media screen and (max-width: 991px) {
        display: flex;

        .btn-style {
          width: 50% !important;
          margin-right: 10px;
        }

        .education_search {
          width: 50%;
          margin-top: 0 !important;
          margin-left: 10px;
        }
      }

      @media screen and (max-width: 767px) {
        flex-direction: column;
        align-items: center;

        .btn-style {
          width: 400px !important;
          margin-right: 0;
          margin-bottom: 10px;
        }

        .education_search {
          width: 400px;
          margin-top: 10px !important;
          margin-left: 0;
        }
      }

      @media screen and (max-width: 575px) {
        display: block;

        .btn-style {
          width: 100% !important;
        }

        .education_search {
          width: 100%;
        }
      }
    }

    &_profit {
      @media (min-width: 992px) {
        max-height: calc(100vh - 480px);
        overflow-y: auto;
        overflow-x: clip;
        padding-right: 5px;
      }

      // @media  (min-width: 768px) and (max-width: 991px) {
      //     display: flex;
      //     flex-wrap: nowrap;
      //     white-space: nowrap;
      //     overflow-x: auto;
      // }

      @media screen and (max-width: 991px) {
        display: flex;
        overflow: hidden;
        position: relative;
        max-width: 90%;
        width: 100%;
        margin: 0 auto;
      }

      @media screen and (max-width: 767px) {
        max-width: 240px;
      }

      &_inner {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding: 10px 0;

        @media screen and (max-width: 991px) {
          min-width: 300px;
          margin-right: 30px;
        }

        @media screen and (max-width: 767px) {
          min-width: 100%;
          margin-right: 0px;
          border-bottom: 0;
        }

        &_detail {
          cursor: pointer;
        }
      }

      &_img {
        width: 130px;

        @media screen and (max-width: 767px) {
          width: 100px;
        }

        img {
          border-radius: 20px;
          height: 80px;
          object-fit: cover;

          @media screen and (max-width: 767px) {
            height: 60px;
          }
        }
      }

      &_text {
        width: calc(100% - 130px);
        padding-left: 5px;

        @media screen and (max-width: 767px) {
          width: calc(100% - 100px);
        }

        h6 {
          font-size: 1rem;
          font-weight: 600;
          color: var.$white;
          padding-bottom: 10px;

          @media screen and (max-width: 767px) {
            padding-bottom: 6px;
          }
        }

        p {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          text-align: left;
          color: var.$textclr;
          word-wrap: break-word;
          white-space: normal;

          @media screen and (max-width: 767px) {
            font-size: 14px;
            ;
            line-height: 15px;
          }
        }
      }

      &_progressbar {
        margin-top: 10px;

        .progress {
          height: 7px;
          background-color: rgba(255, 255, 255, 0.2);

          .progress-bar {
            background-color: var.$baseclr;
          }
        }
      }
    }

    &_article {
      margin: 20px 0 0;
      text-align: center;

      @media screen and (max-width: 991px) {
        margin: 40px 0 0;
      }

      &_data {
        h6 {
          padding-bottom: 10px;
        }
      }
    }
  }

  .scroll-btn {
    background-color: var.$baseclr;
    color: white;
    border: none;
    padding: 0;
    cursor: pointer;
    font-size: 1.2rem;
    min-width: 30px;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rem;
    position: absolute;
    display: none;
    top: 50%;
    transform: translateY(-50%);

    @media (max-width: 991px) {
      display: flex;
    }

    &.left {
      left: -10px;

      svg {
        transform: rotate(180deg);
      }
    }

    &.right {
      right: -10px;
    }

    &:hover {
      background-color: var.$baseclr;
    }

    &.disabled,
    &:disabled {
      background-color: #414c60;
    }
  }

  .commonSearch {
    @media screen and (max-width: 991px) {
      max-width: 100%;

      // .form-control {
      //     border-radius: 10rem;
      // }
    }
  }
}

.CircularProgressbar {
  .CircularProgressbar-trail {
    stroke: rgba(255, 255, 255, 0.2);
  }

  .CircularProgressbar-path {
    stroke: var.$baseclr;
  }
}

.CircularProgressbar_text {
  text-align: center;

  h6 {
    font-size: 0.875rem;
    font-weight: 600;
    fill: var.$white;
    padding-bottom: 5px;
  }
}
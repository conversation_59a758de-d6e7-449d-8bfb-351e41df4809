"use client";
import { Col, Row } from "react-bootstrap";
import React from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  TripleDotsMenu,
  RightArrowIcon,
  BlackShareIcon,
  StaticListingImg,
} from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/SoldPurchProduct.scss";

export default function SoldProducts() {
  const metaArray = {
    noindex: true,
    title: "Sold Product | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Your Sold Products" />
          <Row className="mb-4 mb-lg-4">
            <Col xs={12} className="d-flex mt-4 mt-md-0">
              <CommonWhiteCard
                title="Marketplace Products Sold"
                className="account_card"
              >
                <div className="account_card_sold">
                  <div className="main_inform justify-between">
                    <p className="most_recent">15 Products Sold</p>
                    <span className="most_recent text-end">Sorted: Most recently sold</span>
                  </div>
                  <div className="mini_card">
                    <div className="main_inform respon_sell_feedback">
                      <div className="activeListing_photo">
                        <StaticListingImg />
                      </div>
                      <div className="w-100">
                        <h6>Mastering the stock market</h6>
                        <p className="inner_price_text">
                          $11.95 - Purchased on Mar 13, 2025
                        </p>
                        <p className="inner_price_text">Seller: @aaron</p>
                        <p className="inner_price_text">Order ID: 19392</p>
                        <p className="inner_price_text">Dispute Status: Open</p>
                        <div className="actions_btn gap-2">
                          <div>
                            <button className="round-redfill-btn" type="button">
                              Respond to Dispute
                            </button>
                            <button
                              className="round-bluefill-btn"
                              type="button"
                            >
                              View Order
                            </button>
                          </div>
                          <div>
                            <button className="round-border-btn" type="button">
                              <BlackShareIcon />
                              Share
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

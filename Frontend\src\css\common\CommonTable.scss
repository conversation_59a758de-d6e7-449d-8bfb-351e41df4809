@use "../theme/var";

.tableless {
  border: 1px solid var.$baseclr;
  border-radius: 2rem;
  position: relative;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    border-radius: 2rem;
    z-index: -1;
  }

  .common_table {
    tr {

      th,
      td {
        padding: 1.6rem 1rem;
        white-space: nowrap;
        vertical-align: middle;
        border: 1px solid #34415b;

        @media (max-width: 767px) {
          padding: 1.5rem 0.5rem;
        }

        .clickIcon {
          cursor: pointer;
        }
      }

      td {
        font-weight: 500 !important;
      }
    }

    thead {
      border: none;

      tr {
        position: sticky;
        top: 0;

        th {
          background-color: var.$black;
          font-size: 1.25rem;
          font-weight: 600;
          color: var.$white;
          border-radius: 0;
          border-top: 0;

          @media (max-width: 1199px) {
            font-size: 1rem;
          }

          @media (max-width: 767px) {
            font-size: 0.875rem;
          }

          &:first-child {
            border-top-left-radius: 2rem;
            border-left: 0;
          }

          &:last-child {
            border-top-right-radius: 2rem;
            border-right: 0;
          }
        }
      }
    }

    tbody {
      tr {
        &:last-child {
          border-bottom: 0;

          td {
            border-bottom: 0;
          }
        }
      }

      td {
        background: transparent;
        font-size: 1.125rem;
        font-weight: 600;
        color: var.$clre9e9e9;

        @media (max-width: 1199px) {
          font-size: 1rem;
        }

        @media (max-width: 767px) {
          font-size: 0.875rem;
        }

        a {
          color: var.$baseclr;
        }

        &:first-child {
          border-left: 0;
        }

        &:last-child {
          border-right: 0;
        }
      }

      tr {
        &.no_record {
          td {
            padding: 1rem 0;
          }
        }
      }
    }
  }
}

.no_record_box {
  padding: 3.125rem 1rem;
  text-align: center;

  svg {
    opacity: 1;

    path {
      fill: var.$white;
    }
  }

  img {
    width: 150px !important;
  }

  h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 0.625rem;
    color: var.$white;

    @media (max-width: 1679px) {
      font-size: 0.875rem;
    }
  }

  @media (max-width: 991px) {
    padding: 2.5rem 1.25rem;
  }
}
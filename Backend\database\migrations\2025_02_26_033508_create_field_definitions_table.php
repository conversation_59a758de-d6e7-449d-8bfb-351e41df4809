<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('field_definitions', function (Blueprint $table) {
            $table->id();
            $table->string('field_name');
            $table->string('database_field');
            $table->text('summary');
            $table->string('datatype');
            $table->string('expected_values')->nullable();
            $table->boolean('has_formula');
            $table->enum('metric_dimension', ['metric', 'dimension']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('field_definitions');
    }
};

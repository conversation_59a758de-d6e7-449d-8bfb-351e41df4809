<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Drop foreign key manually using raw SQL
        try {
            DB::statement("ALTER TABLE `user_subscriptions` DROP FOREIGN KEY `user_subscriptions_subscription_id_foreign`");
        } catch (\Exception $e) {
            // Ignore if FK doesn't exist
            info('Foreign key not found or already dropped: ' . $e->getMessage());
        }

        Schema::table('user_subscriptions', function (Blueprint $table) {
            // Drop the column if it still exists
            if (Schema::hasColumn('user_subscriptions', 'subscription_id')) {
                $table->dropColumn('subscription_id');
            }

            // Convert stripe_subscription_id to nullable VARCHAR(255)
            $table->string('stripe_subscription_id', 255)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

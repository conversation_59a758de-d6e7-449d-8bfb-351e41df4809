<?php

namespace App\Models;

use App\Traits\CsvImporterTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Enums\ArticleType;
use Illuminate\Support\Facades\Cache;

class Article extends Model
{
    use HasFactory;
    use CsvImporterTrait;
    public const EDUCATION_PATH = '/education/featured/';
    public const BLOG_PATH = '/blog/featured/';

    protected $table = 'articles';


    protected $fillable = [
        'title',
        'content',
        'slug',
        'type',
        'feature_image',
        'primary_category_id',
        'is_featured',
        'summary',
        'body',
        'keywords'
    ];

    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Find Records based on SLUG-TYPE(OF ARTICLE)
     */
    public static function getTypeSlug($slug, $withCategories = false)
    {
        $query = self::where('slug', $slug);

        if ($withCategories) {
            $query->with('primaryCategory');
        }

        return $query->first();
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($article) {
            if ($article->title) {
                $article->slug = self::generateUniqueSlug($article->title, $article->type);
            }
        });

        static::updating(function ($article) {
            if ($article->isDirty('title') || $article->isDirty('type')) {
                $article->slug = self::generateUniqueSlug($article->title, $article->type, $article->id);
            }
        });
    }



    /**
     * Relationship: Belongs to Primary Category
     */
    public function primaryCategory()
    {
        return $this->belongsTo(Category::class, 'primary_category_id');
    }

    /**
     * Relationship: Many-to-Many with Secondary Categories
     */
    public function secondaryCategories()
    {
        return $this->belongsToMany(Category::class, 'article_secondary_categories', 'article_id', 'category_id')
            ->withTimestamps();
    }

    /**
     * Scope: Filter by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope: Filter Articles by Category
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('primary_category_id', $categoryId)
            ->orWhereHas('secondaryCategories', function ($q) use ($categoryId) {
                $q->where('categories.id', $categoryId);
            });
    }


    /**
     * Generate a unique slug for a given title and type.
     *
     * @param string $title The title to generate a slug from.
     * @param string $type The type/category of the entity (e.g., 'blog', 'education').
     * @param int|null $id Optional ID to exclude when checking for uniqueness (useful for updates).
     * @return string A unique slug with the format "original-title-type" or "original-title-count-type".
     */
    public static function generateUniqueSlug($title, $type, $id = null)
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug . '-' . $type;
        $count = 1;

        // Keep generating new slugs until we find a unique one for this title+type
        while (
            self::where('slug', $slug)
                ->where('type', $type)
                ->when($id, fn ($query) => $query->where('id', '!=', $id))
                ->exists()
        ) {
            $slug = $baseSlug . '-' . $count++ . '-' . $type;
        }

        return $slug;
    }





    /**
     * Modify the model's array representation to return a clean slug without the type postfix.
     *
     */
    public function toArray()
    {
        return array_merge(parent::toArray(), [
            'slug' => $this->getCleanSlug(),
        ]);
    }

    /**
     * Remove the type postfix from the slug.
     */
    public function getCleanSlug()
    {
        return preg_replace('/-\b' . preg_quote($this->type, '/') . '$/', '', $this->slug);
    }

    /**
     * Convert is_featured to boolean storage.
     */
    public function setIsFeaturedAttribute($value)
    {
        $this->attributes['is_featured'] = (bool) $value;
    }

    /**
     * Get the correct storage path based on type.
     */
    public static function getStoragePath($type, $filename)
    {
        $storagePath = match ($type) {
            ArticleType::EDUCATION->value =>  self::EDUCATION_PATH,
            ArticleType::BLOG->value =>  self::BLOG_PATH,
        };

        return $storagePath . $filename;
    }

    /**
     * Accessor for feature image URL.
     */
    public function getFeatureImageUrlAttribute()
    {
        return $this->feature_image
            ? config('filesystems.disks.s3.url') . self::getStoragePath($this->type, $this->feature_image)
            : null;
    }


    /**
     * Fetch the next article within the same type.
     */
    public function getNextArticle()
    {

        $query = self::with('primaryCategory:id,title')
            ->where('type', $this->type)
            ->where('id', '<>', $this->id);

        if ($this->type === 'education') {
            $isNumericTitle = preg_match('/^[0-9]/', $this->title) ? 2 : 1;

            $nextArticle = (clone $query = $query ?? $nextArticle ?? null)
                ->where(function ($query) use ($isNumericTitle) {
                    $query->whereRaw(
                        "(CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END) > ?
                        OR ((CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END) = ? AND title > ?)",
                        [$isNumericTitle, $isNumericTitle, $this->title]
                    );
                })
                ->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END ASC")
                ->orderBy('title', 'ASC')
                ->first();

            return $nextArticle ?? self::with('primaryCategory:id,title')
                ->where('type', $this->type)
                ->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END ASC")
                ->orderBy('title', 'ASC')
                ->first();
        }

        return self::with('primaryCategory:id,title')
            ->where('type', $this->type)
            ->where('created_at', '<', $this->created_at)
            ->orderBy('created_at', 'desc')
            ->first()
            ?? self::with('primaryCategory:id,title')
                ->where('type', $this->type)
                ->orderBy('created_at', 'desc')
                ->first();
    }

    /**
     * Admin Dashboard Listings based on Type.
     */
    public function AdminList($type)
    {
        $query = self::with(['primaryCategory:id,title'])
            ->ofType($type)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        if ($type === 'education') {
            $query->map(function ($article) {
                $baseField = str_replace('-', '_', preg_replace('/-education$/', '', $article->slug));

                $field = FieldDefinition::where('database_field', $baseField)->first();

                if($field) {
                    $article->transaction_summary = TransactionFieldDefinition::where('field_definition_id', $field->id)->value('summary');
                    $article->trade_summary       = TradeFieldDefinition::where('field_definition_id', $field->id)->value('summary');
                    $article->portfolio_summary   = PortfolioFieldDefinition::where('field_definition_id', $field->id)->value('summary');
                }

                return $article;
            });
        }

        return $query;
    }

    /**
     * Create a new article and attach secondary categories.
     */
    public static function createArticle(array $data)
    {
        $article = self::create($data);

        // Attach secondary categories if provided
        if (!empty($data['secondary_categories']) && is_array($data['secondary_categories'])) {
            $article->secondaryCategories()->sync($data['secondary_categories']);
        }

        return $article->refresh();
    }

    /**
     * Update an existing article and its secondary categories.
     */
    public function updateArticle(array $data)
    {
        // Update article fields
        $this->update($data);

        // Sync secondary categories if provided
        if (isset($data['secondary_categories']) && is_array($data['secondary_categories'])) {
            $this->secondaryCategories()->sync($data['secondary_categories']);
        }

        return $this->refresh();
    }

    /**
     * Delete an existing article and its secondary categories.
     */
    public function deleteArticle()
    {
        try {

            $this->secondaryCategories()->detach();

            return $this->delete();
        } catch (\Exception $e) {
            \Log::error("Error deleting article ID {$this->id}: " . $e->getMessage());
            return false;
        }
    }


    /**
     * Fetch paginated data with optional search.
     */
    public static function getPaginatedData(
        $type,
        $perPage = 10,
        $searchKey = null,
        $currentPage = 1,
        $sortByClicks = false,
        $canShowNextArticlesSorting = null,
        $currentArticleId = null
    ) {
        $query = self::with('primaryCategory:id,title', 'secondaryCategories:id,title');

        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($canShowNextArticlesSorting === 'true' && $type === 'education' && !empty($currentArticleId)) {
            $currentArticle = self::find($currentArticleId);
            $isNumericTitle = preg_match('/^[0-9]/', $currentArticle->title) ? 2 : 1;
            $query->where(function ($q) use ($isNumericTitle, $currentArticle) {
                $q->whereRaw("
                      (CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END) > ?
                      OR (
                          (CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END) = ?
                          AND title > ?
                      )
                  ", [$isNumericTitle, $isNumericTitle, $currentArticle->title]);
            });

            $query->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END ASC");
            $query->orderBy('title', 'ASC');
        }

        if (!is_null($searchKey) && $searchKey !== '') {
            $query->where(function ($q) use ($searchKey) {

                $q->where('title', '=', "{$searchKey}")

                ->orWhere('title', 'LIKE', "{$searchKey}%")
                ->orWhere('title', 'LIKE', "%{$searchKey}%")

                ->orWhereHas('primaryCategory', function ($subQuery) use ($searchKey) {
                    $subQuery->where('title', 'LIKE', "%{$searchKey}%");
                })
                ->orWhereHas('secondaryCategories', function ($subQuery) use ($searchKey) {
                    $subQuery->where('title', 'LIKE', "%{$searchKey}%");
                })

                ->orWhere('summary', 'LIKE', "%{$searchKey}%");
            });

            $query->orderByRaw("
                CASE
                    WHEN articles.title = ? THEN 1   -- Exact match in title (Highest)
                    WHEN articles.title LIKE ? THEN 2  -- Starts with searchKey
                    WHEN articles.title LIKE ? THEN 3  -- Contains searchKey
                    WHEN EXISTS (
                        SELECT 1 FROM categories
                        WHERE articles.primary_category_id = categories.id
                        AND categories.title LIKE ?
                    ) THEN 4  -- Primary category match
                    WHEN EXISTS (
                        SELECT 1 FROM categories
                        INNER JOIN article_secondary_categories
                            ON categories.id = article_secondary_categories.category_id
                        WHERE article_secondary_categories.article_id = articles.id
                        AND categories.title LIKE ?
                    ) THEN 5  -- Secondary category match
                    WHEN articles.summary LIKE ? THEN 6  -- Summary match (Lowest priority)
                    ELSE 7
                END
            ", [
                $searchKey, "{$searchKey}%", "%{$searchKey}%",
                "%{$searchKey}%", "%{$searchKey}%", "%{$searchKey}%"
            ]);
        }

        if ($sortByClicks) {
            $query->orderBy('clicks', 'desc');
        } elseif ($type === 'education') {
            $query->orderByRaw("CASE WHEN title REGEXP '^[0-9]' THEN 2 ELSE 1 END, title ASC")
              ->orderBy('title', 'ASC');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        //         $cacheKey = "search_results_{$type}_{$searchKey}_page_{$currentPage}_sort_{$sortByClicks}";
        //         return Cache::remember($cacheKey, now()->addMinutes(10), function () use ($query, $perPage, $currentPage) {
        return $query->paginate($perPage, ['*'], 'page', $currentPage);
        //         });
    }

    /**
     * Fetch Top Blogs data with optional search.
     */
    public static function getTopBlogs($type, $limit = 3)
    {
        return self::with('primaryCategory:id,title')
            ->where('type', $type)
            ->orderBy('clicks', 'desc')
            ->take($limit)
            ->get();
    }

    public function usersWithProgress()
    {
        return $this->belongsToMany(User::class, 'article_progress')
                    ->withPivot('progress')
                    ->withTimestamps();
    }

    /**
     * Fetch Average Progress For Education data with optional search.
     */
    public static function averageProgressEducation()
    {
        $educations = self::where('type', ArticleType::EDUCATION->value)->get();

        $averageProgress = $educations->map(function ($education) {
            return $education->progressPercentage->progress ?? 0;
        })->avg();

        return $averageProgress;
    }


}

"use client";
import { Col, Container, Row } from "react-bootstrap";
import { LanguageProvider } from "@/context/LanguageContext";
import SideBar from "@/Components/common/Dashboard/SideBar";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import MetaHead from "@/Seo/Meta/MetaHead";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import "@/css/dashboard/TradeCalculators.scss";
import "@/css/dashboard/layout.scss";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import RiskRewardCalculator from "@/Components/common/TradingCalculator/RiskRewardCalculator";
import TradeCalculator from "@/Components/common/TradingCalculator/TradeCalculator";
import PercentageIncreaseCalculator from "@/Components/common/TradingCalculator/PercentageIncreaseCalculator";
import CompoundInterestCalculation from "@/Components/common/TradingCalculator/CompoundInterestCalculation";
import React from "react";

const TradeCalculators = ({ meta }) => {
    const metaArray = {
        noindex: false,
        title: "Trading Calculator | Financial Tools for Traders | TradeReply",
        description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions.",
        canonical_link: "https://www.tradereply.com/trading-calculator",
        og_site_name: "TradeReply",
        og_title: "Trading Calculator | Financial Tools for Traders | TradeReply",
        og_description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions.",
        twitter_title: "Trading Calculator | Financial Tools for Traders | TradeReply",
        twitter_description: "Use TradeReply's Trading Calculator to access financial tools designed to assist traders. Perform essential calculations for better trading decisions."
    };
    return (
        <>
            <LanguageProvider>
                <MetaHead props={meta || metaArray} />
                <Header />
                <main className="admin_layout">
                    <div className="admin_layout_sidebar">
                        <SideBar />
                    </div>
                    <div className="admin_layout_content">
                        <div className="trade_calculators">
                            <CommonHead />
                            <Container>
                                <div className="trade_head justify-content-center mb-0 py-4">
                                    <AdminHeading heading="Trading Calculators" centered />
                                </div>
                                <Row>
                                    <Col xs={12} lg={7}>
                                        <RiskRewardCalculator />
                                        <TradeCalculator />
                                    </Col>
                                    <Col xs={12} lg={5} className="mt-4 mt-lg-0">
                                        <PercentageIncreaseCalculator />
                                        <CompoundInterestCalculation />
                                    </Col>
                                </Row>
                            </Container>
                        </div>
                    </div>
                </main>
                <Footer />
            </LanguageProvider>
            {/* <DashboardLayout>
                <MetaHead props={meta || metaArray} />
                <div className="trade_calculators">
                    <Container>
                        <CommonHead />
                        <div className="trade_head justify-content-center mb-0 py-4">
                            <AdminHeading heading="Trading Calculators" centered />
                        </div>
                        <Row>
                            <Col xs={12} lg={7}>
                                <RiskRewardCalculator />
                                <TradeCalculator />
                            </Col>
                            <Col xs={12} lg={5} className="mt-4 mt-lg-0">
                                <PercentageIncreaseCalculator />
                                <CompoundInterestCalculation />
                            </Col>
                        </Row>
                    </Container>
                </div>
            </DashboardLayout> */}
        </>
    );
};

export default TradeCalculators;
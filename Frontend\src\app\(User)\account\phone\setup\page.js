import { Suspense } from "react";
import SetupPhoneNumberComponent from "./SetupPhoneNumberComponent";
import MetaHead from "@/Seo/Meta/MetaHead";

export default function SetupPhoneNumber() {
    const metaArray = {
        noindex: true,
        title: "Account Phone | Add Number | TradeReply",
        description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
        canonical_link: "https://www.tradereply.com/account/phone/setup",
        og_site_name: "TradeReply",
        og_title: "Account Phone | Add Number | TradeReply",
        og_description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
        og_url: "https://www.tradereply.com/account/phone/setup",
        og_type: "website",
        og_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        og_image_width: "1200",
        og_image_height: "630",
        og_locale: "en_US",
        twitter_card: "summary_large_image",
        twitter_title: "Account Phone | Add Number | TradeReply",
        twitter_description:
            "Secure your TradeReply account by setting up your verified phone number. Improve account recovery options and receive important notifications instantly.",
        twitter_image:
            "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
        twitter_site: "@JoinTradeReply",
        robots: "noindex, nofollow",
    };
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <MetaHead props={metaArray} />
            <SetupPhoneNumberComponent />
        </Suspense>
    );
}

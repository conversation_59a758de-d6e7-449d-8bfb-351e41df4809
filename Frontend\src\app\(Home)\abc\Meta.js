export default function Meta({ title, description, keywords , children }) {
    return (
      <>
        {children}

        <title>{title}</title>

        {/*<meta name="robots" content="noindex, nofollow" />*/}

        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
  
        {/* Open Graph Meta Tags */}
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:image" content="/images/og-image.jpg" />
        <meta property="og:url" content="https://www.mywebsite.com/" />
        <meta property="og:type" content="website" />
  
        {/* Twitter Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        <meta name="twitter:image" content="/images/twitter-image.jpg" />
      </>
    );
  }
  
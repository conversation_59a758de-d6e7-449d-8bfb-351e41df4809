"use client";

import { Col, Container, Row } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import CustomPagination from "@/Components/UI/CustomPagination";
import RecentPost from "@/Components/common/Home/RecentPost";
import { useEffect, useRef, useState } from "react";
import { RightArrowIcon } from "@/assets/svgIcons/SvgIcon";
import "@/css/Home/Category.scss";
import HomeLayout from "@/Layouts/HomeLayout";
import { get } from "@/utils/apiUtils";
import { useSearchParams, useRouter } from "next/navigation";
import Loader from "@/Components/common/Loader";
import Link from "next/link";
import { CrossIcon } from "@/assets/svgIcons/SvgIcon";
const CategoryPage = (props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const slug = searchParams.get("slug");

  const sliderRef = useRef(null);
  const [disableLeft, setDisableLeft] = useState(true);
  const [disableRight, setDisableRight] = useState(false);
  const [listingAllCategories, setListingAllCategories] = useState([]);
  const [allCategoryArticles, setAllCategoryArticles] = useState([]);

  const [isMobile, setIsMobile] = useState(false);
  const [categoryMeta, setCategoryMeta] = useState(null);
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [selectedCategory, setselectedCategory] = useState(null);
  const [isLoading, setisLoading] = useState(false);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setisLoading(true);
        const response = await get("/category", { slug });
        setisLoading(false);
        setListingAllCategories(response?.data?.allcategories);
        setAllCategoryArticles(response?.data?.articles);
        setCategoryMeta(response?.data?.meta);
        setselectedCategory(response?.data?.selected_category);
        setSelectedFilter(response?.data?.selected_category?.title);

      } catch (error) {
        setisLoading(false);
        console.log("Category Error ", error);
      }
    };

    fetchCategories();
  }, [slug, selectedFilter]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const checkScreenSize = () => {
        setIsMobile(window.innerWidth <= 768);
      };

      checkScreenSize();
      window.addEventListener("resize", checkScreenSize);

      return () => {
        window.removeEventListener("resize", checkScreenSize);
      };
    }
  }, []);


  const smoothScroll = (amount) => {
    if (sliderRef.current) {
      const start = sliderRef.current.scrollLeft;
      const end = start + amount;
      const duration = 300; // Duration in ms
      const startTime = performance.now();

      const step = (currentTime) => {
        const elapsed = currentTime - startTime;
        // const progress = Math.min(elapsed / duration, 1); // Ensure we don’t overshoot the duration
        const progress = 1; // Ensure we don’t overshoot the duration
        const scrollAmount = start + (end - start) * progress;

        sliderRef.current.scrollLeft = scrollAmount;

        if (progress < 1) {
          requestAnimationFrame(step);
        }
      };
      requestAnimationFrame(step);
    }
  };
  const scrollLeft = () => {
    if (isMobile) {
      smoothScroll(-100); // Scroll left by 100px
    }
  };

  const scrollRight = () => {
    if (isMobile) {
      smoothScroll(100); // Scroll right by 100px
    }
  };
  // Check scroll position
  const checkScrollPosition = () => {
    if (sliderRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = sliderRef.current;
      setDisableLeft(scrollLeft === 0);
      setDisableRight(scrollLeft + clientWidth >= scrollWidth);
    }
  };

  // Add event listener to check scroll position when component mounts
  useEffect(() => {
    if (sliderRef.current) {
      sliderRef.current.addEventListener("scroll", checkScrollPosition);
    }
    return () => {
      if (sliderRef.current) {
        sliderRef.current.removeEventListener("scroll", checkScrollPosition);
      }
    };
  }, []);


  const [newPage, setNewPage] = useState(null);

  const handleCategoryClick = (slug) => {
    console.log('slug', slug);
  }
  const resetSlecetedFilter = () => {
    setisLoading(true);
    router.push('/category');
  }

  const handleDataFromChild = (childData) => {
    window.scrollTo(0, 0);
    setNewPage(childData);
  };
  useEffect(
    () => {
      if (newPage) {
        const getNewCategories = async () => {
          try {
            setisLoading(true);
            // const response = await get(`/category?page=${newPage}` , {selectedCategory.slug}); 
            const response = await get(`/category`, { page: newPage, slug: selectedCategory?.slug });;
            setListingAllCategories(response?.data?.allcategories);
            setAllCategoryArticles(response?.data?.articles);
            setCategoryMeta(response?.data?.meta);
            setselectedCategory(response?.data?.selected_category);
            setSelectedFilter(response?.data?.selected_category?.title);
            setisLoading(false);
          } catch (error) {
            setisLoading(false);
            console.log("CAtegory Error ", error);
          }
        };

        getNewCategories();
      }
      else {
        // console.log("page detail for category new not found");
      }


    }
    , [newPage]
  )

  return (
    <HomeLayout>
      <section className="categorySec py-100">
        <Container>
          <div className="categorySec_heading text-center">
            <h1>TradeReply Categories</h1>
            <p>Browse categories to find relevant articles and insights.</p>

            <div className="categorySec_search">
              <CommonSearch placeholder="Search for terms" icon={true}
                name={"categoryPage"}
              />
            </div>
          </div>
          <div className="categorySec_fliters">
            <div className="categorySec_fliters_inner">
              <button
                className={`scroll-btn left ${disableLeft ? "disabled" : ""}`}
                disabled={disableLeft}
                onClick={scrollLeft}
              >
                <RightArrowIcon />
              </button>
              <div className="slider" ref={sliderRef}>
                <Link href={"/category"} className={`categorySec_fliters_boxbutton ${selectedCategory ? '' : 'active'}`}>
                  All
                </Link>
                {listingAllCategories?.map((item) => (
                  <div
                    className={`categorySec_fliters_boxbutton ${item.slug === selectedCategory?.slug ? 'active' : ''}`}
                    key={item?.id}
                  // onClick={() => handleCategoryClick(item?.slug)}
                  >
                    <Link href={`/category/${item.slug}`}>
                      {item?.title}
                    </Link>
                  </div>
                ))}

              </div>
              <button
                className={`scroll-btn right ${disableRight ? "disabled" : ""}`}
                disabled={disableRight}
                onClick={scrollRight}
              >
                <RightArrowIcon />
              </button>
            </div>
            <div className="categorySec_fliters_boxadd">
              <h6 className="d-flex gap-2 align-items-center">
                <span>Filter:</span>
                <span>
                  {selectedFilter ? selectedFilter : " All "}
                </span>
                <span className="ml-2 pe-auto" onClick={() => { resetSlecetedFilter(); }}>
                  {selectedFilter ? <CrossIcon /> : <></>}
                </span>
              </h6>
            </div>
          </div>
          {isLoading ?
            <Loader />
            :
            <div className="categorySec_term">
              {selectedCategory ?
                <div className="categorySec_term_content">
                  <h4> Category – {selectedCategory?.title}</h4>
                  <p className="mb-5 mt-3">{selectedCategory?.content}</p>
                </div>
                :
                <div className="categorySec_term_content">
                  <h4> All Categories</h4>
                  <p className="mb-5 mt-3">No category selected</p>
                </div>

              }


              {/* {listingAllCategories?.map((item) => (
              <>
                <div className="categorySec_term_content" key={item?.id}>
                  <h4> Category – {item?.title}</h4>
                  <p className="mb-5 mt-3">{item?.content}</p>
                </div>

                <div className="blog_pagination justify-content-center justify-content-md-end d-flex mb-4">
                  <CustomPagination />
                </div>
              </>
            ))} */}
              <div className="d-flex justify-content-end w-100">
                <CustomPagination links={categoryMeta} onDataSend={handleDataFromChild} pageUrl={"category/page/"} />
              </div>
              <div className="blog_recentPost">
                {allCategoryArticles?.map((item, index) => (
                  <div key={index}>
                    <Row>
                      <Col xs={12} className="d-flex">
                        <div>
                          {item.name}
                        </div>
                        <RecentPost
                          img={item.feature_image}
                          title={item?.title}
                          text={item?.summary}
                          // coinname={item.coinname}
                          // onClick={() => navigate(":id")}
                          href={item.type == "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                        />
                      </Col>
                    </Row>
                  </div>
                ))}
                <div className="blog_pagination justify-content-center justify-content-md-end d-flex mt-4">
                  <CustomPagination />
                </div>
              </div>
            </div>
          }
        </Container>
      </section>
    </HomeLayout>
  );
};

export default CategoryPage;

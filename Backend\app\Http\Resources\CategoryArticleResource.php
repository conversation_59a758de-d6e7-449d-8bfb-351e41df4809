<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        {
            return [
                'id' => $this->id,
                'title' => $this->title,
                'content' => $this->content,
                'slug' => $this->slug,
                'blog' => new SitemapBlogResource($this->whenLoaded('primaryCategory')),
                'education' => new SitemapBlogResource($this->whenLoaded('primaryCategory')),
                'created_at' => $this->created_at ?  $this->created_at->toDateTimeString() : null,
                'updated_at' => $this->updated_at ?  $this->updated_at->toDateTimeString() : null,
            ];
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Services\StripeService;


class StripeController extends Controller
{
    //
    protected $stripe;

    public function __construct(StripeService $stripe)
    {
        $this->stripe = $stripe;
    }

    public function chargeCustomer(User $user)
    {
        // Check if the user already has a Stripe customer ID
        if (!$user->stripe_id) {
            $customer = $this->stripe->createCustomer(
                ($user->email ?? '<EMAIL>'),
                ($user->name ?? 'John Doe')
            );

            // Store the Stripe customer ID in the database
            $user->stripe_id = $customer->id;
            $user->save();
        } else {
            $customer = (object) ['id' => $user->stripe_id]; // Use existing customer
        }

        // Charge the customer
        $charge = $this->stripe->createCharge($customer->id, 50, 'usd', 'Test Charge');

        return response()->json(['charge' => $charge]);
    }


    public function checkout()
    {
        $session = $this->stripe->createCheckoutSession(50, 'usd', route('success'), route('cancel'));

        return response()->json(['session_id' => $session->id]);
    }


}

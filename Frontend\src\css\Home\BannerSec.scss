@use "../theme/var";

.banner_sec {
  position: relative;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-trade-analysis.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 1.35rem 0;
  min-height: 566px;
  z-index: 2;
  display: flex;
  align-items: center;

  @media (min-width: 1366px) {
    min-height: calc(100vh - 90px);
  }

  @media (max-width: 1199px) {
    background-position: right;
  }

  @media (max-width: 767px) {
    flex-direction: column;
    background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-visual-kpis.png");
    padding-top: 47px;
    padding-bottom: 71px;
    background-size: contain;
    min-height: auto;
  }

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, #000607 0%, #031940 83.43%);
    opacity: 0.5;
    z-index: -1;
  }

  &_content {
    position: relative;
    z-index: 1;
    margin-top: -10%;

    @media (max-width: 767px) {
      margin-top: 0;
    }

    h1 {
      font-size: 100px;
      line-height: 110px;
      max-width: 990px;
      font-weight: bold;
      position: relative;
      z-index: 1;

      @media (max-width: 1399px) {
        font-size: 90px;
        line-height: 90px;
        max-width: 902px;
      }

      @media (max-width: 1199px) {
        font-size: 70px;
        line-height: 80px;
        max-width: 702px;
      }

      @media (max-width: 991px) {
        font-size: 55px;
        line-height: 64px;
        max-width: 602px;
      }

      @media (max-width: 767px) {
        font-size: 48px;
        line-height: 52px;
        max-width: 600px;
        text-align: center;
      }

      @media (max-width: 374px) {
        font-size: 40px;
        line-height: 48px;
        max-width: 500px;
      }
    }
  }

  &_search {
    text-align: center;
    max-width: 388px;
    width: 100%;
    margin: 0rem auto 0;
    position: absolute;
    bottom: 70px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;

    @media (max-width: 767px) {
      width: 90%;
      position: static;
      transform: none;
      margin-top: 30px;
    }

    .form-label {
      font-size: 28px;
      font-weight: bold;
      line-height: 40.7px;
      letter-spacing: -1px;
      color: var.$white;

      @media (max-width: 1199px) {
        font-size: 24px;
        line-height: 34px;
      }

      @media (max-width: 991px) {
        font-size: 18px;
        line-height: 30px;
      }
    }
  }

  &_overlayimg {
    position: absolute;
    bottom: 0;
    right: 0;

    @media (max-width: 991px) {
      bottom: 90px;
    }

    @media (max-width: 767px) {
      position: static;
    }

    img {
      width: 526px;

      @media (min-width: 1680px) {
        width: 650px;
      }

      @media (max-width: 991px) {
        width: 347px;
      }
    }
  }
}
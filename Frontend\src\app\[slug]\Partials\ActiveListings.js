"use client";
import { Dropdown } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  TripleDotsMenu,
  BlackShareIcon,
  StaticListingImg,
  EyeDarkIcon,
  DeleteDarkIcon,
  RenameIcon,
  RightArrowIconSvg
} from "@/assets/svgIcons/SvgIcon";

export default function AccountOverview({ listings = [] }) {
  const [openIndex, setOpenIndex] = useState(null);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target)
      ) {
        setOpenIndex(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = (index) => {
    setOpenIndex((prevIndex) => (prevIndex === index ? null : index));
  };

  return (
    <CommonWhiteCard
      title="Active Listings"
      Linktext="View all"
      link="/account/your-listings"
      Linkicon={<RightArrowIconSvg />}
      className="account_card"
    >
      <div className="account_card_active_listing">
        <div className="main_inform justify-between">
          <p className="most_recent">
            {listings.length} of {listings.length}
          </p>
          <span className="most_recent text-end">Sorted: Recently Created</span>
        </div>

        {listings.map((item, index) => (
          <div className="mini_card" key={index}>
            <div className="main_inform respon_sell_feedback">
              <div className="activeListing_photo">
                <StaticListingImg />
              </div>
              <div>
                <h6>{item.title}</h6>
                <p className="inner_price_text">
                  ${item.price} - Listed on {item.listedDate}
                </p>
                <p className="inner_price_text">
                  {item.clicks} clicks on listing since listed
                </p>
              </div>
            </div>

            <div
              className="d-flex gap-2 justify-end relative"
              ref={containerRef}
            >
              <button className="round-border-btn" type="button">
                <BlackShareIcon />
                Share
              </button>
              <button
                className="rounded-border-btn px-3"
                type="button"
                onClick={() => toggleDropdown(index)}
              >
                <TripleDotsMenu />
              </button>
              {openIndex === index && (
                <Dropdown.Menu
                  show
                  style={{
                    position: "absolute",
                    bottom: "125%",
                    right: 4,
                    zIndex: 1000,
                  }}
                >
                  <Dropdown.Item className="dropdownlist" eventKey="2">
                    <EyeDarkIcon /> <span>View Listing</span>
                  </Dropdown.Item>
                  <Dropdown.Item className="dropdownlist" eventKey="3">
                    <RenameIcon /> <span>Edit Listing</span>
                  </Dropdown.Item>
                  <Dropdown.Item className="dropdownlist" eventKey="4">
                    <DeleteDarkIcon /> <span>Delete Listing</span>
                  </Dropdown.Item>
                </Dropdown.Menu>
              )}
            </div>

            <div className="d-sm-flex d-block gap-2 mt-3 justify-stretch">
              <button className="round-bluefill-btn w-md-50 w-100" type="button">
                End Listing
              </button>
              <button className="round-bluefill-btn w-md-50 w-100 mt-2 mt-sm-0" type="button">
                Mark out of stock
              </button>
              <button className="round-bluefill-btn w-md-50 w-100 mt-2 mt-sm-0" type="button">
                View Listing
              </button>
            </div>
          </div>
        ))}
      </div>
    </CommonWhiteCard>
  );
}

<?php

namespace App\Providers;

use App\Services\TradeBuilderService;
use Illuminate\Support\ServiceProvider;
use App\Repositories\TradeBuilderRepository;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(TradeBuilderRepository::class, function ($app) {
              return new TradeBuilderRepository();
         });

         $this->app->bind(TradeBuilderService::class, function ($app) {
              return new TradeBuilderService($app->make(TradeBuilderRepository::class));
         });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}

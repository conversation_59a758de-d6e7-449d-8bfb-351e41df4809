<?php
namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // allow all users to register
    }

    public function rules(): array
    {
        return [
            'email' => 'required|string|email:rfc|max:100|unique:users,email',
            'password' => 'required|string|min:8',
            'uuid' => 'required|string',
            'pricing' => 'required|string|in:free,pro,premium,essential',
            'trial' => 'required|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => 'The email field is required.',
            'email.string' => 'The email must be a valid string.',
            'email.email' => 'Please enter a valid email address with a domain extension (e.g., ".com", ".net").',
            'email.max' => 'The email must not exceed 100 characters.',
            'email.unique' => 'This email is already associated with an account.',
            'password.required' => 'The password field is required.',
            'password.min' => 'The password must be at least 8 characters long.',
            'uuid.required' => 'UUID is required.',

            'pricing.required' => 'Please select a pricing plan.',
            'pricing.in' => 'Invalid pricing option selected.',
            'trial.required' => 'Trial selection is required.',
            'trial.boolean' => 'Trial must be true or false.',
        ];
    }
}

'use client';
import React from 'react'
import { Col, Container, Row } from "react-bootstrap"
import { CheckIcon, RedCircleCrossIcon, PlusIcon, SearchIcons } from "@/assets/svgIcons/SvgIcon"
import DashboardLayout from "@/Layouts/DashboardLayout";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonHead from "@/Components/common/Dashboard/CommonHead";
import "@/css/dashboard/TagManager.scss";
import InputLabel from '@/Components/UI/InputLabel';
import TextInput from '@/Components/UI/TextInput';
import CommonButton from '@/Components/UI/CommonButton';
import MetaHead from '@/Seo/Meta/MetaHead';
import store from "@/redux/store/metaStore";
import { setMeta } from "../../../redux/metaSlice";

export default function TagManager() {
    const metaArray = {
        noindex: true,
        title: "Tag Manager | Organize Your Trades with Keywords | TradeReply",
        description: "Manage and apply custom tags to your trades, entries, and exits with TradeReply's Tag Manager. Add context and organize your trading data effortlessly.",
        canonical_link: "https://www.tradereply.com/dashboard/tag-manager",
        og_site_name: "TradeReply",
        og_title: "Tag Manager | Organize Your Trades with Keywords | TradeReply",
        og_description: "Manage and apply custom tags to your trades, entries, and exits with TradeReply's Tag Manager. Add context and organize your trading data effortlessly.",
        og_url: "https://www.tradereply.com/dashboard/tag-manager",
        og_type: "website",
        og_image: "https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg",
        og_image_width: 1200,
        og_image_height: 630,
        og_locale: "en_US",
        twitter_card: "summary_large_image",
        twitter_title: "Tag Manager | Organize Your Trades with Keywords | TradeReply",
        twitter_description: "Manage and apply custom tags to your trades, entries, and exits with TradeReply's Tag Manager. Add context and organize your trading data effortlessly.",
        twitter_image: "https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg",
        twitter_site: "@JoinTradeReply"
    };
    return (
        <>
            <DashboardLayout>
                <MetaHead props={metaArray} />
                <div className="tag_manager">
                    <CommonHead />
                    <Container>
                        <div className="trade_head mb-0 justify-content-center">
                            <AdminHeading heading="Tag Manager" />
                        </div>
                        <div className='create_new_tag'>
                            <div className='customInput'>
                                <InputLabel value="Tag Name" />
                                <TextInput
                                    type="text"
                                    placeholder="e.g. Earning Breakout"
                                />
                            </div>
                            <div className='customInput'>
                                <InputLabel value="Tag Description" />
                                <textarea
                                    className='form-control'
                                    type="text"
                                    placeholder="e.g. Trades triggered by a price surge following an earnings report." />
                            </div>
                            <CommonButton
                                title="Create New Tag"
                                className="w-full my-3"
                                onlyIcon={<PlusIcon />}

                            />
                        </div>
                        <div className='new_tag my-3'>
                            <div className='new_tag_actions mb-4'>
                                <div className='action_btns'>
                                    <select className="form-select" defaultValue="">
                                        <option value="">Bulk Action</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <CommonButton
                                        title="Apply"
                                    />
                                    <div className='search'>
                                        <SearchIcons />
                                        <TextInput type="text" placeholder='Search Tags' />
                                    </div>
                                </div>
                                <p className='no_of_tags'>
                                    3 Tags
                                </p>
                            </div>
                            <div className='new_tag_actions_mobile mb-4'>
                                <div className='action_btns'>
                                    <select className="form-select" defaultValue="">
                                        <option value="">Bulk Action</option>
                                        <option value="delete">Delete</option>
                                    </select>
                                    <CommonButton
                                        title="Apply"
                                    />
                                    <p className='no_of_tags'>
                                        3 Tags
                                    </p>
                                </div>
                                <div className='search mt-3'>
                                    <SearchIcons />
                                    <TextInput type="text" placeholder='Search Tags' />
                                </div>
                            </div>
                            <div className='new_tag_table'>
                                <table className='table table-stripeds'>
                                    <thead>
                                        <tr>
                                            <th>
                                                <input className="custom_checkbox_input form-check-input" type="checkbox" value="" />
                                            </th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Count</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <input className="custom_checkbox_input form-check-input" type="checkbox" value="" />
                                            </td>
                                            <td>Dip</td>
                                            <td>Trade Exceuted when there was a dip</td>
                                            <td>0</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input className="custom_checkbox_input form-check-input" type="checkbox" value="" />
                                            </td>
                                            <td>Dip</td>
                                            <td>Trade Exceuted when there was a dip</td>
                                            <td>0</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <input className="custom_checkbox_input form-check-input" type="checkbox" value="" />
                                            </td>
                                            <td>Dip</td>
                                            <td>Trade Exceuted when there was a dip</td>
                                            <td>0</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <p className='font-semibold'>Deleting a tag will not delete the data attached to it. The tag will simply be removed from the associated entries, exits or trades.</p>
                    </Container>
                </div>
            </DashboardLayout>

        </>
    )
}

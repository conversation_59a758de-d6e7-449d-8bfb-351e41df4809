import React, { useState, useEffect } from "react";
import CommonButton from "@/Components/UI/CommonButton";

export default function DeleteTradeAccount() {
  const [isModal, setIsModal] = useState(false);

  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Delete Trade Account</p>
      </div>
      <div className="portfolio_manager_card_deleteAll">
        <p className="section_text">
          This will permanently delete the entire Trade Account and all
          associated data — including trades, deposits, strategies, analytics,
          dashboards, and account settings.
        </p>
        <p className="section_text">
          Your main account and other Trade Accounts will not be affected.
        </p>
        <p className="section_text">This action is irreversible.</p>
        <CommonButton
          title="Delete Trade Account"
          onClick={() => setIsModal(true)}
          className="red-btn me-2"
        />
        {isModal && (
          <div className="deleteAllModal_overlay">
            <div className="deleteAll_modal">
              <p>
                Are you sure you want to permanently delete “[Trade Account
                Name]”?
              </p>
              <CommonButton
                title="Cancel"
                className="white-btn me-2"
                onClick={() => setIsModal(false)}
              />
              <CommonButton
                title="Delete Trade Account"
                className="red-btn"
                onClick={() => setIsModal(false)}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

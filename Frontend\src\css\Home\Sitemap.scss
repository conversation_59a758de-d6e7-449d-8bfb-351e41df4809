@use "../theme/var";

.sitemap {
  @media screen and (max-width: 767px) {
    padding-bottom: 0 !important;
  }

  &_content {
    &_box {
      @media screen and (max-width: 767px) {
        text-align: center;
        margin-bottom: 50px;
      }

      &_icon {
        height: 70px;
        margin-bottom: 1.25rem;
        display: flex;
        align-items: flex-end;

        @media screen and (max-width: 767px) {
          justify-content: center;
        }
      }

      h4 {
        color: var.$textclr;
        font-size: 1.65rem;
        font-weight: 800;
      }

      ul {
        padding-top: 10px;

        li {
          a {
            color: var.$white;
            display: block;
            margin-top: 0.625rem;
            font-size: 1.25rem;
            font-weight: 500;
            line-height: 24.5px;
            letter-spacing: -0.10000000149011612px;

            @media (max-width: 767px) {
              font-size: 1rem;
            }

            &:hover {
              color: var.$baseclr;
            }
          }
        }
      }
    }
  }
}
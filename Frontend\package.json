{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^3.10.0", "@reduxjs/toolkit": "^2.5.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tiptap/core": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "axios": "^1.9.0", "bootstrap": "^5.3.3", "cookies-next": "^5.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.4", "formik": "^2.4.6", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.2", "jodit-react": "^5.2.15", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.523.0", "next": "^15.1.6", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-circular-progressbar": "^2.1.0", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-i18next": "^15.4.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-slick": "^0.30.3", "sass": "^1.83.4", "slick-carousel": "^1.8.1", "uuid": "^11.1.0", "world-countries": "^5.1.0", "yup": "^1.6.1"}, "devDependencies": {"postcss": "^8", "tailwindcss": "^3.4.1"}}
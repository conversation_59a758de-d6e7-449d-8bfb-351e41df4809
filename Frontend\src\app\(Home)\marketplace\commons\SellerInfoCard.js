import Image from 'next/image';
import CommonButton from "@/Components/UI/CommonButton";

const SellerInfoCard = ({ sellerName, sellerImage, ratingCount, totalReviews }) => {
    return (
        <div className="marketplace_products_sellerInfo">
            <h4>Seller Info</h4>
            <div className="sellerProfile">
                <Image src={sellerImage} alt={sellerName} width={50} height={50} />
                <p>{sellerName}</p>
            </div>
            <div className="sellerRating">
                {Array.from({ length: ratingCount }).map((_, index) => (
                    <Image
                        key={index}
                        src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-star-single.svg"
                        alt="star"
                        width={25}
                        height={25}
                    />
                ))}
                <span>{totalReviews}</span>
            </div>
            <div className="sellerBtn">
                <CommonButton type="button" title="FOLLOW SELLER" className="cart_button" />
            </div>
        </div>
    );
};

export default SellerInfoCard;

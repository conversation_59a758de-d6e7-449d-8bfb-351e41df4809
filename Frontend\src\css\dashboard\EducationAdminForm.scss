// .tiptap-editor {
//   background: #fff; // Match background color
//   border: none; // Remove the default black border
//   border-radius: 8px; // Optional: rounded edges
//   padding: 12px; 
//   min-height: 200px;
//   outline: none; // Prevent default focus outline
//   box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.1); // Soft inner shadow

//   &:focus-within {
//     border: 1px solid #ccc; // Subtle focus border
//     box-shadow: inset 0px 0px 5px rgba(0, 0, 0, 0.1);
//   }

//   /* Styling inside the editor */

//   p {
//     margin: 8px 0;
//   }

//   strong {
//     font-weight: bold;
//   }

//   em {
//     font-style: italic;
//   }

//   blockquote {
//     border-left: 4px solid #ccc;
//     padding-left: 10px;
//     font-style: italic;
//     color: #555;
//   }

//   ul {
//     padding-left: 20px;
//     list-style-type: disc;
//   }

//   ol {
//     padding-left: 20px;
//     list-style-type: decimal;
//   }

//   pre {
//     background: #f4f4f4;
//     padding: 10px;
//     border-radius: 5px;
//     overflow-x: auto;
//   }
// }

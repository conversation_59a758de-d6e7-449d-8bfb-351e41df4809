@use "../theme/var";

.Account_sidebar {
  position: sticky;
  top: 88px;
  left: 0;
  background-color: var.$clr031940;
  padding: 1.875rem 1.5rem 1rem;
  height: calc(100vh - 88px);
  overflow-y: auto;
  width: 355px;
  transition: all ease-in-out 0.3s;
  z-index: 999;

  @media (max-width: 1199px) {
    left: -325px;
    width: 325px;
    height: 100vh;
    position: fixed;
    top: 0;
    padding: 6rem 1.5rem 1.875rem;
    z-index: 9999;
    background-color: rgba(3, 25, 64, 0.9);
    backdrop-filter: blur(5px);
  }

  @media (max-width: 767px) {
    left: -100%;
    width: 100%;
  }

  &.opensidebar {
    @media (max-width: 1199px) {
      left: 0;
    }
  }

  &_head {
    @media (max-width: 1199px) {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 22px;
      width: 100%;
      text-align: center;

      &::after {
        content: "";
        position: absolute;
        right: 0;
        bottom: -20px;
        width: 100%;
        height: 1px;
        background-color: var.$borderclr;
      }

      .headLogo {
        img {
          width: 150px;
        }
      }
    }
  }

  ul {
    padding: 0;
    margin: 0;
    list-style: none;

    li {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      a {
        color: var.$white;
        font-size: 1.125rem;
        font-weight: 600;
        line-height: 24.5px;
        letter-spacing: -0.10000000149011612px;
        text-align: left;
        padding: 15px 1rem;
        border-left: 3px solid transparent;
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;

        &::after {
          content: "";
          background: linear-gradient(
            90deg,
            rgba(4, 73, 140, 0.5) 0%,
            rgba(4, 73, 140, 0) 100%
          );
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0;
          height: 100%;
          z-index: -1;
          transition: all ease-in-out 0.3s;
        }

        span {
          margin-right: 15px;

          svg {
            width: 20px;
          }
        }

        &:hover,
        &.active {
          border-color: var.$baseclr;

          &::after {
            width: 100%;
          }
        }
      }
    }
  }

  &_bottom_link {
    border-top: 1px solid var.$clr04498C;
    padding-top: 1.25rem;
    margin-top: 1.25rem;

    ul {
      li {
        a {
          svg {
            margin-left: 1rem;
            transition: all ease-in-out 0.3s;
          }

          &:hover {
            svg {
              margin-left: 1.5rem;
            }
          }
        }
      }
    }
  }

  .filter_toggle {
    display: none;

    @media (max-width: 1199px) {
      position: absolute;
      top: 0;
      display: block;
      right: 20px;
      left: auto;
    }
  }
}
.Account_sidebar::-webkit-scrollbar{
  width: 5px;
  height: 4px;
  border-radius: 1rem;
}

.Account_sidebar::-webkit-scrollbar-thumb {
  background-color: #0557a3;
  border-radius: 1rem;
}
.filter_toggle {
  display: none;

  @media (max-width: 1199px) {
    position: absolute;
    top: 51px;
    left: 20px;
    display: block;
  }

  &_btn {
    background-color: transparent;
    margin-left: 1.25rem;
    padding: 0;
    position: relative;
    width: 26px;
    height: 22px;
    border: 0;

    &:focus {
      box-shadow: none;
      border: 0;
      outline: 0;
    }

    @media (max-width: 1199px) {
      margin-left: 0;
    }

    @media (max-width: 767px) {
      margin-left: 0;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 26px;
      background-color: var.$white;
      height: 2px;
      transition: all ease-in-out 0.3s;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 26px;
      background-color: var.$white;
      height: 2px;
      transition: all ease-in-out 0.3s;
    }

    span {
      background-image: none;
      height: 2px;
      background-color: var.$baseclr;
      width: 20px;
      transition: all ease-in-out 0.3s;
      display: flex;
    }

    &.active {
      &::after {
        transform: rotate(45deg) translate(-6px, -6px);
      }

      &::before {
        transform: rotate(-45deg) translate(-8px, 8px);
      }

      span {
        opacity: 0;
      }
    }
  }
}

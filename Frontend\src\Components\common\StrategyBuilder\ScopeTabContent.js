import React from 'react';
import { DropDownArrowIcon, SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";

export default function ScopeTabContent({ onSelectField, scope, activeTab, setActiveTab, dimensionData, metricsData }) {
    const handleSelect = (name) => {
        onSelectField(name);
    };

    return (
        <div>
            <div className='strategy-scope-tabs'>
                <div className='left_side'>
                    <div
                        className={`scope_dimension ${activeTab === 'dimension' ? 'active' : ''}`}
                        onClick={() => setActiveTab('dimension')}
                    >
                        <p>Dimensions</p>
                        <DropDownArrowIcon />
                    </div>
                    <div
                        className={`scope_metrices ${activeTab === 'metrics' ? 'active' : ''}`}
                        onClick={() => setActiveTab('metrics')}
                    >
                        <p>Metrics</p>
                        <DropDownArrowIcon />
                    </div>
                </div>

                <div className='right_side'>
                    {activeTab === 'dimension' &&
                        dimensionData.map((item, index) => (
                            <div
                                className="scope_dimension_show"
                                key={index}
                                onClick={() => handleSelect(item.name)}
                            >
                                <div>
                                    <p className="name">{item.name}</p>
                                    <span>Scope: {scope}</span>
                                </div>
                                <SolidInfoIcon />
                            </div>
                        ))}

                    {activeTab === 'metrics' &&
                        metricsData.map((item, index) => (
                            <div
                                className="scope_metrices_show"
                                key={index}
                                onClick={() => handleSelect(item.name)}
                            >
                                <div>
                                    <p className="name">{item.name}</p>
                                    <span>Scope: {scope}</span>
                                </div>
                                <SolidInfoIcon />
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
}
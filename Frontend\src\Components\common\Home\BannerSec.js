'use client';

import { Container } from "react-bootstrap";
import CommonSearch from "@/Components/UI/CommonSearch";
import "../../../css/Home/BannerSec.scss";
import Link from "next/link";
import { debounce } from "lodash";
import { useCallback, useEffect, useState } from "react";
import { get } from "@/utils/apiUtils";

const BannerSec = () => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchedArticles, setSearchedArticles] = useState([]);
  useEffect(() => {

    if (!searchKeyword) return;

    const fetchHome = async () => {
      try {
        const response = await get("/search/home", { key: searchKeyword });
        setSearchedArticles(response.data);
      } catch (error) {
        console.error("Failed to fetch Education Article:", error);
      }
    };
    fetchHome();
  }, [searchKeyword]);

  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      // setEducationSearch(searchTerm);
      setSearchKeyword(searchTerm);
    }, 300), // Adjust delay as needed
    []
  );

  const handleHomeSearch = (event) => {
    debouncedSearch(event.target.value);
  };

  return (
    <section className="banner_sec">
      <Container>
        <div className="banner_sec_content">

          <h1>Empowering Traders with Performance Analytics.</h1>
        </div>
      </Container>
      <div className="banner_sec_search">
        <CommonSearch
          label="Crypto & Stock Trading KPIs"
          placeholder="Explore Key Terms & Concepts"
          onChange={handleHomeSearch}
          icon={true}
          name={"bannerSec"}
        />
        {
          searchedArticles.length > 0 && searchKeyword ?
            <div className="position-relative">
              <div id="comboList" className="list-group position-absolute w-100">

                {searchedArticles.map((item, index) => (
                  <Link key={index}
                    href={item.type == "education" ? `/education/${item?.slug}` : `/blog/${item?.slug}`}
                    className="list-group-item list-group-item-action text-start d-flex justify-content-between align-items-center">
                    <span>
                      {item.title}
                    </span>
                    <span className="search-highlight text-uppercase">
                      {item.type}
                    </span>
                  </Link>
                ))}

                <Link href={`/search?search=${searchKeyword}`} className="list-group-item list-group-item-action text-primary" id="viewAll">View All</Link>

              </div>
            </div>
            :
            <></>
        }
      </div>
      <div className="banner_sec_overlayimg">
        <div>
          <img
            className="d-none d-md-block"
            src="https://cdn.tradereply.com/dev/site-assets/tradereply-optimize-trades.png"
            alt="TradeReply analytics suite: Optimize crypto and stock trading strategies with real-time insights and data-driven metrics."
            height={555}
          />
        </div>
      </div>
    </section>
  );
};

export default BannerSec;


"use client";

import { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import debounce from "lodash.debounce";
import Head from "next/head";
import HomeLayout from "@/Layouts/HomeLayout";
import Loader from "@/Components/common/Loader";
import CommonSearch from "@/Components/UI/CommonSearch";
import CommonButton from "@/Components/UI/CommonButton";
import CustomPagination from "@/Components/UI/CustomPagination";
import { getCookie, setCookie, deleteCookie } from "cookies-next";
import { get } from "@/utils/apiUtils";
import "@/css/Home/Education.scss";
import { Container, Row, Col } from "react-bootstrap";
import Link from "next/link";
import { CrossIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";

export default function EducationClient({
  initialArticles,
  initialPagination,
  initialSearch,
  initialPage,
  metaArray: initialMetaArray,
}) {
  const [educationArticles, setEducationArticles] = useState(initialArticles);
  const [educationPagination, setEducationPagination] = useState(initialPagination);
  const [searchKeyword, setSearchKeyword] = useState(initialSearch);
  const [isLoading, setIsLoading] = useState(false);
  const [newPage, setNewPage] = useState(initialPage);
  const [reset, setReset] = useState(false);
  const [metaArray, setMetaArray] = useState(initialMetaArray || {});
  const router = useRouter();

  useEffect(() => {
    const storedKeyword = getCookie("educationSearchKey");

    if (storedKeyword) setSearchKeyword(storedKeyword);
  }, []);

  const fetchEducation = async (page = 1, keyword = "") => {
    setIsLoading(true);
    try {
      const response = await get("/article", {
        key: keyword,
        type: "education",
        page: page,
        per_page: 25,
      });
  
      setEducationArticles(response.data.education);
      setEducationPagination(response.data.meta);      
    } catch (error) {
      console.error("Error fetching education:", error);
    } finally {
      setIsLoading(false);
    }
  };


  useEffect(() => {
    // removed extra redering when url change
    if (
      newPage === initialPage &&
      searchKeyword === initialSearch &&
      educationArticles.length > 0
    ) {
      return;
    }
    const fetchEducation = async () => {
      setIsLoading(true);
      try {
        const response = await get("/article", {
          key: searchKeyword,
          type: "education",
          page: newPage,
          per_page: 25,
        });

        setEducationArticles(response.data.education);
        setEducationPagination(response.data.meta);

        // Update meta dynamically
        const isSearch = searchKeyword.trim() !== "";
        //  for searching link
       
        setMetaArray({
          title: "TradeReply Education Center | Learn Trading Strategies",
          description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
          og_title: "TradeReply Education Center | Learn Trading Strategies",
          og_description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
          og_site_name: "TradeReply",
          twitter_title: "TradeReply Education Center | Learn Trading Strategies",
          twitter_description: "Learn the essentials of trading with TradeReply.com's Education Center. Access resources and tutorials on trading strategies, market analysis, and more.",
          noindex: isSearch,

          canonical_link: isSearch
            ? "" // 🔥 Clear canonical link when search is active
            : `https://dev.tradereply.com/education/page/${newPage}`,
          
        });

      } catch (error) {
        console.error("Error fetching education:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEducation();
  }, [searchKeyword]);

  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchKeyword(term);
      setNewPage(1);
      setCookie("educationSearchKey", term, { path: "/", sameSite: "lax" });
      // setCookie("educationSearchPage", "1");
    }, 400),
    []
  );

  const handleEducationSearch = (e) => {
    debouncedSearch(e.target.value);
  };


  const handlePageChange = (page) => {
    const url = page === 1 ? `/education` : `/education/page/${page}`;
    const isSamePage = page === newPage;
  
    setCookie("educationSearchKey", searchKeyword, { path: "/", sameSite: "lax" });
  
    if (!isSamePage) {
      setNewPage(page); // let useEffect trigger
      router.push(url);
    } else {
      // 👇 Force data refresh
      setNewPage(page); // Still set it, in case internal state reset
      fetchEducation(page, searchKeyword); // Force fetch manually
    }
  };
  

  const handleClearSearch = () => {
    deleteCookie("educationSearchKey", { path: "/" });
    const isAlreadyOnEducation = window.location.pathname === "/education";
    if (isAlreadyOnEducation) {
      // Reset state
      setSearchKeyword("");
      setNewPage(1);
      fetchEducation(1, ""); // Manually fetch page 1 data when already on /education
    } else {
      setNewPage(1);
      router.push("/education"); // Navigate to base route
    }
  };
 
  

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="education py-100">
        <Container>
          <div className="education_heading text-center">
            <h1>TradeReply Education Center</h1>
            <p>Your go-to resource for understanding key financial terms and concepts.</p>
            <div className="education_search">
              <CommonSearch
                placeholder="Explore Key Terms & Insights"
                icon={true}
                onChange={handleEducationSearch}
                reset={reset}
                name="education"
                value={searchKeyword}
                onClear={handleClearSearch}  
              />
            </div>
          </div>


          {searchKeyword && (
            <div className="education_fliters">
              <div className="education_fliters_boxadd">
                <h6>
                  Filter:
                  <span className="ml-3">{searchKeyword}</span>
                  <span
                    className="ml-2 pe-auto"
                    onClick={() => {
                      setReset(!reset);
                      setSearchKeyword("");
                      // deleteCookie("educationSearchKey");
                      deleteCookie("educationSearchKey", { path: "/" });
                      const isAlreadyOnEducation = window.location.pathname === "/education";

                    if (isAlreadyOnEducation) {
                      // ✅ Manually fetch page 1 if already on /education
                      setNewPage(1);
                      fetchEducation(1, "");
                    } else {
                      // ✅ Otherwise push to base route
                      setNewPage(1);
                      router.push("/education");
                    }
                    }}
                  >
                    {searchKeyword ? <CrossIcon /> : null}
                  </span>
                </h6>
              </div>
            </div>
          )}

          <div className="education_term">
            <Row className="mb-3 education_term_head">
              <Col md={3} lg={2} className="d-none d-md-block">
                <h4>Term</h4>
              </Col>
              <Col md={6} lg={8} className="d-none d-md-block">
                <h4>Summary</h4>
              </Col>
              <Col xs={12} md={3} lg={2}>
                <div className="education_pagination mb-4">

                  {educationPagination && (
                    <CustomPagination
                      links={educationPagination}
                      onDataSend={handlePageChange}
                      pageUrl="education"
                      flag={!!searchKeyword}
                      useDynamicParams={true}
                    // links={educationPagination}
                    // onDataSend={(page) => {
                    //   setNewPage(page); // Updates internal state
                    // }} 
                    // useLinks={false} 

                    />
                  )}
                </div>
              </Col>
            </Row>

            {isLoading ? (
              <Loader />
            ) : educationArticles?.length > 0 ? (
              educationArticles.map((item, index) => (
                <Row key={index} className="education_term_list align-items-center">
                  <Col xs={6} md={3} lg={2}>
                    <h6 className="text-break ws-normal">{item?.title}</h6>
                  </Col>
                  <Col xs={6} md={3} lg={2} className="order-md-last">
                    <div className="text-end">
                      <Link href={`/education/${item?.slug}`}>
                        <CommonButton type="button" title="Read More" className="read_more_button" />
                      </Link>
                    </div>
                  </Col>
                  <Col xs={12} md={6} lg={8}>
                    <p>{item?.summary}</p>
                  </Col>
                </Row>
              ))
            ) : (
              <p className="w-100 text-center">No results found</p>
            )}

            <div className="d-flex justify-content-end mt-5">
              {educationPagination && (
                <CustomPagination
                  links={educationPagination}
                  onDataSend={handlePageChange}
                  pageUrl="education"
                  useDynamicParams={true}

                // links={educationPagination}
                //     onDataSend={(page) => {
                //       setNewPage(page); // Updates internal state
                //     }} 
                // onDataSend={handlePageChange} 
                // useLinks={false} 


                />
              )}
            </div>
          </div>
        </Container>
      </div>
    </HomeLayout>
  );
}

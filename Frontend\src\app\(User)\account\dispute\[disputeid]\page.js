"use client";
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function Dispute() {
  const insignArray = [
    {
      title: "[Apr 1, 2025 - 3:42 PM]",
      array: [
        'Buyer updated dispute category changed from "Product not as described" to "Download link is broken',
        'Buyer updated preferred outcome changed from "Full refund" to "Corrected link',
        'Buyer updated their description from "this" to "that" ',
      ],
    },
    {
      title: "[Mar 25, 2025-3:42 PM)]",
      array: [
        " Seller disputed Buyers request",
        'Buyer updated preferred outcome changed from "Full refund" to "Corrected link',
        'Buyer updated their description from "this" to "that" ',
      ],
    },
    {
      title: "[Apr 1, 2025 - 3:42 PM]",
      array: ["Buyer opened a dispute for Order ID 29311"],
    },
  ];
  const disputeArray = [
    {
      orderId: "29311",
      product: "[Auto-Filled]",
      status: "Open",
      dispute: "3231",
      resolved: "Mar 24, 2025",
      opened: "Mar 24, 2025",
      respondBy: "[Auto-Filled]",
      buyer: "@wakos",
    },
  ];
  const sellBuyArray = [
    {
      orderId: "29311",
      product: "[Auto-Filled]",
      status: "Open",
      dispute: "3231",
      resolved: "Mar 24, 2025",
      opened: "Mar 24, 2025",
      respondBy: "[Auto-Filled]",
      buyer: "@wakos",
    },
  ];

  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Dispute Details" />
          <Row className="mb-4 mb-lg-4">
            {/* Followers */}
            <Col>
              <CommonWhiteCard title="Dispute Summary" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Order ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Product Title</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute ID</div>
                          </th>
                          <th>
                            <div className="th-inner">Status</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Opened</div>
                          </th>
                          <th>
                            <div className="th-inner">Date Resolved</div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Outcome</div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {disputeArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.orderId}</td>
                            <td>
                              <Link className="w-100" href="/marketplace/details">
                                {item.product}
                              </Link>
                            </td>
                            <td>{item.dispute}</td>
                            <td>{item.status}</td>
                            <td>{item.opened}</td>
                            <td>{item.resolved}</td>
                            <td>{item.respondBy}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {disputeArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={4}>Order ID</Col>
                            <Col xs={4}>Product Title</Col>
                            <Col xs={4}>Status</Col>
                          </div>

                          <Col xs={4} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            <Link className="w-100" href="/marketplace/details">
                              {item.product}
                            </Link>
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.status}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={4}>Dispute ID</Col>
                            <Col xs={4}>Date Opened</Col>
                            <Col xs={4}>Date Resolved</Col>
                          </div>

                          <Col xs={4} className="colunm_value">
                            {item.orderId}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.dispute}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.resolved}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Resolution Outcome</Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.respondBy}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Buyer Details" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Buyer</div>
                          </th>
                          <th>
                            <div className="th-inner">Dispute Category</div>
                          </th>
                          <th>
                            <div className="th-inner">Preferred Outcome</div>
                          </th>
                          <th>
                            <div className="th-inner">Description</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {sellBuyArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>{item.product}</td>
                            <td>{item.respondBy}</td>
                            <td>
                              <CommonButton
                                title="example.jpg"
                                className="view_res_btn"
                              />
                              <CommonButton
                                title="example_n.pdf"
                                className="view_res_btn w-auto mt-2"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {sellBuyArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={6}>Buyer</Col>
                            <Col xs={6}>Dispute Category</Col>
                          </div>

                          <Col xs={6} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={6} className="colunm_value">
                            {item.product}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Preferred Outcome</Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.respondBy}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12}>Description</Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.respondBy}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={12} className="d-flex gap-2">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </Col>
                          </div>
                          <CommonButton
                            title="example.jpg"
                            className="view_res_btn"
                          />
                          <CommonButton
                            title="example_n.pdf"
                            className="view_res_btn mt-2"
                          />
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col>
              <CommonWhiteCard title="Seller Details" className="account_card">
                <div className="account_card_disputes">
                  {/* Table View (shown only on md and up) */}
                  <div className="d-none d-md-block lg_screen_table">
                    <table className="table">
                      <thead>
                        <tr>
                          <th>
                            <div className="th-inner">Seller</div>
                          </th>
                          <th>
                            <div className="th-inner">Response Message</div>
                          </th>
                          <th>
                            <div className="th-inner">Resolution Chosen</div>
                          </th>
                          <th>
                            <div className="th-inner">
                              External Link
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                          <th>
                            <div className="th-inner">
                              Attachments
                              <CommonTooltip
                                className="d-flex align-items-center"
                                content="Manual entry lets you input trading data."
                                position="top-left"
                              >
                                <SolidInfoIcon />
                              </CommonTooltip>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {sellBuyArray.map((item, index) => (
                          <tr key={index}>
                            <td>{item.buyer}</td>
                            <td>{item.product}</td>
                            <td>{item.product}</td>
                            <td>{item.respondBy}</td>
                            <td>
                              <CommonButton
                                title="example.jpg"
                                className="view_res_btn"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Card View (shown only on small screens) */}
                  <div className="d-block d-md-none sm_screen_table">
                    {sellBuyArray.map((item, index) => (
                      <div className="wrap-div" key={index}>
                        <Row className="mb-2 px-2">
                          <div className="colunm_head">
                            <Col xs={4}>Seller</Col>
                            <Col xs={4}>Response Message</Col>
                            <Col xs={4}>Resolution Chosen</Col>
                          </div>

                          <Col xs={4} className="colunm_value">
                            {item.buyer}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.product}
                          </Col>
                          <Col xs={4} className="colunm_value">
                            {item.product}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                        <div className="colunm_head">
                          <Col xs={12} className="d-flex gap-2">
                            External Link
                            <CommonTooltip
                              className="d-flex align-items-center"
                              content="Manual entry lets you input trading data."
                              position="top-left"
                            >
                              <SolidInfoIcon />
                            </CommonTooltip>
                          </Col>
                          </div>
                          <Col xs={12} className="colunm_value">
                            {item.respondBy}
                          </Col>
                        </Row>
                        <Row className="mb-2 px-2">
                        <div className="colunm_head">
                          <Col xs={12} className="d-flex gap-2">
                            Attachments
                            <CommonTooltip
                              className="d-flex align-items-center"
                              content="Manual entry lets you input trading data."
                              position="top-left"
                            >
                              <SolidInfoIcon />
                            </CommonTooltip>
                          </Col>
                          </div>
                          <CommonButton
                            title="example.jpg"
                            className="view_res_btn w-auto"
                          />
                        </Row>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            {/* Followers */}
            <Col>
              <CommonWhiteCard
                title="Dispute Timeline Log"
                className="account_card"
              >
                <div className="account_card_timeline">
                  <div className=" ">
                    {insignArray.map((item, index) => (
                      <div key={index}>
                        <h2>{item.title}</h2>
                        <ul>
                          {item.array.map((item, index) => (
                            <li key={index}>{item}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              </CommonWhiteCard>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

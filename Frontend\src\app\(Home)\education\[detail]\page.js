import { unstable_noStore as noStore } from 'next/cache';
import EducationContent from "./components/EducationContent";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import { Container } from "react-bootstrap";
import Script from "next/script"; // <-- add this import

async function fetchEducationDetail(detail) {
  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);

  const res = await fetch(url.toString(), {
    cache: "no-store",
  });

  if (!res.ok) throw new Error(`API error: ${res.status}`);
  return res.json();
}

export async function generateMetadata({ params }) {
  noStore();

  const resolvedParams = await params;
  const detailSlug = resolvedParams.detail;
  const data = await fetchEducationDetail(detailSlug);
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  return {
    title: `What is ${data.data.title} | TradeReply Education`,
    description: data.data.summary,
    openGraph: {
      title: `What is ${data.data.title} | TradeReply Education`,
      description: data.data.summary,
      images: [{
                url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg', // Replace with actual default image if needed
                width: 1200,
                height: 630,
             }],
    },
     twitter: {
         title: `What is ${data?.data?.title} | TradeReply Education`,
         description: data?.data?.summary,
         site: '@JoinTradeReply',
         images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'], // Replace with actual default image if needed
       },
       icons: {
                icon: [
                  {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
                    type: "image/x-icon",
                  },
                  {
                    url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
                    type: "image/svg+xml",
                  },
                ],
              },
  };
}

export default async function EducationDetail({ params }) {
  noStore();

  const resolvedParams = await params;
  const detailSlug = resolvedParams.detail;

  const data = await fetchEducationDetail(detailSlug);
  const articleData = data.data;


  return (
    <HomeLayout>
      <Container>
        {/* JSON-LD Article Schema Only */}
        <Script id="ld-json-article" type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Article",
            "mainEntityOfPage": {
              "@type": "WebPage",
              "@id": `https://www.tradereply.com/education/${detailSlug}`
            },
            "headline": articleData.title,
            "description": articleData.summary,
            "author": {
              "@type": "Organization",
              "name": "TradeReply"
            },
            "publisher": {
              "@type": "Organization",
              "name": "TradeReply",
              "logo": {
                "@type": "ImageObject",
                "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png"
              }
            },
            "datePublished": new Date(articleData.created_at).toISOString(),
            "dateModified": new Date(articleData.updated_at || articleData.created_at).toISOString(),
            "articleSection": "Education",
            "articleBody": articleData.articleBody || ""
          })}
        </Script>
        <EducationContent
          detailSlug={detailSlug}
          articleData={data.data}
          nextArticle={data.next_article}
          avgProgress={data.avgProgress}
        />
      </Container>
    </HomeLayout>
  );
}
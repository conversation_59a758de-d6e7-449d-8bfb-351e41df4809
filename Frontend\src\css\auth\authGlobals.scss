@use "../theme/var";

.loginCommon {
  .referralCol {
    overflow-y: auto;
    overflow-x: clip;
    height: 100vh;
    width: 603px;

    @media (min-width: 1400px) {
      width: 45%;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .loginCol {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;

    @media (min-width: 992px) {
      width: calc(100% - 603px);
    }

    @media (min-width: 1400px) {
      width: 55%;
    }

    @media (max-width: 991px) {
      min-height: 100vh;
    }
  }

  &_rightSide {
    padding: 1rem 1rem 1rem;
    width: 100%;

    @media (max-width: 991px) {
      padding: 3rem 1rem 1.5rem;
    }

    &_inner {
      max-width: 553px;
      margin: 0 auto;
    }

    &_formBox {
      width: 100%;
      background-color: var.$cardbg;
      border-radius: 50px;
      padding: 2.5rem 4.35rem 2.5rem;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(4px);

      @media (max-width: 991px) {
        padding: 2rem 1rem 2rem;
      }

      &::after {
        content: "";
        position: absolute;
        top: 0px;
        left: -90px;
        width: 100%;
        height: 100%;
        background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-for-stock-traders.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        z-index: -1;
      }
    }

    .thirdParty_login {
      margin-top: 1.25rem;

      &_btn {
        background-color: rgba(255, 255, 255, 0.9);
        width: 30px;
        height: 30px;
        border-radius: 0.625rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 1rem;
        border: 0;
        transition: all ease-in-out 0.3s;

        &:last-child {
          margin-right: 0;
        }

        &:hover {
          background-color: var.$white;
        }
      }
    }

    .orLine {
      margin: 1.25rem 0;
      text-align: center;
      position: relative;

      span {
        color: var.$white;
        display: inline-block;
        margin: 0 auto;
      }

      &::after,
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 30%;
        height: 1px;
        background-color: var.$borderclr;

        @media (max-width: 575px) {
          width: 25%;
        }
      }

      &::before {
        left: auto;
        right: 0;
      }
    }
  }

  .forgot_form {
    .orLine {

      &::after,
      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 20%;
        height: 1px;
        background-color: var.$borderclr;

        @media (max-width: 575px) {
          width: 15%;
        }
      }

      &::before {
        left: auto;
        right: 0;
      }
    }
  }

  .loginHeading {
    h1 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var.$white;
      margin-bottom: 6px;
      text-align: center;

      @media (max-width: 991px) {
        font-size: 1rem;
      }
    }
  }

  .Forgotpassoword {
    margin-top: -1rem;

    a {
      font-weight: 700;
      color: #35c7ff;

      @media (max-width: 1199px) {
        font-size: 1rem;
      }

      &:hover {
        color: var.$baseclr;
        opacity: 0.8;
      }
    }
  }

  .anAccount {
    h6 {
      font-size: 16px;
      font-weight: 600;
      line-height: 26px;
      text-align: center;
      color: var.$baseclr;
      max-width: 266px;
      margin: 0 auto;
    }
  }

  .signup_form .anAccount {
    h6 {
      color: var.$lightgreyclr;
    }
  }

  .login_footer {
    &_links {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;

      a {
        color: var.$clrc5c5d5;
        font-size: 15px;
        line-height: 12px;
        font-weight: 400;
        text-transform: uppercase;
        border-right: 1px solid var.$textclr;
        padding: 0 0.625rem;

        @media (max-width: 991px) {
          font-size: 14px;
          padding: 0 0.625rem;
          margin-top: 0.5rem;
        }

        &:last-child {
          border-right: 0;
        }

        &:hover {
          color: var.$baseclr;
        }
      }

      p {
        font-size: 18px;
        font-weight: 400;
      }
    }
  }

  .backbtn {
    margin-bottom: 20px;

    a {
      display: flex;
      align-items: center;
      font-size: 1.125rem;
      font-weight: 600;
      color: var.$white;

      img,
      svg {
        transform: rotate(180deg);
        margin-right: 10px;
        transition: all ease-in-out 0.3s;
      }

      &:hover {
        color: var.$baseclr;

        img,
        svg {
          margin-right: 12px;

          path {
            fill: var.$baseclr;
          }
        }
      }
    }
  }
}

.authEmailBg {
  background-color: #011132;
  display: flex;
  justify-content: center;
  margin: 20px 0;

  &_authContainer {
    width: 650px;
    padding: 20px;

    img {
      height: 80px;
      margin-bottom: 15px;
    }

    &_inner {
      background-color: #1F2A3E;
      width: 100%;
      padding: 25px 15px;
      border-radius: 10px;
      border: 1px solid #ffffff50;

      p {
        font-size: 14px;
        margin-bottom: 15px;

        a {
          margin-bottom: 0 !important;
        }
      }

      a {
        margin-bottom: 15px;

        button {
          background-color: #00adef;
          padding: 10px 25px;
          color: #fff;
          border-radius: 6px;
          font-size: 24px;
          font-weight: 600;
        }
      }

      &_user {
        p:first-child {
          margin-bottom: 0px !important;
        }

        span {
          color: #2EC735;
        }
      }
    }

    &_outer {
      margin-top: 20px;

      p {
        font-size: 14px;
        ;
        text-align: center;
        margin-bottom: 5px;
      }
    }
  }
}

.authCorrectIcon {
  position: relative;

  .checkIcon {
    position: absolute;
    left: -40px;
    top: 18px;
  }

  svg {
    height: 22;
  }
}

.invalid_credential {
  display: flex;
  gap: 10px;
  align-items: center;
  border: 1px solid #ff696a !important;
  background-color: #1E222D;
  width: fit-content;
  padding: 10px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
}

.signup_invalid_credential {
  border: 1px solid #ff696a !important;
  background-color: #1E222D;
  width: fit-content;
  padding: 10px;
  border-radius: 10px;

  p,
  span {
    font-size: 14px;

    a {
      font-size: 16px !important;
      font-weight: 700;
    }
  }

}

.session-expire-message {
  background-color: #ffa9a9;
  color: #000;
  border-radius: 2rem;
  padding: 10px 30px;
  text-align: center;
  font-weight: 600;
  width: fit-content;
  margin-bottom: 10px;
}
"use client";
import { Col, Row } from "react-bootstrap";
import React from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  FollowersIcon,
  EyeDarkInsightIcon,
  ShareLightStrIcon,
} from "@/assets/svgIcons/SvgIcon";
import "@/css/account/MarketPlace.scss";

export default function MarketInsight() {
  const insignArray = [
    {
      first: "Last 7 days",
      second: "35 Clicks on listings",
      third: "$20 in sales",
      fourth: "10 Listing shares",
      five: "20 New Followers",
      six: "20 Lost Followers",
    },
    {
      first: "Last 14 days",
      second: "35 Clicks on listings",
      third: "$20 in sales",
      fourth: "10 Listing shares",
      five: "20 New Followers",
      six: "20 Lost Followers",
    },
    {
      first: "Last 30 days",
      second: "35 Clicks on listings",
      third: "$20 in sales",
      fourth: "10 Listing shares",
      five: "20 New Followers",
      six: "20 Lost Followers",
    },
  ];

  return (
    <>
      <CommonWhiteCard title="Marketplace Insights" className="account_card">
        <div className="account_card_insight">
          {insignArray.map((item) => (
            <Row className="mb-1 mb-lg-0">
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <p>{item.first}</p>
                </div>
              </Col>
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <EyeDarkInsightIcon />
                  <p>{item.second}</p>
                </div>
                <p></p>
              </Col>
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <EyeDarkInsightIcon />
                  <p>{item.third}</p>
                </div>
              </Col>
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <ShareLightStrIcon />
                  <p>{item.fourth}</p>
                </div>
              </Col>
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <FollowersIcon />
                  <p>{item.five}</p>
                </div>
              </Col>
              <Col className="px-1" xs={4} md={2}>
                <div className="wrap_div">
                  <FollowersIcon />
                  <p>{item.six}</p>
                </div>
              </Col>
            </Row>
          ))}
        </div>
      </CommonWhiteCard>
    </>
  );
}

<?php

namespace App\Services;

use App\Models\TradeBuilder;
use App\Repositories\TradeBuilderRepository;

class TradeBuilderService
{
    public function __construct(protected TradeBuilderRepository $tradeBuilderRepository)
    {
    }

    /**
      * Get both entry & exit fields
     */
    public function getEntryAndExitFields()
    {
        return [
          'entry_overview'   => $this->tradeBuilderRepository->getEntryOverviewFields(),
          'exit_overview'    => $this->tradeBuilderRepository->getExitOverviewFields(),
          'entry_projection' => $this->tradeBuilderRepository->getEntryProjectionFields(),
          'exit_projection'  => $this->tradeBuilderRepository->getExitProjectionFields(),
          'entry_outcome'    => $this->tradeBuilderRepository->getEntryOutcomeFields(),
          'exit_outcome'     => $this->tradeBuilderRepository->getExitOutcomeFields(),
        ];
    }

    /**
     * Create a new trade and save the first entry form.
     */
    public function createTradeWithEntry(): TradeBuilder
    {
        return $this->tradeBuilderRepository->createTradeWithEntry();
    }

    /**
     * Create a new trade and save the first entry form.
     */
    public function applyIsEditable(array $item, array $formulaData): array
    {
        return $this->tradeBuilderRepository->applyIsEditable($item, $formulaData);
    }

    /**
     * Create a new trade and save the first entry form.
     * @throws \Exception
     */
    public function getTradeReplayFormulas(): array
    {
        return $this->tradeBuilderRepository->getTradeReplayFormulas();
    }
}

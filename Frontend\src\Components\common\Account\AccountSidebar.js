import {
  CartSideIcon,
  DollerIcon,
  LinkIcon,
  LockIcon,
  PaymentIcon,
  RightArrowIcon,
  SecurityIcon,
  BaseEyeIcon,
  UserBlueIcon,
  MarketplaceListIcon,
  MarketplaceDisputeIcon,
  SoldProductIcon,
  PurchasedProductIcon,
  SellerDashboardIcon,
  PublicProfileIcon,
} from "@/assets/svgIcons/SvgIcon";
import NavLink from "@/Components/UI/NavLink";
import "@/css/account/AccountSidebar.scss";

const AccountSidebarOptions = [
  {
    href: "/account/overview",
    label: "Account Overview",
    icon: <BaseEyeIcon />
  },
  {
    href: "/account/details",
    label: "Account Details",
    icon: <UserBlueIcon />
  },
  {
    href: "/account/subscriptions",
    label: "Subscriptions",
    icon: <DollerIcon />,
  },
  {
    href: "/account/security",
    label: "Security",
    icon: <SecurityIcon />
  },
  {
    href: "/account/privacy",
    label: "Privacy & Communication",
    icon: <LockIcon />,
  },
  {
    href: "/account/connections",
    label: "Connections",
    icon: <LinkIcon />
  },
  {
    href: "/account/payments",
    label: "Payment Methods",
    icon: <PaymentIcon />,
  },
  {
    href: "/account/transactions",
    label: "Transaction History",
    icon: <CartSideIcon />,
  },
];
const MarketPlaceSidebarOpt = [
  {
    href: "/account/public-profile",
    label: "Public Profile",
    icon: <PublicProfileIcon />
  },
  {
    href: "/account/seller-dashboard",
    label: "Seller Dashboard",
    icon: <SellerDashboardIcon />
  },
  {
    href: "/account/your-listings",
    label: "Marketplace Listings",
    icon: <MarketplaceListIcon />,
  },
  {
    href: "/account/your-disputes",
    label: "Marketplace Disputes",
    icon: <MarketplaceDisputeIcon />,
  },
  {
    href: "/account/sold-products",
    label: "Sold Products",
    icon: <SoldProductIcon />
  },
  {
    href: "/account/purchased-products",
    label: "Purchased Products",
    icon: <PurchasedProductIcon />,
  },
];

const AccountSidebar = ({ className, isActive, toggleClass }) => {
  return (
    <>
      <div
        onClick={toggleClass}
        className={isActive ? "d-xl-none sidebar_backdrop" : " d-xl-none"}
      />
      <div
        className={`Account_sidebar ${isActive ? "opensidebar" : ""} ${className}`}
      >
        <div className="Account_sidebar_head">
          <NavLink href="/" className="headLogo d-block d-xl-none ps-4">
            {/* <Logo /> */}
            <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg" alt="Brand Logo" />
          </NavLink>
          <div className="filter_toggle">
            <button
              onClick={toggleClass}
              className={"filter_toggle_btn " + (isActive ? "active" : "")}
            >
              <span></span>
            </button>
          </div>
        </div>
        <ul>
          {AccountSidebarOptions.map((item) => (
            <li key={item.href}>
              <NavLink href={item.href} className="d-flex gap-4">
                {item.icon}
                {item.label}
              </NavLink>
            </li>
          ))}
        </ul>
        <ul className="Account_sidebar_bottom_link pt-2 mt-2">
          {MarketPlaceSidebarOpt.map((item) => (
            <li key={item.href}>
              <NavLink href={item.href} className="d-flex gap-4">
                {item.icon}
                {item.label}
              </NavLink>
            </li>
          ))}
        </ul>
        <div className="Account_sidebar_bottom_link">
          <ul>
            <li>
              <NavLink href="/refer-a-friend">
                Refer a friend <RightArrowIcon />
              </NavLink>
            </li>
            <li>
              <NavLink href="#">
                Affiliate Center <RightArrowIcon />
              </NavLink>
            </li>
            <li>
              <NavLink href="#">
                Help Center <RightArrowIcon />
              </NavLink>
            </li>
          </ul>
        </div>
      </div>
    </>
  );
};

export default AccountSidebar;

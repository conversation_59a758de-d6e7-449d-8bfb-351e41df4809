
import React from "react";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

const CircularProgressbarWithChildren = ({ value, children }) => {
    return (
        <div style={{
            width: "100%",
            height: "100%",
            position: "relative"
        }}>
            <CircularProgressbar
                value={value}
                styles={buildStyles({
                    pathColor: `rgba(0, 173, 239, ${value / 100})`,
                    trailColor: "rgba(255, 255, 255, 0.2)",
                })}
            />
            <div
                style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    marginTop: "-65%",
                }}
            >
                {children}
            </div>
        </div >
    );
};

export default CircularProgressbarWithChildren;

<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->getCleanSlug(),
            'content' => $this->content,
            'type' => $this->type,
            'feature_image_url' => $this->feature_image_url ?? '',
            'feature_image' => $this->feature_image ?? '',
            'is_featured' => (bool) $this->is_featured,
            'summary' => $this->summary ?? '',
            'primary_category' => new CategoryResource($this->whenLoaded('primaryCategory')),
            'secondary_categories' => CategoryResource::collection($this->secondaryCategories),
            'created_at' => $this->created_at?->toDateTimeString() ?? '',
            'updated_at' => $this->updated_at?->toDateTimeString() ?? '',
            'progress' => (int) ($this->user_progress ?? 0),
        ];
    }
}

//import "../../../assets/theme/_var.scss";

import Link from "next/link";
const SidebarHeading = ({ title, text, Linktext, Linkicon, link = "#" }) => {
  return (
    <div className="sidebar_heading">
      <div className="sidebar_heading_top">
        <h2>{title}</h2>
        <div className="sidebar_heading_icon">
          <Link href={link}>
            <button className="d-flex align-items-center">
              {Linktext}
              {Linkicon && <span className="ms-2">{Linkicon}</span>}
            </button>
          </Link>
        </div>
      </div>
      {text && <p>{text}</p>}
    </div>
  );
};

export default SidebarHeading;

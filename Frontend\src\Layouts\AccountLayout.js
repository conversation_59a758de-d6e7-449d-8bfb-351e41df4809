"use client";
import 'bootstrap/dist/css/bootstrap.min.css';
import { Fragment, useEffect, useState } from "react";
import Header from "@/Components/UI/Header";
import Footer from "@/Components/UI/Footer";
import AccountSidebar from "@/Components/common/Account/AccountSidebar";
import "@/css/account/AccountLayout.scss";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

const AccountLayout = ({ children }) => {
  const [isActive, setActive] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const toggleClass = () => {
    setActive(!isActive);
  };

  useEffect(() => {
    const token = Cookies.get("authToken");
    if (!token) {
      router.replace("/login");
    } else {
      setIsLoading(false);
    }
  }, [router]);

  if (isLoading) return null;

  return (
    <Fragment>
      <Header />
      <main className="Account_layout">
        <div className="Account_layout_main">
          <div className="Account_layout_leftaside">
            <AccountSidebar
              onclick={toggleClass}
              isActive={isActive}
              toggleClass={toggleClass}
            />
          </div>
          <div className="Account_layout_rightaside">
            <div className="filter_toggle">
              <button
                onClick={toggleClass}
                className={"filter_toggle_btn " + (isActive ? "active" : "")}
              >
                <span></span>
              </button>
            </div>
            {children}
          </div>
        </div>
      </main>
      {/* <Footer /> */}
    </Fragment>
  );
};

export default AccountLayout;

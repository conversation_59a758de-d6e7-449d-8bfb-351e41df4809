"use client";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import { RightArrowIconSvg, ProfileUserDarkIcon } from "@/assets/svgIcons/SvgIcon";
import { usePathname } from "next/navigation";
import Link from "next/link";

export default function FollowersList() {
  const pathname = usePathname();
  const linkProps =
    pathname !== "/account/followers"
      ? {
        Linktext: "View all",
        Linkicon: <RightArrowIconSvg />,
        link: "/account/followers",
      }
      : null;

  const [followerList, setFollowerList] = useState([
    { name: "<PERSON>" },
    { name: "<PERSON>" },
    { name: "<PERSON>" },
  ]);
  return (
    <CommonWhiteCard
      title="Followers (3,684)"
      {...linkProps}
      className="account_card"
    >
      <div className="account_card_followers">
        {followerList.map((data, index) => (
          <div className="main_inform" key={index}>
            <div className="profile_photo">
              <ProfileUserDarkIcon />
            </div>
            <Link href={`/@${data.name}`} legacyBehavior passHref>
              <a
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: "#00adef",
                }}
              >
                <h6>{data.name}</h6>
              </a>
            </Link>
          </div>
        ))}
      </div>
    </CommonWhiteCard>
  );
}

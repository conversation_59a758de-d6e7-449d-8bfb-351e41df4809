<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('portfolio_field_definitions', function (Blueprint $table) {
            $table->boolean('has_formula')->after('account_field_placeholder')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('portfolio_field_definitions', function (Blueprint $table) {
            $table->dropColumn('has_formula');
        });
    }
};

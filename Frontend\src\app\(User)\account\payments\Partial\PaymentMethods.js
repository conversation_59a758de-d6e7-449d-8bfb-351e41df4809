'use client';
import { Col } from "react-bootstrap";
import React, { useState } from 'react'
import { CheckIcon, RemoveIconSvg, PlusIconSvg, EditIconSvg } from "@/assets/svgIcons/SvgIcon";
import CommonTable from "@/Components/UI/CommonTable";
import Link from "next/link";

export default function PaymentMethods() {
    const paymentMethods = [
        {
            id: 1,
            card_type: 'Visa',
            card_number: '1122 3344 5566 7788',
            text: 'Payment Method',
            isDefault: true,
        },
        {
            id: 2,
            card_type: 'Master',
            card_number: '1122 3344 5566 7788',
            text: 'Payment Method',
            isDefault: false,
        },
    ];
    return (
        <>
            <Col lg={12} xs={12} className="d-flex mb-4 mb-lg-4 removePadding">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Your Payment Methods</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href='/security-check' prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <span className="me-2"><PlusIconSvg /></span>
                                    Add a new Payment Method
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_table">
                            <CommonTable fields={false} className="simple_table">
                                {[...paymentMethods]
                                    .sort((a, b) => (b.isDefault === true) - (a.isDefault === true))
                                    .map((payment) => (
                                        <tr key={payment.id}>
                                            <td>
                                                {payment.isDefault ? (
                                                    <div className="d-flex align-items-center gap-2">
                                                        <CheckIcon />
                                                        <span className="green_text">Default</span>
                                                    </div>
                                                ) : (
                                                    <Link href='/security-check' prefetch={true}>
                                                        <button>
                                                            <span className='text_00ADEF'>Set as Default</span>
                                                        </button>
                                                    </Link>
                                                )}
                                            </td>
                                            <td>
                                                <h6>{payment?.card_type}
                                                    <span className="ps-2">{payment?.card_number?.slice(-4)}</span>
                                                </h6>
                                                <p className="mt-2">{payment?.text}</p>
                                            </td>
                                            <td>
                                                <div className="flex justify-end gap-4">
                                                    {!payment.isDefault && (
                                                        <Link href='/security-check' prefetch={true}>
                                                            <button className="blue_text_btn d-flex align-items-center">
                                                                <RemoveIconSvg /> Remove
                                                            </button>
                                                        </Link>
                                                    )}
                                                    <Link href='/security-check' prefetch={true}>
                                                        <button className="blue_text_btn d-flex align-items-center">
                                                            <EditIconSvg /> Edit
                                                        </button>
                                                    </Link>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                            </CommonTable>
                        </div>
                    </div>
                </div>
            </Col>
        </>
    )
}

"use client";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";

export default function UserAbout({ user }) {
  const websiteData = user.website || "";

  return (
    <CommonWhiteCard
      title="About"
      className="account_card"
    >
      <div className="account_card_about">
        <p className="para_desc">{user.about}</p>
        <a href={websiteData.startsWith("http") ? websiteData : `https://${websiteData}`} legacyBehavior passHref
          target="_blank"
          rel="noopener noreferrer"
          className="website-link"
        >
          {websiteData}
        </a>
      </div>
    </CommonWhiteCard >
  );
}

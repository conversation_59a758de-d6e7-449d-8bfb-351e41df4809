// 'use client';

// import Category from "@/app/(Home)/category/page";
// import { useParams } from "next/navigation";


// const CategoryPage = () => {
//     const params = useParams();
//     const page = params.id;
    
//     return(
//         <>
//         <Category pages={page} />
//         </>
//     )
// }

// export default CategoryPage;
import CategoryPage from "../../page"; // ✅ this is allowed if done right

export default function EducationDynamicPageWrapper({ params, searchParams }) {
  return <CategoryPage params={params} searchParams={searchParams} />;
}
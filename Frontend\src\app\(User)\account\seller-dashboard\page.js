"use client";
import { Col, Row } from "react-bootstrap";
import React from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import MarketInsight from "@/Components/common/Account/SellerDashboard/MarketInsight";
import OpenDispute from "@/Components/common/Account/SellerDashboard/OpenDispute";
import MarketFeedBack from "@/Components/common/Account/SellerDashboard/MarketFeedBack";
import ActiveListings from "@/Components/common/Account/SellerDashboard/ActiveListings";
import DraftListings from "@/Components/common/Account/SellerDashboard/DraftListings";
import EndedListings from "@/Components/common/Account/SellerDashboard/EndedListings";
import { PlusIcon } from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function SellerDashboard() {
  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Seller Dashboard" />
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex">
              <MarketInsight />
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex">
              <OpenDispute />
            </Col>
          </Row>

          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex">
              <MarketFeedBack />
            </Col>
          </Row>
          <Link className="w-100" href="/account/create-listing">
            <CommonButton
              title="Create New Listing"
              onlyIcon={<PlusIcon />}
              className="w-100 mb-md-4 mb-0"
            />
          </Link>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <ActiveListings />
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <DraftListings />
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <EndedListings />
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

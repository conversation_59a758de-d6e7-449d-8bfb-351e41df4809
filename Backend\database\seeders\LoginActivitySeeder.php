<?php

namespace Database\Seeders;

use App\Models\LoginActivity;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LoginActivitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (you can modify this to target a specific user)
        $user = User::first();

        if (!$user) {
            $this->command->info('No users found. Please create a user first.');
            return;
        }

        $activities = [
            [
                'user_id' => $user->id,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'device_type' => 'Desktop',
                'browser' => 'Chrome',
                'platform' => 'Windows',
                'city' => 'Miami',
                'country' => 'United States',
                'session_token' => 'sample_token_1',
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => $user->id,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                'device_type' => 'Mobile',
                'browser' => 'Safari',
                'platform' => 'iOS',
                'city' => 'New York',
                'country' => 'United States',
                'session_token' => 'sample_token_2',
                'logged_out_at' => now()->subHours(2),
                'created_at' => now()->subDays(2),
                'updated_at' => now()->subHours(2),
            ],
            [
                'user_id' => $user->id,
                'ip_address' => '*************',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'device_type' => 'Desktop',
                'browser' => 'Chrome',
                'platform' => 'Mac OS X',
                'city' => 'San Francisco',
                'country' => 'United States',
                'session_token' => 'sample_token_3',
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
        ];

        foreach ($activities as $activity) {
            LoginActivity::create($activity);
        }

        $this->command->info('Login activities seeded successfully!');
    }
}

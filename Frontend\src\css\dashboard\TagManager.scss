@use "../theme/var" as *;

.tag_manager {
    .trade_head {
        display: flex;
        align-items: center;
        // margin-bottom: 30px;
        position: relative;

        @media screen and (max-width: 991px) {
            justify-content: unset;
        }

        .common_heading {
            h2 {
                @media screen and (max-width: 1599px) {
                    font-size: 35px !important;
                }

                @media screen and (max-width: 1279px) {
                    font-size: 28px !important;
                    margin-top: 20px;
                }
            }
        }

        &_title {
            display: flex;
            flex-wrap: wrap;

            @media screen and (max-width: 767px) {
                justify-content: center;
                margin-top: 20px;
            }

            h4 {
                margin-right: 10px;
                margin-bottom: 10px;

                @media screen and (max-width: 1599px) {
                    font-size: 18px;
                    line-height: normal;
                }
            }
        }

        .head_draft {

            &_icons {
                display: flex;
                align-items: center;
                gap: 5px;
                margin-bottom: 5px;

                svg {
                    width: 20px;
                    height: 20px;
                }
            }
        }
    }

    .create_new_tag {
        .tags_fields {
            margin-bottom: 1.75rem;
        }

        label {
            font-size: 20px;
            font-weight: 600;
            color: #fff;
        }

        input,
        textarea {
            font-size: 24px;
            font-weight: 600;
            background-color: #FFFFFF33;
            color: #FFFFFF33;
        }

        button {
            text-transform: uppercase;
            font-size: 28px;
            font-weight: 600;
        }
    }

    .new_tag {
        &_actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action_btns {
            display: flex;
            gap: 10px;

            .common_select,
            select {
                width: 170px;
                height: 55px;
                border-radius: 15px;
                padding: 10px 20px;
                font-size: 16px;
                font-weight: 600;
                color: #031940;
            }

            button {
                min-height: 55px !important;
                border-radius: 15px;
                padding: 10px 25px;
                min-width: fit-content !important;
            }

            .search {
                width: 400px;
                min-height: 55px !important;
                border-radius: 15px;
                padding: 5px 15px;
                background-color: #FFFFFF4D;
                display: flex;
                align-items: center;
                gap: 10px;

                .customInput {
                    margin-bottom: 0 !important;
                    width: 100%;
                }

                input {
                    background-color: transparent;
                    color: #fff;
                    font-size: 20px;
                    font-weight: 600;
                    min-height: fit-content;
                    padding: 0;
                    box-shadow: none;
                    outline: none;
                    width: 100%;
                    border: none !important;
                }
            }
        }

        .no_of_tags {
            font-size: 24px;
            font-weight: 600;
        }
    }
}

::placeholder {
    color: #fff;
    opacity: 1;
}

::-ms-input-placeholder {
    color: #fff;
}

.new_tag_table {
    max-height: 400px;
    overflow-y: auto;
    overflow-x: auto;

    thead th {
        position: sticky;
        top: 0px;
    }

    table thead tr th {
        background-color: #031940;
        color: #fff;
        padding: 20px;
    }


    thead tr th:first-child {
        border-radius: 10px 0px 0px 0px;
        width: 60px;
    }

    thead tr th:last-child {
        border-radius: 0px 10px 0px 0px;
        width: 60px;
    }

    tbody tr td {
        padding: 8px 20px;
        font-weight: 600;
        color: #00ADEF;
    }

    tbody tr:last-child td {
        border: none !important;
    }

    tbody tr:last-child td:first-child {
        border-radius: 0px 0px 0px 10px;
    }

    tbody tr:last-child td:last-child {
        border-radius: 0px 0px 10px 0px;
    }

    tbody tr td:nth-child(3) {
        color: #000000;
    }
}

.new_tag_table::-webkit-scrollbar {
    display: none;
}

@media only screen and (width <=1200px) {
    .tag_manager {
        .new_tag {
            .action_btns .search {
                width: 250px;
            }
        }
    }
}

@media only screen and (width > 600px) {
    .tag_manager {
        .new_tag {
            &_actions {
                &_mobile {
                    display: none !important;
                }
            }
        }
    }
}

@media only screen and (width <=600px) {
    .tag_manager {
        .new_tag {
            &_actions {
                display: none !important;



                &_mobile {
                    display: block;

                    .action_btns {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        select {
                            text-align: center;
                        }

                        .no_of_tags {
                            font-size: 18px !important;
                        }
                    }

                    .search {
                        width: 100%;
                        min-height: 55px !important;
                        border-radius: 15px;
                        padding: 5px 15px;
                        background-color: #FFFFFF4D;
                        display: flex;
                        align-items: center;
                        gap: 10px;

                        .customInput {
                            margin-bottom: 0 !important;
                        }

                        input {
                            background-color: transparent;
                            color: #fff;
                            font-size: 20px;
                            font-weight: 600;
                            min-height: fit-content;
                            padding: 0;
                            box-shadow: none;
                            outline: none;
                            width: 100%;
                            border: none !important;
                        }
                    }

                }
            }
        }

        .create_new_tag {
            label {
                font-size: 16px;
            }

            input,
            textarea {
                font-size: 15px;
            }


            button {
                font-size: 16px;

                .onlyIcon {
                    margin-right: 7px;
                }

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .trade_head {
            .head_draft_icons {
                justify-content: center;

                svg,
                img {
                    width: 20px !important;
                    height: 20px !important;
                }
            }

        }
    }

}
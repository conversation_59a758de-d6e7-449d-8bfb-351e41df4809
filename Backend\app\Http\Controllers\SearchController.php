<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use App\Models\Blog;
use App\Models\Category;
use App\Models\Education;
use App\Http\Resources\BlogResource;
use App\Http\Resources\EducationResource;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;


class SearchController extends Controller
{
    use ApiResponseTrait;

    //
    public function home(Request $request, $page = null){

        $key = $request->input('key', null);
        $perpage = $request->input('perpage', 5);

        $articles = DB::table('articles')
            ->select(
                'articles.id', 'articles.title', 'articles.summary', 'articles.slug', 'articles.type',
                DB::raw("CASE
                WHEN articles.title = ? THEN 100
                WHEN articles.title LIKE ? THEN 90
                WHEN articles.title LIKE ? THEN 70
                WHEN categories.title = ? THEN 60
                WHEN categories.title LIKE ? THEN 50
                WHEN articles.summary LIKE ? THEN 40
                WHEN articles.summary LIKE ? THEN 35
                ELSE 0
            END as relevance")
            )
            ->leftJoin('categories', 'articles.primary_category_id', '=', 'categories.id')
            ->addBinding([$key, "{$key}%", "%{$key}%", $key, "%{$key}%", "{$key}%", "%{$key}%"], 'select')
            ->when(!empty($key), function ($q) use ($key) {
                return $q->where(function ($query) use ($key) {
                    $query->where('articles.title', 'LIKE', "%{$key}%")
                        ->orWhere('articles.summary', 'LIKE', "%{$key}%")
                        ->orWhere('categories.title', 'LIKE', "%{$key}%");
                });
            })
            ->orderByDesc('relevance')
            ->take($perpage)
            ->get()
            ->map(function ($article) {
                $article->slug = preg_replace('/-\b' . preg_quote($article->type, '/') . '$/', '', $article->slug);
                return $article;
            });


        return $this->successResponse($articles,'Articles retrieved successfully');

    }

    public function Search(Request $request, $page = null)
    {
        $key = $request->input('key', null);
        $perPage = (int) $request->input('perpage', 10);
        $currentPage = $page ?? $request->query('page', 1);

        $query = DB::table('articles')
            ->select(
                'articles.id',
                'articles.title',
                'articles.summary',
                'articles.slug',
                'articles.type'
            )
            ->leftJoin('categories', 'articles.primary_category_id', '=', 'categories.id');

        if (!empty($key)) {
            $query->addSelect(DB::raw("CASE
            WHEN articles.title = ? THEN 100
            WHEN articles.title LIKE ? THEN 90
            WHEN articles.title LIKE ? THEN 80
            WHEN categories.title = ? THEN 70
            WHEN categories.title LIKE ? THEN 60
            WHEN articles.summary LIKE ? THEN 50
            WHEN articles.summary LIKE ? THEN 40
            ELSE 0
        END AS relevance"));

            $query->addBinding([
                $key, "{$key}%", "%{$key}%",
                $key, "%{$key}%",
                "{$key}%", "%{$key}%"
            ], 'select');

            $query->where(function ($q) use ($key) {
                $q->where('articles.title', 'LIKE', "%{$key}%")
                    ->orWhere('articles.summary', 'LIKE', "%{$key}%")
                    ->orWhere('categories.title', 'LIKE', "%{$key}%");
            });

            $query->orderByDesc('relevance');
        }

        $totalRecords = $query->count();

        $articles = $query
            ->forPage($currentPage, $perPage)
            ->get()
            ->map(function ($article) {
                $article->slug = preg_replace('/-\b' . preg_quote($article->type, '/') . '$/', '', $article->slug);
                return $article;
            });


        $articlesPaginated = new \Illuminate\Pagination\LengthAwarePaginator(
            $articles,
            $totalRecords,
            $perPage,
            $currentPage,
            ['path' => $request->url(), 'query' => $request->query()]
        );
        return $this->successResponse([
            'articles' => $articlesPaginated,
            'meta' => [
                'total' => ceil($totalRecords / $perPage),
                'current_page' => $articlesPaginated->currentPage(),
                'next_page' => $articlesPaginated->nextPageUrl(),
                'prev_page' => $articlesPaginated->previousPageUrl(),
                'canonical_url' => url("/article/page/{$articlesPaginated->currentPage()}"),
            ]
        ], 'Articles retrieved successfully');


    }

}

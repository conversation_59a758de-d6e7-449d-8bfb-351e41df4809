<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Plan;
use Illuminate\Http\Request;
use App\Models\BillingDetail;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Customer;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $userId = $request->input('user_id');
        $billing = null;
        $clientSecret = null;

        $orders = Order::with('plan')
            ->when($userId !== 'null', function ($query) use ($userId) {
                return $query->where('user_id', $userId);
            })
            ->where('status', 'pending')
            ->latest()
            ->get();

        if ($userId !== 'null') {
            $billing = BillingDetail::with('user')
                ->where('user_id', $userId)
                ->first();

            if ($billing && $billing->payment_method_id && $billing->user->stripe_customer_id) {
                $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
                $intent = $stripe->setupIntents->create([
                    'customer' => $billing->user->stripe_customer_id,
                    'payment_method' => $billing->payment_method_id,
                ]);
                $clientSecret = $intent->client_secret;
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => $orders,
            'billing' => $billing,
            'clientSecret' => $clientSecret,
        ]);
    }

    public function storeSubscriptionOrder(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:plans,id',
            'is_trial' => 'nullable|boolean'
        ]);

        $userId = $request->user_id;

        $plan = Plan::find($request->plan_id);

        if (!$plan || !$plan->stripe_price_id) {
            return response()->json(['error' => 'Invalid plan or missing Stripe price ID'], 400);
        }

        $price = $plan->billing_type === 'yearly'
                    ? $plan->price * 12
                    : $plan->price;

        Order::create([
            'user_id'      => $userId != 'null' ? $userId : null,
            'plan_id'      => $plan->id,
            'order_type'   => 'subscription',
            'status'       => 'pending',
            'billing_type' => $plan->billing_type,
            'price'        => $price,
            'is_free_subscription' => $request->boolean('is_trial'),
        ]);

        return response()->json(['message' => 'ok'], 200);
    }

    public function destroy(Order $order)
    {
        $order->delete();
        return response()->noContent(200);
    }

    public function storeBillingInfoAndChargePayment(Request $request)
    {
        $validated = $request->validate([
            'firstName'     => 'required|string|max:255',
            'lastName'      => 'required|string|max:255',
            'country'       => 'required|string|max:255',
            'address'       => 'required|string|max:500',
            'payment_method_id' => 'required|string',
        ]);

        Stripe::setApiKey(config('services.stripe.secret'));

        $user = auth()->user();

        $this->getOrCreateCustomer($user);

        $checkout = BillingDetail::updateOrCreate(
            ['user_id' => $user->id],
            [
                'first_name'    => $validated['firstName'],
                'last_name'     => $validated['lastName'],
                'country'       => $validated['country'],
                'address'       => $validated['address'],
                'payment_method_id' => $validated['payment_method_id'],
            ]
        );

        $user->refresh();

        $order = Order::whereNull('user_id')
            ->where('order_type', 'subscription')
            ->where('status', 'pending')
            ->latest()
            ->first();

        if ($order) {
            $order->update(['user_id' => $user->id]);
        } else {
            $order = Order::where('user_id', $user->id)
                ->where('order_type', 'subscription')
                ->where('status', 'pending')
                ->latest()
                ->first();
        }

        $order->refresh();

        if ($order) {
            $response = $this->chargeStripeSubscription($user, $order);
            $type     = $response['type'] ?? 'error';
            $message  = $response['message'] ?? 'Unexpected error occurred.';

            if ($type === 'error') {
                return response()->json([
                    'type'    => $type,
                    'message' => $message
                ], 400);
            }
        }

        return response()->json(['status' => 'success', 'data' => $checkout]);
    }

    private function chargeStripeSubscription(User $user, Order $order)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        if (!$order || !$order->plan_id) {
            return [
                'type'    => 'error',
                'message' => 'No valid pending subscription order found.'
            ];
        }

        $plan = Plan::find($order->plan_id);
        if (!$plan || !$plan->stripe_price_id) {
            return [
                'type'    => 'error',
                'message' => 'Invalid plan or missing Stripe price ID.'
            ];
        }

        $billing = $user->billingDetail;
        if (!$billing || !$billing->payment_method_id) {
            return [
                'type'    => 'error',
                'message' => 'No saved payment method found.'
            ];
        }

        try {
            $customerId = $this->getOrCreateCustomer($user);

            $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));

            $stripe->paymentMethods->attach(
                $billing->payment_method_id,
                ['customer' => $customerId]
            );

            $stripe->customers->update($customerId, [
                'invoice_settings' => [
                    'default_payment_method' => $billing->payment_method_id,
                ],
            ]);

            if ($order->is_downgrade) {
                $activeSub = $user->activeSubscription;
                if (!$activeSub || !$activeSub->stripe_subscription_id) {
                    return [
                        'type' => 'error',
                        'message' => 'No active subscription to downgrade.'
                    ];
                }

                $subscription = $stripe->subscriptions->retrieve($activeSub->stripe_subscription_id);

                $stripe->subscriptions->update($subscription->id, [
                    'items' => [[
                        'id'    => $subscription->items->data[0]->id,
                        'price' => $plan->stripe_price_id,
                    ]],
                    'proration_behavior' => 'none',
                ]);

                $activeSub->update([
                    'plan_id' => $plan->id,
                ]);

                $order->update(['status' => 'completed']);

                return [
                    'type'    => 'success',
                    'message' => 'Plan downgraded successfully.'
                ];
            }

            $subscriptionParams = [
                'customer' => $customerId,
                'items' => [[
                    'price' => $plan->stripe_price_id,
                    'quantity' => 1,
                ]],
                'expand' => ['latest_invoice.payment_intent'],
            ];

            if ($order->is_free_subscription) {
                $subscriptionParams['trial_period_days'] = 30;
                $subscriptionParams['trial_settings'] = [
                    'end_behavior' => ['missing_payment_method' => 'cancel'],
                ];
            }

            $subscription = $stripe->subscriptions->create($subscriptionParams);

            $order->update(['status' => 'completed']);

            UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'deactive']);

            UserSubscription::updateOrCreate(
                ['stripe_subscription_id' => $subscription->id],
                [
                    'user_id'     => $user->id,
                    'plan_id'     => $plan->id,
                    'is_trial'    => $order->is_free_subscription,
                    'status'      => 'active',
                    'starts_at'   => Carbon::createFromTimestamp($subscription->current_period_start),
                    'ends_at'     => Carbon::createFromTimestamp($subscription->current_period_end),
                ]
            );

            return [
                'type'    => 'success',
                'message' => 'Subscription charge successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Stripe subscription error: ' . $e->getMessage());
            return [
                'type'    => 'error',
                'message' => 'Subscription failed: ' . $e->getMessage()
            ];
        }
    }
    private function getOrCreateCustomer(User $user): string
    {
        if ($user->stripe_customer_id) {
            return $user->stripe_customer_id;
        }

        $customer = Customer::create([
            'email' => $user->email,
            'name'  => $user->name,
        ]);

        $user->stripe_customer_id = $customer->id;
        $user->save();

        return $customer->id;
    }
}

"use client";
import { Col, Row, Dropdown } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  TripleDotsMenu,
  BlackShareIcon,
  StaticListingImg,
  OpenNewtabIcon,
  DeleteDarkIcon,
  RenameIcon,
  DropArrowIcon,
  DropArrowUpIcon,
} from "@/assets/svgIcons/SvgIcon";

export default function ActiveListings() {
  const containerRef = useRef(null);
  const [openIndex, setOpenIndex] = useState(false);
  const [isActiveExp, setIsActiveExp] = useState(true);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target)
      ) {
        // setOpenIndex(null);
        setOpenIndex(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleDropdown = () => {
    // setOpenIndex((prev) => (prev === index ? null : index));
    setOpenIndex((prev) => !prev);
  };
  return (
    <CommonWhiteCard
      title="Active Listings"
      Linkicon={
        <div
          className="account_card_btnArrow"
          onClick={() => setIsActiveExp((prev) => !prev)}
        >
          {isActiveExp ? <DropArrowUpIcon /> : <DropArrowIcon />}
        </div>
      }
      className="account_card"
    >
      {isActiveExp && (
        <div className="account_card_dash_listings">
          <div className="main_inform justify-between">
            <p className="most_recent">3 of 15</p>
            <span className="most_recent text-end">
              Sorted: Recently Created
            </span>
          </div>
          <div className="mini_card">
            <div className="main_inform respon_sell_feedback">
              <div className="activeListing_photo">
                <StaticListingImg />
              </div>
              <div className="w-100">
                <h6>Mastering the stock market</h6>
                <p className="inner_price_text">$11.95 - Listed on 1/19/25</p>
                <p className="inner_price_text">
                  65 clicks on listing since listed
                </p>
                <div className="actions_btn">
                  <div className="first_part" ref={containerRef}>
                    <button className="round-border-btn" type="button">
                      <BlackShareIcon />
                      Share
                    </button>
                    <button
                      className="rounded-border-btn px-3"
                      type="button"
                      onClick={() => toggleDropdown()}
                    >
                      <TripleDotsMenu />
                    </button>
                    {openIndex && (
                      <Dropdown.Menu
                        show
                        style={{
                          position: "absolute",
                          bottom: "125%",
                          right: 4,
                          zIndex: 1000,
                        }}
                      >
                        <Dropdown.Item className="dropdownlist" eventKey="2">
                          <OpenNewtabIcon /> <span>View Listing</span>
                        </Dropdown.Item>
                        <Dropdown.Item className="dropdownlist" eventKey="3">
                          <RenameIcon /> <span>Edit Listing</span>
                        </Dropdown.Item>
                        <Dropdown.Item className="dropdownlist" eventKey="4">
                          <DeleteDarkIcon /> <span>Delete Listing</span>
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    )}
                  </div>
                  <div className="second_part">
                    <button className="round-bluefill-btn" type="button">
                      End Listing
                    </button>
                    <button className="round-bluefill-btn" type="button">
                      Mark out of stock
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </CommonWhiteCard>
  );
}

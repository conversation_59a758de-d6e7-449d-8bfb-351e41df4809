"use client";
import { Col, Row, Dropdown } from "react-bootstrap";
import React, { useState, useEffect, useRef } from "react";
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import CommonWhiteCard from "@/Components/common/Account/CommonWhiteCard";
import {
  TripleDotsMenu,
  EyeDarkIcon,
  BlackShareIcon,
  StaticListingImg,
  DeleteDarkIcon,
  RenameIcon,
  PlusIcon,
  RelistIcon,
  DropArrowIcon,
  DropArrowUpIcon,
  SearchIcons,
} from "@/assets/svgIcons/SvgIcon";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/MarketPlace.scss";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";

export default function Yourlistings() {
  const [openIndex, setOpenIndex] = useState(false);
  const [isActiveExp, setIsActiveExp] = useState(true);
  const [isEndedExp, setIsEndedExp] = useState(true);
  const [isDraftExp, setIsDraftExp] = useState(true);
  const dropdownContainerRef = useRef(null);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownContainerRef.current &&
        !dropdownContainerRef.current.contains(event.target)
      ) {
        setOpenIndex(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    // setOpenIndex((prev) => (prev === index ? null : index));
    setOpenIndex((prev) => !prev);
  };
  const metaArray = {
    noindex: true,
    title: "Account Overview | TradeReply",
    description:
      "View your account overview on TradeReply.com. Access a summary of your account details, recent activity, and important notifications.",
    canonical_link: "https://www.tradereply.com/account/overview",
    og_site_name: "TradeReply",
    og_title: "Account Overview | TradeReply",
    og_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
    twitter_title: "Account Overview | TradeReply",
    twitter_description:
      "View your account overview on TradeReply. Access a summary of your account details, recent activity, and important notifications.",
  };

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_overview">
          <SidebarHeading title="Your Marketplace Listings" />
          <Link className="w-100" href="/account/create-listing">
            <CommonButton
              title="Create New Listing"
              onlyIcon={<PlusIcon />}
              className="w-100 mb-4"
            />
          </Link>
          <div className="account_search">
            <SearchIcons height={"24px"} width={"24px"} />
            <input type="text" placeholder="Search Your Listings" />
          </div>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <CommonWhiteCard
                title="Active Listings"
                Linkicon={
                  <div
                    className="account_card_btnArrow"
                    onClick={() => setIsActiveExp((prev) => !prev)}
                  >
                    {isActiveExp ? <DropArrowUpIcon /> : <DropArrowIcon />}
                  </div>
                }
                className="account_card"
              >
                {isActiveExp && (
                  <div className="account_card_dash_listings">
                    <div className="main_inform justify-between">
                      <p className="most_recent">15 Active Listings</p>
                      <span className="most_recent text-end">
                        Sorted: Newest to oldest
                      </span>
                    </div>
                    <div className="mini_card">
                      <div className="main_inform respon_sell_feedback">
                        <div className="activeListing_photo">
                          <StaticListingImg />
                        </div>
                        <div className="w-100">
                          <h6>Mastering the stock market</h6>
                          <p className="inner_price_text">
                            $11.95 - Listed on 1/19/25
                          </p>
                          <p className="inner_price_text">
                            65 clicks on listing since listed
                          </p>
                          <div
                            className="actions_btn"
                            ref={dropdownContainerRef}
                          >
                            <div className="first_part">
                              <button
                                className="round-border-btn"
                                type="button"
                              >
                                <BlackShareIcon />
                                Share
                              </button>
                              <button
                                className="rounded-border-btn px-3"
                                type="button"
                                onClick={() => toggleDropdown()}
                              >
                                <TripleDotsMenu />
                              </button>
                              {openIndex && (
                                <Dropdown.Menu
                                  show
                                  style={{
                                    position: "absolute",
                                    bottom: "125%",
                                    right: 4,
                                    zIndex: 1000,
                                  }}
                                >
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="2"
                                  >
                                    <EyeDarkIcon /> <span>View Listing</span>
                                  </Dropdown.Item>
                                  <Dropdown.Item
                                    eventKey="3"
                                    as={Link}
                                    href="/account/edit-listing/1"
                                    className="dropdownlist"
                                  >
                                    <RenameIcon /> <span>Edit Listing</span>
                                  </Dropdown.Item>
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="4"
                                  >
                                    <DeleteDarkIcon />{" "}
                                    <span>Delete Listing</span>
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              )}
                            </div>
                            <div className="second_part">
                              <button
                                className="round-bluefill-btn"
                                type="button"
                              >
                                End Listing
                              </button>
                              <button
                                className="round-bluefill-btn"
                                type="button"
                              >
                                Mark out of stock
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <CommonWhiteCard
                title="Draft Listings"
                Linkicon={
                  <div
                    className="account_card_btnArrow"
                    onClick={() => setIsEndedExp((prev) => !prev)}
                  >
                    {isEndedExp ? <DropArrowUpIcon /> : <DropArrowIcon />}
                  </div>
                }
                className="account_card"
              >
                {isEndedExp && (
                  <div className="account_card_dash_listings">
                    <div className="main_inform justify-between">
                      <p className="most_recent">15 Draft Listings</p>
                      <span className="most_recent text-end">
                        Sorted: Recently Edited
                      </span>
                    </div>
                    <div className="mini_card">
                      <div className="main_inform respon_sell_feedback">
                        <div className="activeListing_photo">
                          <StaticListingImg />
                        </div>
                        <div className="w-100">
                          <h6>Mastering the stock market</h6>
                          <p className="inner_price_text">
                            $11.95 - Listed on 1/19/25
                          </p>
                          <p className="inner_price_text">
                            65 clicks on listing since listed
                          </p>
                          <div className="actions_btn" ref={dropdownContainerRef}>
                            <div className="first_part">
                              <button
                                className="rounded-border-btn px-3"
                                type="button"
                                onClick={() => toggleDropdown()}
                              >
                                <TripleDotsMenu />
                              </button>
                              {openIndex && (
                                <Dropdown.Menu
                                  show
                                  style={{
                                    position: "absolute",
                                    bottom: "125%",
                                    right: 4,
                                    zIndex: 1000,
                                  }}
                                >
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="2"
                                  >
                                    <EyeDarkIcon /> <span>View Listing</span>
                                  </Dropdown.Item>
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="3"
                                  >
                                    <RenameIcon /> <span>Edit Listing</span>
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              )}
                            </div>
                            <div className="second_part">
                              <button
                                className="round-bluefill-btn"
                                type="button"
                              >
                                Edit Draft
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CommonWhiteCard>
            </Col>
          </Row>
          <Row className="mb-4 mb-lg-4">
            <Col className="d-flex mt-4 mt-md-0">
              <CommonWhiteCard
                title="Ended Listings"
                Linkicon={
                  <div
                    className="account_card_btnArrow"
                    onClick={() => setIsDraftExp((prev) => !prev)}
                  >
                    {isDraftExp ? <DropArrowUpIcon /> : <DropArrowIcon />}
                  </div>
                }
                className="account_card"
              >
                {isDraftExp && (
                  <div className="account_card_dash_listings">
                    <div className="main_inform justify-between">
                      <p className="most_recent">15 Ended Listings</p>
                      <span className="most_recent text-end">
                        Sorted: Recently Ended
                      </span>
                    </div>
                    <div className="mini_card">
                      <div className="main_inform respon_sell_feedback">
                        <div className="activeListing_photo">
                          <StaticListingImg />
                        </div>
                        <div className="w-100">
                          <h6>Mastering the stock market</h6>
                          <p className="inner_price_text">
                            $11.95 - Listed on 1/19/25
                          </p>
                          <p className="inner_price_text">
                            65 clicks on listing since listed
                          </p>
                          <div className="actions_btn" ref={dropdownContainerRef}>
                            <div className="first_part">
                              <button
                                className="round-border-btn"
                                type="button"
                              >
                                <BlackShareIcon />
                                Share
                              </button>
                              <button
                                className="rounded-border-btn px-3"
                                type="button"
                                onClick={() => toggleDropdown()}
                              >
                                <TripleDotsMenu />
                              </button>
                              {openIndex && (
                                <Dropdown.Menu
                                  show
                                  style={{
                                    position: "absolute",
                                    bottom: "125%",
                                    right: 4,
                                    zIndex: 1000,
                                  }}
                                >
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="2"
                                  >
                                    <EyeDarkIcon /> <span>View Listing</span>
                                  </Dropdown.Item>
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="3"
                                  >
                                    <RenameIcon /> <span>Edit Listing</span>
                                  </Dropdown.Item>
                                  <Dropdown.Item
                                    className="dropdownlist"
                                    eventKey="4"
                                  >
                                    <DeleteDarkIcon />{" "}
                                    <span>Delete Listing</span>
                                  </Dropdown.Item>
                                </Dropdown.Menu>
                              )}
                            </div>
                            <div className="second_part">
                              <button
                                className="round-border-btn"
                                type="button"
                              >
                                <RelistIcon />
                                Relist this item
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CommonWhiteCard>
            </Col>
          </Row>
        </div>
      </AccountLayout>
    </>
  );
}

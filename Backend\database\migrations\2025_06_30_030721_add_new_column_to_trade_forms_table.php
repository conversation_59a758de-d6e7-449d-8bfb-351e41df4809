<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trade_forms', function (Blueprint $table) {
            $table->boolean('is_published')->default(false)->after('index');
            $table->text('notes')->nullable()->after('is_published');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trade_forms', function (Blueprint $table) {
            $table->dropColumn(['is_published', 'notes']);
        });
    }
};

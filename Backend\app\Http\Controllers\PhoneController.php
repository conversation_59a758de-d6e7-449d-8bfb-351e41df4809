<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Traits\ApiResponseTrait;

class PhoneController extends Controller
{
    use ApiResponseTrait;

    /**
     * Add or update phone number (simplified - no verification required)
     */
    public function store(Request $request)
    {
        $request->validate([
            'phone_number' => 'required|string|max:20',
        ]);

        try {
            $user = Auth::user();

            // Format phone number (remove any non-digit characters except +)
            $phoneNumber = preg_replace('/[^\d+]/', '', $request->phone_number);

            // Basic phone number format validation
            if (strlen($phoneNumber) < 7 || strlen($phoneNumber) > 15) {
                return $this->errorResponse('Please enter a valid phone number.', 422);
            }

            // Check if phone number is already taken by another user
            $existingUser = User::where('phone_number', $phoneNumber)
                               ->where('id', '!=', $user->id)
                               ->first();

            if ($existingUser) {
                return $this->errorResponse('This phone number is already associated with another account.', 422);
            }

            // Directly save phone number to database
            $user->update([
                'phone_number' => $phoneNumber,
            ]);

            return $this->successResponse([
                'phone_number' => $this->maskPhoneNumber($phoneNumber),
            ], 'Phone number saved successfully');

        } catch (\Exception $e) {
            Log::error('Phone number setup failed: ' . $e->getMessage());
            return $this->errorResponse('Failed to save phone number. Please try again.', 500);
        }
    }



    /**
     * Remove phone number from user account
     */
    public function destroy()
    {
        try {
            $user = Auth::user();

            $user->update([
                'phone_number' => null,
                'phone_country_code' => null,
                'phone_verified_at' => null,
            ]);

            return $this->successResponse([], 'Phone number removed successfully');

        } catch (\Exception $e) {
            Log::error('Phone number removal failed: ' . $e->getMessage());
            return $this->errorResponse('Failed to remove phone number. Please try again.', 500);
        }
    }

    /**
     * Mask phone number for display
     * Shows only last 2 digits: ********01
     */
    private function maskPhoneNumber($phoneNumber)
    {
        if (!$phoneNumber || strlen($phoneNumber) < 2) {
            return $phoneNumber;
        }

        $cleaned = preg_replace('/[^\d]/', '', $phoneNumber);
        if (strlen($cleaned) < 2) {
            return $phoneNumber;
        }

        $lastTwoDigits = substr($cleaned, -2);
        $starsCount = max(strlen($cleaned) - 2, 8);

        return str_repeat('*', $starsCount) . $lastTwoDigits;
    }
}

'use client';

import { Col, Row } from "react-bootstrap";
import React, { useEffect, useState } from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import CommonTable from "@/Components/UI/CommonTable";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import CommonSearch from "@/Components/UI/CommonSearch";
import { RemoveIconSvg, PlusIconSvg, SolidInfoIcon, RightSolidArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import { get } from "@/utils/apiUtils";
import Link from "next/link";

export default function BrokersIntegrations() {

    const [Providers, setProviders] = useState([]);
    const [filterProviders, setFilterProviders] = useState([]);
    const [brokerSearch, setBrokerSearch] = useState([]);
    const [searchKeyword, setSearchKeyword] = useState('');

    const handleSearchChange = (keyword) => {
        setBrokerSearch(keyword);
    };

    useEffect(() => {
        const fetchBrokers = async () => {
            try {
                const response = await get('/thirdpartyprovider', { key: searchKeyword });
                setProviders(response.data.providers);
                setFilterProviders(response.data.providers);
            } catch (error) {
                console.error('Failed to fetch brokers:', error);
                setProviders([]);
            }
        };
        fetchBrokers();
    }, [searchKeyword]);

    useEffect(() => {
        if (!brokerSearch) {
            setFilterProviders(Providers);
            return;
        }

        const results = GetSearchedBrokers(brokerSearch);
        setFilterProviders(results);
    }, [brokerSearch]);


    const GetSearchedBrokers = (brokerSearch) => {
        return Providers.filter((provider) =>
            provider.name.toLowerCase().includes(brokerSearch.toLowerCase())
        );
    };
    return (
        <>
            <div className="brokers_tablesec_inner">
                <CommonTooltip
                    className="d-flex align-items-center mb-3 justify-content-end"
                    content="Manual entry lets you input trading data using a form builder or upload via Template or Broker CSV files. AutoSync connects directly to your broker via API, automatically updating your data in real-time with limited manual effort."
                    position="top-left"
                >
                    <SolidInfoIcon />
                    <h6 className="mb-0 ms-3 yellow_text">Sync Options: Manual vs. AutoSync</h6>
                </CommonTooltip>
            </div>
            <Col xs={12} className="connetionTable">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Brokers & Integrations</h6>
                            <p>Connect your TradeReply.com account to your brokers and trading platforms to automatically sync your trades, ensuring all your trading data is seamlessly integrated for comprehensive analysis and performance tracking.</p>
                            <div className="common_blackcard_innerheader_search my-3">
                                <CommonSearch
                                    icon={true}
                                    placeholder="Search for Broker or Integration"
                                    onChange={handleSearchChange}
                                />
                            </div>
                            <div className="submit-request">
                                <h6>Missing an integration?
                                    <Link
                                        href="/help"
                                        target="_blank"
                                        className="d-inline-flex align-items-center submit_request px-2"
                                    >
                                        Submit a Request{" "}
                                        <span className="ms-2">
                                            <RightSolidArrowIconSvg />
                                        </span>
                                    </Link>
                                </h6>
                            </div>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <div className="common_blackcard_innerheader_tradeacct">
                                <h6>Trade Account</h6>
                                <p>acct-123</p>
                            </div>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_table">
                            <CommonTable fields={false} className="simple_table">
                                <tr>
                                    <td>Interactive Brokers</td>
                                    <td>
                                        <p className="darkgrey_text font-extrabold">https://www.interactivebrokers.com/</p>
                                        <p >Interactive Brokers Account ID 3292423 Synced</p>
                                        <span className="darkgrey_text font-extrabold">Supported:</span> AutoSync, Stocks, Crypto
                                    </td>
                                    <td colSpan={2}>
                                        <div className="flex justify-end">
                                            <button className="blue_text_btn d-flex align-items-center">
                                                <RemoveIconSvg color="svg-baseblue" /> Remove
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Coinbase</td>
                                    <td>
                                        <p className="darkgrey_text font-extrabold">https://www.coinbase.com/</p>
                                        <p >Coinbase Account ID 342421 Synced
                                        </p>
                                        <span className="darkgrey_text font-extrabold">Supported:</span> AutoSync, Stocks, Crypto
                                    </td>
                                    <td colSpan={2}>
                                        <div className="flex justify-end">
                                            <button className="blue_text_btn d-flex align-items-center">
                                                <RemoveIconSvg color="svg-baseblue" /> Remove
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {filterProviders?.map((item, index) => (
                                    <tr key={index}>
                                        <td>{item?.name}</td>
                                        <td>{item?.status ? "Connected" : "Not Connected"}</td>
                                        <td>
                                            <p className="darkgrey_text font-weight-600">
                                                {item?.link}
                                            </p>
                                            <span className="darkgrey_text font-extrabold">Supported:</span>{" "}
                                            {item?.crypto ? "Crypto " : ""}
                                            {item?.auto_sync ? "AutoSync " : ""}
                                            {item?.stocks ? "Stocks " : ""}
                                        </td>
                                        <td >
                                            <div className="flex justify-end">
                                                {item?.status ? (
                                                    <button className="blue_text_btn d-flex align-items-center">
                                                        <RemoveIconSvg color="svg-baseblue" /> Remove
                                                    </button>
                                                ) : (
                                                    <button className="blue_text_btn d-flex align-items-center">
                                                        <PlusIconSvg color="svg-baseblue" /> Connect
                                                    </button>
                                                )}
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </CommonTable>
                        </div>
                    </div>
                </div>
                {/* <CommonBlackCard
                    title="Brokers & Integrations"
                    text="Connect your TradeReply.com account to your brokers and trading platforms to automatically sync your trades, ensuring all your trading data is seamlessly integrated for comprehensive analysis and performance tracking."
                    className="account_card"
                    searcbar={true}
                    tradeacct={true}
                    searchPlaceholder="Search for Broker or Integration"
                    onSearchChange={handleSearchChange}
                >
                </CommonBlackCard> */}
            </Col>
        </>
    )
}

<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Cashier\Subscription;

class SubscriptionService
{
    /**
     * Create a new subscription for the user.
     */
    public function create($paymentMethodId, $planId)
    {
        $user = Auth::user();

        try {
            $subscription = $user->newSubscription('default', $planId)
                ->create($paymentMethodId);

            return [
                'message' => 'Subscription created successfully.',
                'subscription' => $subscription
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Cancel the user's subscription and prevent future charges.
     */
    public function cancel()
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');

        if ($subscription && $subscription->valid()) {
            $subscription->cancel();
            return ['message' => 'Your subscription is canceled but remains active until the end of the billing period.'];
        }

        return ['error' => 'No active subscription found.'];
    }

    /**
     * Downgrade the user's subscription, effective next billing cycle.
     */
    public function downgrade($newPlanId)
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');

        if (!$subscription || !$subscription->valid()) {
            return ['error' => 'No active subscription found.'];
        }

        // Schedule the downgrade for the next billing cycle
        $subscription->swap($newPlanId);

        return ['message' => 'Your plan will be downgraded at the end of the current billing period.'];
    }

    /**
     * Upgrade the user's subscription, effective immediately.
     */
    public function upgrade($newPlanId)
    {
        $user = Auth::user();
        $subscription = $user->subscription('default');

        if (!$subscription || !$subscription->valid()) {
            return ['error' => 'No active subscription found.'];
        }

        // Upgrade the plan immediately and generate a new invoice
        $subscription->swapAndInvoice($newPlanId);

        return ['message' => 'Your plan has been upgraded successfully.'];
    }


    /**
     * Get the subscription for the authenticated user.
     */
    public function getSubscription(User $user)
    {
        return $user->subscription;
    }

    /**
     * Check if the user has an active subscription.
     */
    public function hasActiveSubscription(User $user): bool
    {
        return $user->subscription && now()->lessThan($user->subscription->expires_at);
    }

    /**
     * Assign a new subscription to the user.
     */
    public function assignSubscription(User $user, $subscriptionId)
    {
        $user->subscription_id = $subscriptionId;
        $user->save();
    }

    /**
     * Remove the user's subscription (Cancel).
     */
    public function cancelSubscription(User $user)
    {
        $user->subscription_id = null;
        $user->save();
    }

    /**
     * Check if the user has a specific subscription plan.
     */
    public function hasSubscriptionPlan(User $user, $planName): bool
    {
        return $user->subscription && $user->subscription->name === $planName;
    }
}

@use "../theme/var" as *;


.trade_manager_entrylist {
    margin-top: 20px;

    .trade_manager_table {
        width: 100%;
        border-collapse: collapse;

        thead {
            background-color: #002f6c;
            color: white;

            th {
                padding: 12px;
                text-align: left;
                font-weight: bold;
                cursor: pointer;
            }
        }

        tbody {
            background: white;

            p {
                color: #000;
            }

            tr {
                border-bottom: 1px solid #ddd;

                &:hover {
                    background: #f5f5f5;
                }
            }

            td {
                padding: 12px;
                color: #333;
                max-width: 600px;
                overflow-wrap: break-word;
            }

            input[type="checkbox"] {
                cursor: pointer;
            }
        }
    }
}
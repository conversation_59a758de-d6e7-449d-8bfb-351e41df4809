import { Accordion } from "react-bootstrap";
import CommonHeading from "@/Components/UI/CommonHeading";
import "../../../css/Home/BrokerToggle.css";
import { CheckIcon, CrossIcon, GreyCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { useEffect, useState } from "react";


const BrokersToggle = ({ providers }) => {
  const [Providers, setProviders] = useState([]);
  useEffect(() => {
    setProviders(providers);
  }, [providers]);

  return (
    <>
      <div className="tableless broker-table">
        <div className="faq_card mb-0">
          <div className="faq_card_accordion mt-0">
            <div className="broker-head fs-6 fw-bold">
              <p>Broker / Integration</p>
            </div>
            <div className="scroll-table">
              <Accordion defaultActiveKey="0">
                {Providers?.map((item) => (
                  <Accordion.Item eventKey={item.id} key={item?.id}>
                    <Accordion.Header><p className="w-full text-center  font-14 fw-bold text-sec">{item?.name}</p></Accordion.Header>
                    <Accordion.Body>
                      <p className="w-full text-center font-14 fw-bold text-sec"> {item?.url ? item?.url : ""} </p>
                      <div className="d-flex mt-4 align-items-center justify-content-between w-full">
                        <span className="d-flex align-items-center justify-content-between flex-column gap-2 font-14 fw-bold text-sec">{item?.stocks ? <CheckIcon /> : <GreyCrossIcon />} Stock</span>
                        <span className="d-flex align-items-center justify-content-between flex-column gap-2 font-14 fw-bold text-sec">{item?.crypto ? <CheckIcon /> : <GreyCrossIcon />} Crypto</span>
                        <span className="d-flex align-items-center justify-content-between flex-column gap-2 font-14 fw-bold text-sec">{item?.auto_sync ? <CheckIcon /> : <GreyCrossIcon />} Auto Sync</span>
                      </div>
                    </Accordion.Body>
                  </Accordion.Item>
                ))}
              </Accordion>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BrokersToggle;
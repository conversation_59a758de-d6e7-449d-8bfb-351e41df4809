import React, { useState, useEffect } from "react";
import CommonButton from "@/Components/UI/CommonButton";

export default function DeleteAll() {
  const [isModal, setIsModal] = useState(false);

  return (
    <div className="innerCard">
      <div className="cardHeading">
        <div className="whiteCircle"></div>
        <p>Delete All Data</p>
      </div>
      <div className="portfolio_manager_card_deleteAll">
        <p className="section_text">
          This will permanently delete all deposits, withdrawals, trade entries,
          and performance metrics associated with “[Trade Account Name]”.
        </p>
        <p className="section_text">
          Your saved reports, dashboards & settings, tags, strategies, broker
          API connections, and all Trade Account Details (such as currency,
          timezone, and formatting preferences) will remain intact.
        </p>
        <p className="section_text">This action cannot be undone.</p>
        <CommonButton
          title="Delete All Data"
          onClick={() => setIsModal(true)}
          className="red-btn me-2"
        />
        {isModal && (
          <div className="deleteAllModal_overlay">
            <div className="deleteAll_modal">
              <p>
                Are you sure you want to delete all data in “[Trade Account
                Name]”?
              </p>
              <CommonButton
                title="Cancel"
                className="white-btn me-2"
                onClick={() => setIsModal(false)}
              />
              <CommonButton
                title="Delete All Data"
                className="red-btn"
                onClick={() => setIsModal(false)}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

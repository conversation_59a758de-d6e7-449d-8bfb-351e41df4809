'use client';
import React, { useState, useEffect } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg, PlusIconSvg, CheckIcon, RemoveIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';
import { get } from '@/utils/apiUtils';
import { maskFullName, maskAddress } from '@/utils/addressMask';

export default function AddressBook() {
    const [addresses, setAddresses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchAddresses = async () => {
        try {
            setLoading(true);

            setError(null);

            const controller = new AbortController();
            const response = await get('/addresses', {}, { signal: controller.signal });

            if (response.success && response.data) {
                setAddresses(response.data);
            } else {
                throw new Error(response.message || 'Failed to fetch addresses');
            }
        } catch (err) {
            console.error('Error fetching addresses:', err);
            setError(err.message || 'Failed to load addresses');
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        fetchAddresses();
    }, []);

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <div className="account_header_main">
                                <h6>Address Book</h6>
                            </div>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href="/account/address/setup" prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <PlusIconSvg />
                                    <span className="ms-2">Add New Address</span>
                                </button>
                            </Link>
                        </div>
                    </div>

                    <div className="common_blackcard_innerbody">
                        {loading ? (
                            <div className="text-center py-4">
                                <p>Loading addresses...</p>
                            </div>
                        ) : error ? (
                            <div className="text-center py-4">
                                <p className="text-danger">{error}</p>
                                <button
                                    onClick={fetchAddresses}
                                    className="btn btn-sm btn-outline-primary mt-2"
                                >
                                    Try Again
                                </button>
                            </div>
                        ) : addresses.length === 0 ? (
                            <div className="text-center py-4">
                                <p>No addresses found. Add your first address to get started.</p>
                            </div>
                        ) : (
                            <div className="account_card_list new-address-section">
                                <ul>
                                    {addresses.map((addr) => (
                                        <li key={addr.id}>
                                            <Col xs={12} md={3}>
                                                {addr.is_default ? (
                                                    <div className="d-flex align-items-center gap-2">
                                                        <CheckIcon />
                                                        <span className="green_text">Default</span>
                                                    </div>
                                                ) : (
                                                    <Link href={`/account/address/manage?id=${addr.id}`} prefetch={true}>
                                                        <button className="btn btn-link p-0 set-default-btn">
                                                            <span className='text_00ADEF'>Set as Default</span>
                                                        </button>
                                                    </Link>
                                                )}
                                            </Col>
                                            <Col xs={12} md={9}>
                                                <div className='d-flex justify-content-between align-items-center w-100'>
                                                    <div className='show-address-details'>
                                                        <p className='name'>{maskFullName(addr.full_name)}</p>
                                                        <p className='address'>{maskAddress(addr.address)}</p>
                                                        <p className='city'>{addr.city}, {addr.state}, {addr.zip_code}</p>
                                                    </div>

                                                    <div className='btns d-flex gap-2'>
                                                        {!addr.is_default && (
                                                            <>
                                                                <Link href={`/account/address/manage`} prefetch={true}>
                                                                    <button className="d-flex align-items-center">
                                                                        <RemoveIconSvg />
                                                                        <span className="ms-1">Remove</span>
                                                                    </button>
                                                                </Link>
                                                                <Link href={`/account/address/manage`} prefetch={true}>
                                                                    <button className="d-flex align-items-center">
                                                                        <EditIconSvg />
                                                                        <span className="ms-1">Edit</span>
                                                                    </button>
                                                                </Link>
                                                            </>
                                                        )}

                                                        {addr.is_default && (
                                                            <Link href={`/account/address/manage`} prefetch={true}>
                                                                <button className="d-flex align-items-center">
                                                                    <EditIconSvg />
                                                                    <span className="ms-1">Edit</span>
                                                                </button>
                                                            </Link>
                                                        )}
                                                    </div>
                                                </div>
                                            </Col>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                </div>
            </Col>
        </>
    );
}

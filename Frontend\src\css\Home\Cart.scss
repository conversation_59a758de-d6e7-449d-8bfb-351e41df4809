@use "../theme/var";

.cart {
    padding: 4rem 0;
    position: relative;

    .cartContainer {
        background-color: #F2F2F2;
        padding: 40px 50px;
        border-radius: 30px;

        &_title {
            font-size: 36px;
            font-weight: 700;
            color: var.$clr031940;
        }

        &_itemsbox {
            margin-top: 20px;
            background-color: #fff;
            border: 1px solid #00000033;
            width: 100%;
            border-radius: 10px;

            &_title {
                background-color: var.$clr031940;
                padding: 10px;
                border-radius: 10px 10px 0 0;
                display: flex;
                justify-content: space-between;

                p {
                    font-size: 18px;
                    font-weight: 400;
                }
            }

            &_inner {
                padding: 20px;

                &_box {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;

                    p {
                        color: #000;
                        font-weight: 600;
                        margin-bottom: 5px;
                    }

                    p:last-child {
                        margin-bottom: 0px !important;
                    }

                    .itemImg {
                        height: 120px;
                        width: 120px;
                        object-fit: cover;
                    }

                    .itemName {
                        font-size: 18px;
                    }

                    .itemFormat,
                    .itemDuration {
                        color: var.$borderclr;

                        span {
                            color: #000;
                        }
                    }

                    .itemLicense {
                        display: flex;
                        align-items: center;
                        gap: 5px;

                        p {
                            margin-bottom: 0 !important;
                        }
                    }

                    &_right {
                        display: flex;
                        flex-direction: column;
                        align-items: end;

                        p {
                            margin-bottom: 0 !important;
                        }

                        button {
                            color: var.$baseclr;
                            text-decoration: underline;
                            font-weight: bold;
                        }

                    }
                }

                &_box:last-child {
                    margin-bottom: 0 !important;
                }

            }

            &_right {
                &_top {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    gap: 10px;

                    p {
                        font-size: 14px;
                        font-weight: 600;
                        margin-top: 10px;
                    }

                    .border-gray {
                        border-top: 3px solid #808080;
                    }
                }

                &_inner {
                    margin-top: 25px;
                    background-color: #FFFFFF;
                    border: 1px solid #00000033;
                    padding: 20px;
                    border-radius: 15px;
                    font-weight: 600;

                    p {
                        font-size: 18px;
                        color: #000;
                        font-weight: 600;
                    }

                    .orderSummary,
                    .tax {
                        display: flex;
                        justify-content: space-between;
                        padding-bottom: 20px;
                        border-bottom: 1px solid #00000033;
                    }

                    .subtotal {
                        display: flex;
                        justify-content: space-between;
                        padding-top: 10px;
                        padding-bottom: 5px;

                        p {
                            font-size: 16px;
                        }

                        span {
                            color: var.$borderclr;
                        }
                    }

                    .tax {
                        padding-bottom: 10px;

                        p {
                            font-size: 16px;
                        }

                        span {
                            color: var.$borderclr;
                        }
                    }

                    .orderTotal {
                        display: flex;
                        justify-content: space-between;
                        padding-top: 10px;
                    }
                }

                a {
                    text-decoration: underline;
                    font-size: 18px;
                    font-weight: 600;
                }
            }
        }
    }

}

.cartLogOut {
    display: flex;
    align-items: center;
    gap: 15px;

    &_image {
        height: 300px;
    }

    .heading {
        font-size: 36px;
        font-weight: 700;
        color: var.$clr031940;
    }

    .subHeading {
        font-size: 15px;
        font-weight: 600;
        color: #04498C;
    }
}


@media only screen and (width <=550px) {
    .cart {
        padding: 2rem 0;
    }

    .cart .cartContainer {
        padding: 20px 10px;
    }

    .cart .cartContainer_title {
        font-size: 28px !important;
    }

    .cart .cartContainer_itemsbox_inner_box {
        display: block;
    }

    .cartContainer_itemsbox_inner_box_right {
        flex-direction: row !important;
        justify-content: space-between;
    }

    .cartLogOut {
        .heading {
            font-size: 28px !important;
        }

        .subHeading {
            font-size: 15px;
        }

        button {
            font-size: 14px !important;
        }
    }
}

@media only screen and (width <=992px) {
    .cartLogOut {
        flex-direction: column;

        &_text {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
    }
}

.cart_steps {
    width: 55px;
    height: 55px;
    background-color: #fff;
    border-radius: 50%;
    border: 2.5px solid;
    display: flex;
    justify-content: center;
}
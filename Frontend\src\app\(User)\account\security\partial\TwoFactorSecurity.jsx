"use client";

import React, { useState, useEffect } from "react";
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { useRouter } from "next/navigation";
import { EditIconSvg, CheckIcon, CrossIcon } from "@/assets/svgIcons/SvgIcon";
import { get2FAStatus } from "@/utils/apiUtils";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { SolidInfoIcon } from "@/assets/svgIcons/SvgIcon";

const TwoFactorSecurity = () => {
  const router = useRouter();
  const [is2FAEnabled, setIs2FAEnabled] = useState(false);
  const [isAlwaysRequired, setIsAlwaysRequired] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch 2FA status from backend
  const fetch2FAStatus = async () => {
    try {
      setIsLoading(true);
      const response = await get2FAStatus();

      if (response.success && response.data) {
        setIs2FAEnabled(response.data.two_factor_enabled || false);
        setIsAlwaysRequired(response.data.two_factor_always_required || false);
      }
    } catch (error) {
      console.error("Error fetching 2FA status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetch2FAStatus();
  }, []);

  return (
    <CommonBlackCard
      title="2-Step Verification"
      text="Prevent unauthorized logins by setting up 2-step email verification."
      link="/account/security/two-factor?from=/account/security"
      Linktext="Update"
      editicon={<EditIconSvg />}
      className="account_card pullcontent"
    >
      <div className="account_card_list ">
        <ul>
          <li>
            <span>Status </span>
            {isLoading ? (
              <div className="d-flex align-items-center">
                <img
                  src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                  alt="Loading"
                  width={20}
                  height={20}
                />
                <span className="fw-semibold ms-3" style={{ color: "#00adef" }}>
                  Loading...
                </span>
              </div>
            ) : (
              <div className="d-flex align-items-center">
                {is2FAEnabled ? (
                  <>
                    <CheckIcon />
                    <span className="green_text ms-3">Active</span>
                  </>
                ) : (
                  <>
                    <img
                      src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-failure.svg"
                      alt="Inactive"
                      width={20}
                      height={20}
                    />
                    <span
                      className="fw-semibold ms-3"
                      style={{ color: "#9c9a9f" }}
                    >
                      Inactive
                    </span>
                  </>
                )}
              </div>
            )}
          </li>
          <li>
            {is2FAEnabled ? (
              <>
                <span>Restore Code </span>{" "}
                <CommonTooltip
                  className="mt-1"
                  content={
                    <>
                      <p className="mb-2 text-justify">
                        This code allows you to regain access to your account if you lose your login credentials or can’t complete two-factor verification. Generating a new code will replace your current one and make the old code invalid.
                      </p>
                    </>
                  }
                  position="top-right"
                >
                  <SolidInfoIcon />
                </CommonTooltip>
                <button
                  className="restore_code add_number ml-2"
                  type="button"
                  onClick={() =>
                    router.push(
                      "/account/security/two-factor?from=/account/security"
                    )
                  }
                >
                  Get Restore Code
                </button>
              </>
            ) : (
              <>
                <span>Restore Code </span>{" "}
                <CommonTooltip
                  className="mt-1"
                  content={
                    <>
                      <p className="mb-2 text-justify">
                        This code allows you to regain access to your account if you lose your login credentials or can’t complete two-factor verification. Generating a new code will replace your current one and make the old code invalid.
                      </p>
                    </>
                  }
                  position="top-right"
                >
                  <SolidInfoIcon />
                </CommonTooltip>
                <button
                  className="restore_code add_number  ml-2 text-[#00adef]"
                  type="button"
                >
                  Enable 2FV to unlock restore code
                </button>
              </>
            )}
          </li>
        </ul>

        <div className="divider"></div>

        <div className="step_verification d-flex align-items-center justify-content-between">
          <div className="step_verification_content">
            <h6>Always Require 2-step verification for login</h6>
            <p className="mt-2">
              If enabled, 2-step verification will be prompted on every login.
            </p>
            <p>
              If disabled, your device will be remembered for 30 days and will
              not prompt you until the period is over.
            </p>
          </div>
          {isLoading ? (
            <div className="d-flex align-items-center">
              <img
                src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-saving.svg"
                alt="Loading"
                width={20}
                height={20}
              />
              <span className="fw-semibold ms-3" style={{ color: "#00adef" }}>
                Loading...
              </span>
            </div>
          ) : (
            <div className="step_verification_off d-flex align-items-center">
              {is2FAEnabled && isAlwaysRequired ? (
                <>
                  <CheckIcon />
                  <span className="green_text ms-2">On</span>
                </>
              ) : (
                <>
                  <CrossIcon />
                  <span className="ms-2">Off</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </CommonBlackCard>
  );
};

export default TwoFactorSecurity;

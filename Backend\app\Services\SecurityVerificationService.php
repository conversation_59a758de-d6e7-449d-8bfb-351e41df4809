<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SecurityVerificationService
{
    /**
     * Generate a signed and optionally encrypted cookie value
     */
    public function generateSecureCookieValue(array $payload): string
    {
        // Add timestamp and nonce for security
        $payload['timestamp'] = now()->toISOString();
        $payload['nonce'] = Str::random(16);

        $jsonPayload = json_encode($payload);

        // Note: Encryption disabled for simplicity - using signing only

        // Always sign the payload for security
        $signature = $this->generateSignature($jsonPayload);
        $securePayload = [
            'data' => $jsonPayload,
            'signature' => $signature
        ];
        return base64_encode(json_encode($securePayload));
    }

    /**
     * Validate and decode a secure cookie value
     */
    public function validateSecureCookieValue(string $cookieValue): ?array
    {
        try {
            $decoded = json_decode(base64_decode($cookieValue), true);

            if (!$decoded) {
                return null;
            }

            // Verify signature (always required)
            if (!isset($decoded['data']) || !isset($decoded['signature'])) {
                return null;
            }

            // Verify signature
            if (!$this->verifySignature($decoded['data'], $decoded['signature'])) {
                return null;
            }

            $payload = $decoded['data'];

            // Note: Decryption not needed since encryption is disabled

            $data = json_decode($payload, true);

            if (!$data || !isset($data['timestamp'])) {
                return null;
            }

            // Check expiration
            $timestamp = Carbon::parse($data['timestamp']);
            $expiresMinutes = config('security.cookie.expires_minutes', 1);

            if ($timestamp->addMinutes($expiresMinutes)->isPast()) {
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            \Log::warning('Failed to validate security cookie', [
                'error' => $e->getMessage(),
                'cookie_value' => substr($cookieValue, 0, 50) . '...'
            ]);
            return null;
        }
    }

    /**
     * Generate cryptographic signature for payload
     */
    private function generateSignature(string $payload): string
    {
        $key = config('app.key');
        $algorithm = config('security.crypto.signing_algorithm');

        return hash_hmac($algorithm, $payload, $key);
    }

    /**
     * Verify cryptographic signature
     */
    private function verifySignature(string $payload, string $signature): bool
    {
        $expectedSignature = $this->generateSignature($payload);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get dynamic cookie configuration based on environment
     */
    public function getCookieConfig(): array
    {
        $config = config('security.cookie');

        // Auto-detect domain and secure flag
        $config['domain'] = $this->detectCookieDomain();
        $config['secure'] = $this->shouldUseSecureCookies();

        return $config;
    }

    /**
     * Detect appropriate cookie domain based on current request
     */
    private function detectCookieDomain(): ?string
    {
        // Remove domain-specific logic to ensure consistent behavior across environments
        // Always return null to avoid domain-related cookie issues
        return null;
    }

    /**
     * Determine if secure cookies should be used
     */
    private function shouldUseSecureCookies(): bool
    {
        // Check if secure flag is explicitly set in config
        $configSecure = config('security.cookie.secure');
        if ($configSecure !== null) {
            return (bool) $configSecure;
        }

        if (!request()) {
            return false;
        }

        // Simplified logic: Use secure cookies for HTTPS, non-secure for HTTP
        // Remove environment-specific domain checking to ensure consistent behavior
        return request()->isSecure();
    }

    /**
     * Check if user has exceeded verification attempts
     */
    public function hasExceededAttempts(int $userId): bool
    {
        return \App\Models\SecurityVerificationAttempt::hasUserExceededGlobalRateLimit($userId);
    }

    /**
     * Record a failed verification attempt
     */
    public function recordFailedAttempt(int $userId): void
    {
        // The SecurityVerificationAttempt model handles this through the recordAttempt method
        // This method is kept for backward compatibility but the actual recording
        // is now done in the controller when creating SecurityVerificationAttempt records
    }

    /**
     * Clear failed attempts for a user (called on successful verification)
     */
    public function clearFailedAttempts(int $userId): void
    {
        // With database-based approach, we don't need to explicitly clear attempts
        // The rate limiting is based on time windows, so this method is kept
        // for backward compatibility but doesn't need to do anything
    }
}

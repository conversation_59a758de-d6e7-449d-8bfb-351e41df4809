# Backend Requirements for Super Admin Enhancements

This document outlines the backend changes needed to fully implement the super admin page enhancements that are currently implemented as frontend-only solutions.

## 1. Scope Summary Fields Implementation

### Database Schema Changes

#### 1.1 Articles Table Migration
Create a new migration to add scope summary fields to the `articles` table:

```php
// File: database/migrations/YYYY_MM_DD_HHMMSS_add_scope_summaries_to_articles_table.php

Schema::table('articles', function (Blueprint $table) {
    $table->text('transaction_scope_summary')->nullable()->after('summary');
    $table->text('trade_scope_summary')->nullable()->after('transaction_scope_summary');
    $table->text('portfolio_scope_summary')->nullable()->after('trade_scope_summary');
});
```

### Model Updates

#### 1.2 Article Model
Update the `Article` model to include new fields in the fillable array:

```php
// File: app/Models/Article.php

protected $fillable = [
    'title', 
    'content', 
    'slug', 
    'type', 
    'feature_image',
    'primary_category_id', 
    'is_featured',
    'summary',
    'transaction_scope_summary',  // Add this
    'trade_scope_summary',        // Add this
    'portfolio_scope_summary',    // Add this
    'body', 
    'keywords'
];
```

### API Endpoints

#### 1.3 Scope Summaries Endpoint
Create a new API endpoint to fetch scope summaries from field definitions:

```php
// File: app/Http/Controllers/Admin/ArticleController.php

/**
 * Get scope summaries for Transaction, Trade, and Portfolio
 */
public function getScopeSummaries()
{
    try {
        // Get summaries from field definitions or use defaults
        $transactionSummary = TransactionFieldDefinition::where('database_field', 'LIKE', '%TRANSACTION%')
            ->whereNotNull('summary')
            ->first();
        
        $tradeSummary = TradeFieldDefinition::where('database_field', 'LIKE', '%TRADE%')
            ->whereNotNull('summary')
            ->first();
            
        $portfolioSummary = PortfolioFieldDefinition::where('database_field', 'LIKE', '%PORTFOLIO%')
            ->whereNotNull('summary')
            ->first();

        return response()->json([
            'success' => true,
            'message' => 'Scope summaries retrieved successfully',
            'data' => [
                'transaction_summary' => $transactionSummary?->summary ?? 'Default transaction summary',
                'trade_summary' => $tradeSummary?->summary ?? 'Default trade summary',
                'portfolio_summary' => $portfolioSummary?->summary ?? 'Default portfolio summary',
            ],
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error retrieving scope summaries',
            'error' => $e->getMessage(),
        ], 500);
    }
}
```

#### 1.4 Route Registration
Add the new route to the API routes:

```php
// File: routes/api.php

Route::prefix('super-admin/articles')->group(function () {
    Route::get('/list/{type}', [ArticleController::class, 'articlesList'])->where(['type' => 'blog|education']);
    Route::get('/create', [ArticleController::class, 'create']);
    Route::get('/scope-summaries', [ArticleController::class, 'getScopeSummaries']); // Add this
    Route::post('/store', [ArticleController::class, 'store']);
    Route::put('/{article}', [ArticleController::class, 'update']);
    Route::delete('/{id}', [ArticleController::class, 'destroy'])->where('id', '[0-9]+');
    Route::get('/{type}/{article}/edit', [ArticleController::class, 'edit'])->where(['type' => 'blog|education']);
});
```

### Controller Updates

#### 1.5 ArticleController Store Method
Update the store method to handle new scope summary fields:

```php
// File: app/Http/Controllers/Admin/ArticleController.php

$data = $request->only([
    'title',
    'content',
    'summary',
    'transaction_scope_summary',  // Add this
    'trade_scope_summary',        // Add this
    'portfolio_scope_summary',    // Add this
    'type',
    'is_featured',
    'feature_image',
    'primary_category_id',
    'secondary_categories',
]);
```

#### 1.6 ArticleController Update Method
Update the update method to handle new scope summary fields:

```php
// File: app/Http/Controllers/Admin/ArticleController.php

$data = $request->only([
    'title',
    'content',
    'summary',
    'transaction_scope_summary',  // Add this
    'trade_scope_summary',        // Add this
    'portfolio_scope_summary',    // Add this
    'primary_category_id',
    'type',
    'is_featured',
    'feature_image',
    'secondary_categories',
]);
```

### Resource Updates

#### 1.7 ArticleResource
Update the ArticleResource to include new fields in API responses:

```php
// File: app/Http/Resources/ArticleResource.php

return [
    'id' => $this->id,
    'title' => $this->title ?? '',
    'content' => $this->content ?? '',
    'slug' => $this->slug ?? '',
    'type' => $this->type ?? '',
    'feature_image' => $this->feature_image ?? '',
    'is_featured' => (bool) $this->is_featured,
    'summary' => $this->summary ?? '',
    'transaction_scope_summary' => $this->transaction_scope_summary ?? '',  // Add this
    'trade_scope_summary' => $this->trade_scope_summary ?? '',              // Add this
    'portfolio_scope_summary' => $this->portfolio_scope_summary ?? '',      // Add this
    'primary_category' => new CategoryResource($this->whenLoaded('primaryCategory')),
    'secondary_categories' => CategoryResource::collection($this->whenLoaded('secondaryCategories')),
    'created_at' => $this->created_at,
    'updated_at' => $this->updated_at,
];
```

## 2. Search Functionality Implementation

### Model Updates

#### 2.1 Article Model AdminList Method
Update the AdminList method to support search and sorting:

```php
// File: app/Models/Article.php

public function AdminList($type, $searchKey = null, $perPage = 10)
{
   $query = self::with(['primaryCategory:id,title'])
        ->ofType($type);
        
   // Add search functionality
   if (!empty($searchKey)) {
       $query->where(function ($q) use ($searchKey) {
           $q->where('title', 'LIKE', "%{$searchKey}%")
             ->orWhere('summary', 'LIKE', "%{$searchKey}%");
       });
   }
   
   // Default alphabetical sorting by title (name)
   return $query->orderBy('title', 'asc')->paginate($perPage);
}
```

#### 2.2 Category Model getCategoryList Method
Update the getCategoryList method to support search and sorting:

```php
// File: app/Models/Category.php

public static function getCategoryList($perPage = 10, $searchKey = null)
{
    $query = self::withCount([
        'primaryArticles as primary_count',
        'secondaryArticles as secondary_count'
    ]);
    
    // Add search functionality
    if (!empty($searchKey)) {
        $query->where(function ($q) use ($searchKey) {
            $q->where('title', 'LIKE', "%{$searchKey}%")
              ->orWhere('content', 'LIKE', "%{$searchKey}%");
        });
    }
    
    // Default alphabetical sorting by title (name)
    return $query->orderBy('title', 'asc')->paginate($perPage);
}
```

### Controller Updates

#### 2.3 ArticleController articlesList Method
Update to accept search parameters:

```php
// File: app/Http/Controllers/Admin/ArticleController.php

public function articlesList($type, Request $request)
{
    $type = strtolower($type);
    $searchKey = $request->input('search', null);
    $perPage = $request->input('per_page', 10);
    
    $Articles = (new Article())->AdminList($type, $searchKey, $perPage);

    return response()->json([
        'success' => true,
        'message' => ucfirst($type) . 'Articles List',
        'data' => ArticleResource::collection($Articles),
        'pagination' => [
            'total' => $Articles->lastPage(),
            'count' => $Articles->count(),
            'per_page' => $Articles->perPage(),
            'current_page' => $Articles->currentPage(),
            'next_page_url' => $Articles->nextPageUrl(),
            'prev_page_url' => $Articles->previousPageUrl(),
        ],
    ]);
}
```

#### 2.4 CategoryController categoriesList Method
Update to accept search parameters:

```php
// File: app/Http/Controllers/CategoryController.php

public function categoriesList(Request $request)
{
    $searchKey = $request->input('search', null);
    $perPage = $request->input('per_page', 10);
    
    $categories = Category::getCategoryList($perPage, $searchKey);

    return response()->json([
        'success' => true,
        'message' => 'Categories List',
        'data' => CategoryResource::collection($categories),
        'pagination' => [
            'total' => $categories->lastPage(),
            'count' => $categories->count(),
            'per_page' => $categories->perPage(),
            'current_page' => $categories->currentPage(),
            'next_page_url' => $categories->nextPageUrl(),
            'prev_page_url' => $categories->previousPageUrl(),
        ],
    ]);
}
```

## 3. Implementation Priority

1. **High Priority**: Scope summary fields implementation (database, model, controller updates)
2. **Medium Priority**: Search functionality implementation
3. **Low Priority**: Additional sorting options and advanced search features

## 4. Testing Requirements

- Unit tests for new API endpoints
- Integration tests for search functionality
- Database migration testing
- Frontend integration testing with new backend endpoints

## 5. Deployment Considerations

- Run database migrations in production
- Update API documentation
- Ensure backward compatibility during deployment
- Monitor performance impact of new search queries

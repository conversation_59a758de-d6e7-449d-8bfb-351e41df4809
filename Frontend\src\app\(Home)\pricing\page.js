import { cookies } from "next/headers";
import { Suspense } from "react";
import PricingClient from "./PricingClient";

export default async function Page() {
    const cookieStore = await cookies();
    const loginToken = cookieStore.get("authToken") || null;

    const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/stripe/plans`, {
        cache: "no-store",
    });

    const { data: plans } = await res.json();
    const defaultIsMonthly = false;

    let activeSubscription = null;
    let nextPlan = null;
    let previousPlan = null;

    if (loginToken) {
        try {
            const subRes = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/active/subscription`, {
                headers: {
                    Authorization: `Bearer ${loginToken.value}`,
                    Accept: "application/json",
                },
                cache: "no-store",
            });
            const subJson = await subRes.json();
            activeSubscription = subJson?.data || null;
            nextPlan = subJson?.next_plan || null;
            previousPlan = subJson?.previous_plan || null;
        } catch (err) {
            console.error("Failed to fetch active subscription:", err);
        }
    }
    const filteredPlans = plans.filter(plan => {
        const isCorrectBilling = plan.billing_type === (defaultIsMonthly ? "monthly" : "yearly");
        const isFreePlan = plan.billing_type === "free";

        if (loginToken) {
            return isFreePlan || isCorrectBilling;
        } else {
            return isCorrectBilling;
        }
    });
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <PricingClient
                plans={plans}
                billingPlans={filteredPlans}
                defaultIsMonthly={defaultIsMonthly}
                activeSubscription={activeSubscription}
                authToken={loginToken}
                nextPlan={nextPlan}
                previousPlan={previousPlan}
            />
        </Suspense>
    );
}

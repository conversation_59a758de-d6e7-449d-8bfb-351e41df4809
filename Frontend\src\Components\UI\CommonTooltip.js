'use client';

import { useState, useRef, useEffect, useCallback } from "react";
import { createPortal } from "react-dom";
import "../../css/common/CommonTooltip.scss";

const CommonTooltip = ({
  children,
  content,
  position = "top-right",
  className = ""
}) => {
  const [visible, setVisible] = useState(false);
  const [tooltipStyle, setTooltipStyle] = useState({});
  const wrapperRef = useRef(null);
  const tooltipRef = useRef(null);

  const updateTooltipPosition = useCallback(() => {
    if (!wrapperRef.current || !tooltipRef.current) return;

    const triggerRect = wrapperRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();

    const scrollY = window.scrollY || window.pageYOffset;
    const scrollX = window.scrollX || window.pageXOffset;

    let top = triggerRect.top + scrollY - tooltipRect.height - 8;
    let left = triggerRect.left + scrollX;

    switch (position) {
      case "top-right":
        left += triggerRect.width - tooltipRect.width;
        break;
      case "bottom-left":
        top = triggerRect.bottom + scrollY + 8;
        break;
      case "bottom-right":
        top = triggerRect.bottom + scrollY + 8;
        left += triggerRect.width - tooltipRect.width;
        break;
      case "center-top":
        left += triggerRect.width / 2 - tooltipRect.width / 2;
        break;
      case "center-bottom":
        top = triggerRect.bottom + scrollY + 8;
        left += triggerRect.width / 2 - tooltipRect.width / 2;
        break;
      default:
        break;
    }

    setTooltipStyle({
      position: "absolute",
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 9999,
    });
  }, [position]);

  useEffect(() => {
    if (visible) {
      updateTooltipPosition();

      window.addEventListener("scroll", updateTooltipPosition, true); // true captures bubbling scroll
      window.addEventListener("resize", updateTooltipPosition);

      return () => {
        window.removeEventListener("scroll", updateTooltipPosition, true);
        window.removeEventListener("resize", updateTooltipPosition);
      };
    }
  }, [visible, updateTooltipPosition]);

  return (
    <div
      className={`tooltip-wrapper ${className}`}
      onMouseEnter={() => setVisible(true)}
      onMouseLeave={() => setVisible(false)}
      onFocus={() => setVisible(true)}
      onBlur={() => setVisible(false)}
      ref={wrapperRef}
      style={{ display: "inline-block" }}
    >
      {children}
      {visible &&
        createPortal(
          <div
            ref={tooltipRef}
            className="tooltip-box"
            style={tooltipStyle}
          >
            {content}
          </div>,
          document.body
        )}
    </div>
  );
};

export default CommonTooltip;

'use client';
import React, { useState, useEffect } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';
import { maskEmail } from '@/utils/emailMask';
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { get } from '@/utils/apiUtils';
import { usePathname } from 'next/navigation';


export default function Email() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);
    const pathname = usePathname();

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const controller = new AbortController();
            const response = await get('/account', {}, { signal: controller.signal });

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Always fetch fresh data first to ensure we have the latest from DB
        fetchUserData();

        // Also check for cached data as fallback
        const storedUser = localStorage.getItem('user');

        if (reduxUser) {
            // Use Redux data if available, but still fetch fresh data
            setUserData(reduxUser);
            setLoading(false);
        } else if (storedUser) {
            // Use localStorage data as immediate fallback while API loads
            try {
                const parsedUser = JSON.parse(storedUser);
                setUserData(parsedUser);
                // Update Redux store
                dispatch(setUser(parsedUser));
                setLoading(false);
            } catch (err) {
                console.error('Error parsing stored user data:', err);
            }
        }
    }, []); // Empty dependency array to run only on mount

    const getDisplayEmail = () => {
        if (!userData?.email) return 'Not set';
        return maskEmail(userData.email);
    };

    if (loading) {
        return (
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Email</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href={`/account/email/setup?from=${encodeURIComponent(pathname)}`} prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <EditIconSvg />
                                    <span className="ms-2">Update</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Email </span>
                                    </Col>
                                    <Col xs={12} md={3}>
                                        <span>Loading...</span>
                                    </Col>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col>
        );
    }

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Email</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href={`/account/email/setup?from=${encodeURIComponent(pathname)}`} prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <EditIconSvg />
                                    <span className="ms-2">Update</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Email </span>
                                    </Col>
                                    <Col xs={12} md={3}>
                                        <span>{getDisplayEmail()}</span>
                                    </Col>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col >
        </>
    )
}

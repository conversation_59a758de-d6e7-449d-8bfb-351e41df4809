<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\ArticleResource;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\SitemapCategoryResource;
use App\Models\Article;
use App\Models\Category;
use App\Models\User;
use App\Models\ArticleProgress;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ArticleController extends Controller
{
 use ApiResponseTrait;
    public function index(Request $request, $page = null)
    {
        $sidebar = $request->boolean('sidebar', false);

        $currentPage = $page ?? $request->query('page', 1);
        $perPage = $request->query('per_page', 10);
        $key = $request->input('key', null);
        $type = $request->input('type', null);
        $sortByClicks = $request->query('sort_by_clicks', false);
        $canShowNextArticlesSorting = $request->input('is_next_article_sorting', false);
        $currentArticleId = $request->input('current_article_id', null);
        $userId = $request->input('user_id', null);

        $articles = Article::getPaginatedData($type, $perPage, $key, $currentPage, $sortByClicks, $canShowNextArticlesSorting, $currentArticleId);

        $isSidebar = $sidebar && $perPage == 8;
        $isSidebarFilled = false;

        if ($isSidebar) {
            $allArticles = Article::query()
                ->where('type', $type)
                ->orderBy('title', 'ASC')
                ->get();

            $currentIndex = $allArticles->search(fn($a) => $a->id == $currentArticleId);
            $total = $allArticles->count();

            $sidebarArticles = collect();
            $i = 1;

            // Start collecting the next articles (wrap around if needed)
            while ($sidebarArticles->count() < 8 && $i < $total) {
                $nextIndex = ($currentIndex + $i) % $total;
                $candidate = $allArticles[$nextIndex];

                if ($candidate->id !== $currentArticleId) {
                    $sidebarArticles->push($candidate);
                }

                $i++;
            }

            $articles = $sidebarArticles;
            $isSidebarFilled = true;
        }



        $canonicalUrl = url("/{$type}/page/{$currentPage}");
        $topBlogs = ($type === 'blog') ? Article::getTopBlogs($type) : null;
        $averageProgress = ($type !== 'blog') ? Article::averageProgressEducation() : null;

        $progressMap = [];
        if ($userId) {
            $progressMap = ArticleProgress::where('user_id', $userId)->pluck('progress', 'article_id');
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . ' data retrieved successfully',
            'data' => [
                'education' => ArticleResource::collection($articles)->map(function ($article) use ($progressMap) {
                    $article->user_progress = $progressMap[$article->id] ?? 0;
                    return new ArticleResource($article);
                }),
                'averageProgress' => $averageProgress,
                'latest_blogs' => ArticleResource::collection($articles),
                'top_blogs' => $type === 'blog' ? ArticleResource::collection($topBlogs) : [],
                'meta' => $isSidebarFilled ? [
                    'current_page' => 1,
                    'next_page' => null,
                    'prev_page' => null,
                    'total' => 1,
                    'total_records' => $articles->count(),
                    'per_page' => 8,
                    'canonical_url' => null,
                ] : [
                    'current_page' => $articles->currentPage(),
                    'next_page' => $articles->nextPageUrl(),
                    'prev_page' => $articles->previousPageUrl(),
                    'total' => $articles->lastPage(),
                    'total_records' => $articles->total(),
                    'per_page' => $articles->perPage(),
                    'canonical_url' => $canonicalUrl,
                ]
            ]
        ]);
    }



    /**
     * Article listing resource in storage.
     */
    public function articlesList($type)
    {
        $type = strtolower($type);
        $Articles = (new Article())->AdminList($type);

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . 'Articles List',
            'data' => ArticleResource::collection($Articles),
            'pagination' => [
                'total' => $Articles->lastPage(),
                'count' => $Articles->count(),
                'per_page' => $Articles->perPage(),
                'current_page' => $Articles->currentPage(),
                'next_page_url' => $Articles->nextPageUrl(),
                'prev_page_url' => $Articles->previousPageUrl(),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create()
    {
        $categories = Category::select('id', 'title','slug','database_field')->get();

        return response()->json([
            'success' => true,
            'message' => 'Categories data retrieved successfully',
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->only([
                'title',
                'content',
                'summary',
                'type',
                'is_featured',
                'feature_image',
                'primary_category_id',
                'secondary_categories',
            ]);

            $article = Article::createArticle($data);

            return response()->json([
                'success' => true,
                'message' => ucfirst($data['type']) . ' article added successfully.',
                'imported_records' => new ArticleResource($article),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }



    /**
     * Display the specified resource.
     */
    public function show($type, $slug, Request $request)
    {
        $article =Article::getTypeSlug($slug.'-'.$type,true);
        $userId = $request->input('user_id', null);

        if(!$article) {
            return $this->errorResponse('Article not found', 400);
        }

        $article->clicks += 1;
        $article->save();

        $progressRecord = ArticleProgress::query()->where('article_id', $article->id)->first();
        $progress = $progressRecord->progress ?? 0;

        $avgProgress = 0;
        if ($userId) {
            $totalArticles = Article::where('type', 'education')->count();
            $userProgressSum = ArticleProgress::where('user_id', $userId)->sum('progress');
            $avgProgress = round($userProgressSum / $totalArticles);
        }

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . ' article data',
            'data' => new ArticleResource($article),
            'progress' => $progress,
            'avgProgress' => $avgProgress,
            'next_article' => $article->getNextArticle() ? new ArticleResource($article->getNextArticle()) : null,
        ]);
    }

    /**
     * Show the specified resource for update.
     */
    public function edit($type,$slug)
    {
        $article =  Article::getTypeSlug($slug.'-'.$type);
        if ($article->type !== $type) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid article type',
            ], 404);
        }

        $categories = Category::all();

        return response()->json([
            'success' => true,
            'message' => ucfirst($type) . ' article data',
            'data' => [
                'categories' => $categories,
                'article' => $article,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request,$slug)
    {
        $article =  Article::getTypeSlug($slug.'-'.$request->type);

        $data = $request->only([
            'title',
            'content',
            'summary',
            'primary_category_id',
            'type',
            'is_featured',
            'feature_image',
            'secondary_categories',
        ]);

        $article = $article->updateArticle($data);

        return response()->json([
            'success' => true,
            'message' => ucfirst($data['type']) . ' article updated successfully.',
            'updated_record' => new ArticleResource($article),
        ]);
    }

    /**
     * Delete the specified resource in storage.
     */
    public function destroy($id)
    {
        $article = Article::find($id);
        if ($article->deleteArticle()) {
            return response()->json([
                'success' => true,
                'message' => 'Article deleted successfully.',
                'data' => '',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to delete article.',
        ], 500);
    }

    /**
     * Calculate the progress of article readibility
     */
    public function updateProgress(Request $request, $slug)
    {
        $article = Article::getTypeSlug($slug . '-education');

        $request->validate([
            'progress' => 'required|integer|min:0|max:100',
        ]);

        $user = auth()->user();

        try {
            DB::beginTransaction();

            $user->educationProgress()->syncWithoutDetaching([
                $article->id => ['progress' => $request->progress]
            ]);

            DB::commit();

            return response()->json([
                'message' => 'Progress updated successfully',
                'progress' => $request->progress
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Something went wrong while updating progress.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function blogSitemap()
    {
        $categories = (new Category())->blogSiteMap();
        $resource = SitemapCategoryResource::collection($categories);

        return $this->successResponse([
            'data'       => $resource,

        ], 'Sitemap Blogs of Categories', 200);
    }



    public function featured_resource()
    {
        $featured = Article::where('type','education')->where('is_featured', true)->first();

        if (!$featured) {
            return response()->json([
                'success' => false,
                'message' => 'No featured resource found.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' =>new ArticleResource($featured),
        ]);

    }

}

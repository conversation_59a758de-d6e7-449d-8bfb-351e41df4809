import React, { useEffect, useState } from "react";
import {
  DeleteIcon,
  SolidInfoIcon,
  AddPlusIcon,
  DragDropIcon,
  DropArrowIcon,
  PinIcon,
} from "@/assets/svgIcons/SvgIcon";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { useDroppable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import ConfigScopeModal from "@/Components/common/TradeAnalysis/ConfigScopeModal";
import { useSortable } from "@dnd-kit/sortable";
import {
  SortableContext,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

const SortableItem = ({ item, from, onDelete, overId, activeId }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.id,
    data: {
      from,
      item,
    },
  });

  const handleDeleteClick = (e) => {
    e.stopPropagation();
    e.preventDefault();
    onDelete();
  };
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: isDragging ? "grabbing" : "grab",
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 100 : 1,
    backgroundColor:
      !isDragging && overId === item.id && activeId !== item.id
        ? "#172d55" : undefined,
    marginTop:
      !isDragging && overId === item.id && activeId !== item.id
        ? "15px" : undefined,
    marginBottom:
      !isDragging && overId === item.id && activeId !== item.id
        ? "15px" : undefined,
    padding: !isDragging && overId === item.id && activeId !== item.id ? "6px 10px" : undefined,
    borderRadius: !isDragging && overId === item.id && activeId !== item.id ? "8px" : undefined,
  };

  return (
    <>
      <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
        <div className="include_trades">
          <div className="head">
            <DragDropIcon />
            <div>
              <div className="d-flex align-items-center gap-2">
                <h6>{item.title}</h6>
                <CommonTooltip
                  className="CustomTooltip"
                  content="💡 These are portfolio-level settings."
                  position="top-right"
                >
                  <SolidInfoIcon height={"20px"} width={"20px"} />
                </CommonTooltip>
              </div>
              <p>Scope: Trade</p>
            </div>
          </div>
          <button
            className="p-2 pe-1"
            onClick={handleDeleteClick}
            onPointerDown={(e) => e.stopPropagation()}
          >
            <DeleteIcon />
          </button>
        </div>
      </div>
    </>
  );
};

const DropZone = ({
  id,
  items,
  from,
  onDelete,
  placeholder,
  openModal,
  overId,
  activeId,
}) => {

  const { setNodeRef, isOver } = useDroppable({
    id,
    data: {
      from: id,
    },
  });

  return (
    <div>
      <SortableContext
        items={items.map((i) => i.id)}
        strategy={verticalListSortingStrategy}
      >
        {items.map((item, index) => (
          <SortableItem
            key={item.id}
            item={item}
            from={from}
            overId={overId}
            activeId={activeId}
            onDelete={() => onDelete(index)}
          />
        ))}
      </SortableContext>
      <div
        ref={setNodeRef}
        style={{
          backgroundColor: isOver ? "#172d55" : "transparent",
          transition: "background-color 0.2s ease",
        }}
      >
        <div className="empty-state" onClick={openModal}>
          <PinIcon />
          <p>{placeholder}</p>
        </div>
      </div>
    </div>
  );
};
export default function LayoutTradeDetails({
  rows,
  columns,
  values,
  deleteRow,
  deleteColumn,
  deleteValue,
  overId,
  activeId,
}) {
  const [isFilterCollapse, setFilterCollapse] = useState(true);
  const [isRowsModal, setIsRowsModal] = useState(false);
  const [isColumnsModal, setIsColumnsModal] = useState(false);
  const [isValuesModal, setIsValuesModal] = useState(false);
  const collapseFilter = () => {
    setFilterCollapse((prev) => !prev);
  };
  const rowsModal = () => {
    setIsRowsModal((prev) => !prev);
  };
  const columnsModal = () => {
    setIsColumnsModal((prev) => !prev);
  };
  const valuesModal = () => {
    setIsValuesModal((prev) => !prev);
  };
  return (
    <>
      <div className="heading">
        <h2>Layout</h2>
        <CommonTooltip
          className="CustomTooltip"
          content="💡 Organize how your data appears in the table by placing dimensions into rows and columns, and metrics into values. This defines the structure of your analysis."
          position="top-right"
        >
          <SolidInfoIcon />
        </CommonTooltip>
        <span className="collapse_main" onClick={collapseFilter}>
          <DropArrowIcon />
        </span>
      </div>
      {isFilterCollapse && (
        <div>
          {/* Rows Section */}
          <div className="innerCard">
            <div className="cardHeading">
              <p>Rows</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Row</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={rowsModal}
                >
                  <p className="count_add">({rows.length}/10)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_row">
                <DropZone
                  id="rows"
                  items={rows}
                  placeholder="Drop or select dimensions"
                  from="rows"
                  openModal={rowsModal}
                  overId={overId}
                  activeId={activeId}
                  onDelete={(i) => {
                    const updated = [...rows];
                    updated.splice(i, 1);
                    deleteRow(updated);
                  }}
                />
                <div className="pagination_row">
                  <p>Show Rows</p>
                  <div className="value_pagination">
                    <select className="custom-select">
                      {[...Array(10)].map((_, i) => (
                        <option key={i + 1} value={i + 1}>
                          {i + 1}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                {isRowsModal && <ConfigScopeModal title="Rows" toggleModal={rowsModal} />}
              </div>
            </div>
          </div>

          {/* Columns Section - Updated to match Rows */}
          <div className="innerCard">
            <div className="cardHeading">
              <p>Columns</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Column</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={columnsModal}
                >
                  <p className="count_add">({columns.length}/10)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_row">
                <DropZone
                  id="columns"
                  items={columns}
                  placeholder="Drop or select dimensions"
                  from="columns"
                  openModal={columnsModal}
                  overId={overId}
                  onDelete={(i) => {
                    const updated = [...columns];
                    updated.splice(i, 1);
                    deleteColumn(updated);
                  }}
                />
                <div className="pagination_row">
                  <p>Show Rows</p>
                  <div className="value_pagination">
                    <select className="custom-select">
                      {[...Array(10)].map((_, i) => (
                        <option key={i + 1} value={i + 1}>
                          {i + 1}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                {isColumnsModal && (
                  <ConfigScopeModal
                    title="Columns"
                    toggleModal={columnsModal}
                  />
                )}
              </div>
            </div>
          </div>

          {/* Values Section */}
          <div className="innerCard">
            <div className="cardHeading">
              <p>Values</p>
            </div>
            <div className="trade_analysis_configure">
              <div className="configure_add">
                <div className="d-flex gap-2">
                  <div className="dashed_design"></div>
                  <h6 className="header_title">Add Value</h6>
                </div>
                <div
                  className="d-flex gap-2 cursor-pointer"
                  onClick={valuesModal}
                >
                  <p className="count_add">({values.length}/10)</p>
                  <AddPlusIcon />
                </div>
              </div>
              <div className="trade_analysis_row">
                <DropZone
                  id="values"
                  items={values}
                  placeholder="Drop or select metrics"
                  from="values"
                  overId={overId}
                  activeId={activeId}
                  openModal={valuesModal}
                  onDelete={(index) => {
                    const updated = [...values];
                    updated.splice(index, 1);
                    deleteValue(updated);
                  }}
                />
                {isValuesModal && (
                  <ConfigScopeModal title="Values" toggleModal={valuesModal} />
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
